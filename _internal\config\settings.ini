# PlexMovieAutomator/config/settings.ini
# This is the central configuration file for the entire pipeline.
# Fill in your user-specific paths and API keys below.

[General]
# SQLite database for reliable state management (replaces JSON files)
sqlite_database_path = _internal/data/pipeline_state.db
# Legacy JSON paths (kept for backward compatibility during transition)
movies_to_process_json_path = _internal/config/movies_to_process.json
pipeline_state_file = _internal/config/pipeline_state.json
# Path to the master list for original language hints (used by 03_mkv_processor.py)
movies_master_list_path = _internal/config/movies_master_list.txt
# Path to the input file for new movie requests.
new_movie_requests_file = new_movie_requests.txt
# A list of valid movie file extensions to look for in completed downloads.
movie_file_extensions = [".mkv", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mpg", ".mpeg", ".m4v", ".3gp", ".ts"]

[Database]
# SQLite database configuration
# Enable automatic filesystem synchronization at script startup
auto_sync_filesystem = true
# Enable self-healing validation
auto_heal_discrepancies = true
# Database backup settings
backup_enabled = true
backup_retention_days = 30
backup_directory = _internal/backups
# Health monitoring settings
health_check_enabled = true
health_alert_threshold = 80

[Paths]
# --- Workspace Paths ---
# These are the main working directories for the pipeline.
# They are relative to the project root directory.
intake_pending_dir = workspace/0_new_movie_requests
nzb_files_for_download_dir = %(intake_pending_dir)s/nzb_files_for_download
nzb_files_launched_archive_dir = %(intake_pending_dir)s/nzb_files_launched_archive

download_client_active_dir = workspace/1_downloading
download_incomplete_dir = %(download_client_active_dir)s/incomplete
download_complete_raw_dir = %(download_client_active_dir)s/complete_raw

mkv_processing_output_dir = workspace/2_downloaded_and_organized
subtitles_finalized_ready_dir = workspace/5_ready_for_final_mux
poster_pending_dir = workspace/6_awaiting_poster
quality_check_pending_dir = workspace/7_awaiting_quality_check

archive_processed_dir = workspace/archive_processed
archive_original_pgs_sup_dir = %(archive_processed_dir)s/original_pgs_sup_files

# Plex media directory (where movies are organized for Plex)
plex_movies_directory = C:/Users/<USER>/Videos/plex

issues_hold_dir = workspace/issues_hold
issues_metadata_failed_dir = %(issues_hold_dir)s/metadata_failed
issues_download_failed_dir = %(issues_hold_dir)s/download_failed
issues_mkv_processing_failed_dir = %(issues_hold_dir)s/mkv_processing_failed
issues_subtitle_ocr_failed_dir = %(issues_hold_dir)s/subtitle_ocr_failed
issues_consolidation_error_dir = %(issues_hold_dir)s/consolidation_error
issues_manual_review_dir = %(issues_hold_dir)s/manual_review_needed

[Executables]
# --- Paths to external command-line tools and drivers ---
# Ensure these paths are correct for your system, or that the executables are in your system's PATH.
chrome_driver_path = C:/Program Files/ChromeDriver/chromedriver.exe
mkvmerge_path = C:/Program Files/MKVToolNix/mkvmerge.exe
mkvpropedit_path = C:/Program Files/MKVToolNix/mkvpropedit.exe
mkvextract_path = C:/Program Files/MKVToolNix/mkvextract.exe
ffprobe_path = C:/ffmpeg/bin/ffprobe.exe
# Path to BDSup2Sub.jar (for NGC Models OCR)
bdsup2sub_jar_path = _internal/tools/bdsup2sub.jar
# Path to pgsrip executable (if not in PATH)
pgsrip_path = pgsrip

[APIKeys]
# Your API Keys. It is recommended to use environment variables for these in a production setup.
tmdb_api_key = efd5018b7283ecb6fa1c0c525783bc08
# Path to your Google Cloud credentials JSON file (for Google Vision OCR)
google_vision_credentials_path = C:/path/to/your/google_cloud_credentials.json
# Your OpenAI key (if used for future enhancements)
# openai_api_key = sk-proj-sN3hH...

[Radarr]
# Radarr API configuration (replaces Chrome driver NZB scraping)
url = http://localhost:7878
api_key = 6ba9b679124f4414b3c4b93756decab8
# Quality profile ID for downloads (6 = HD - 720p/1080p)
quality_profile_id = 6
# Year-based quality profile IDs for advanced quality selection
# HD profile for 1080p downloads (≤2009 movies and 2010-2015 1080p)
hd_quality_profile_id = 4
# Ultra-HD profile for 4K downloads (2010-2015 4K and 2016+ movies)
uhd_quality_profile_id = 5

[IntakeAndNZBSearch]
nzbfinder_url = https://nzbfinder.ws/
# Your Chrome profile that has login information for nzbfinder.ws
chrome_user_data_dir = C:\Users\<USER>\AppData\Local\Google\Chrome\User Data
chrome_profile_name = Profile 1
# Set to 'true' to attempt automatic download of the top NZB result.
# Set to 'false' to have the script open the search page for manual selection.
auto_download_nzb = true
search_bar_xpath = //*[@id='search-sidebar']
sort_by_size_xpath = //a[contains(@href, 'ob=size_desc') and contains(., 'Size')]
top_result_download_xpath = (//a[contains(@href, 'getnzb')])[1]


[DownloadAndOrganize]
# 'NewshostingLauncher' uses os.startfile on Windows.
# 'SABnzbdAPI' uses SABnzbd API for direct integration.
# Future options: 'NZBGetAPI', 'JDownloaderAPI'
downloader_client_type = SABnzbdAPI
# Path to the executable for the Newshosting client (used for logging/checking if running)
newshosting_exe_path = C:\Users\<USER>\AppData\Local\Newshosting\3.7.6\newshosting.exe
# SABnzbd API configuration
sabnzbd_url = http://127.0.0.1:8080
sabnzbd_api_key = 91f5bd2c786d4886bd0a1072856719c9
# Parameters for monitoring system activity to pause processing
disk_busy_threshold_mbps = 10
network_busy_threshold_mbps = 5
download_check_interval_seconds = 60
max_wait_for_download_hours = 12

[MKVProcessor]
# Language preferences for track selection
audio_preferred_lang = eng
subtitle_preferred_lang = eng

[SubtitleHandler]
# OCR service to use for PGS/SUP subtitles. Options: imagesorcery, pgsrip, google_vision, none
# imagesorcery: Enhanced NVIDIA NGC Models OCR with zero costs (recommended)
# pgsrip: Simple fallback tool
# google_vision: Legacy Google Vision API (deprecated, costs money)
ocr_service = imagesorcery
# Set to 'true' to attempt to convert ASS/SSA subtitles to SRT using ffmpeg.
convert_ass_to_srt = true

[PosterAndQCPrep]
# Set to 'true' to automatically download a poster from TMDb.
automate_poster_download = true
# Standard filename for the downloaded poster.
poster_filename = poster.jpg
# Poster size to download from TMDb. Options: w92, w154, w185, w342, w500, w780, original
poster_size = w500

[Orchestrator]
# How often the main pipeline script should run a full cycle, in seconds (for continuous mode).
pipeline_interval_seconds = 300
# Path for the orchestrator's specific log file.
orchestrator_log_path = logs/pipeline_orchestrator.log

[Logging]
# Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
file_log_level = INFO
console_log_level = INFO
log_rotation_max_bytes = 10485760 # 10MB
log_rotation_backup_count = 5
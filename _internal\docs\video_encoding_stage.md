# Video Encoding Stage Documentation

## Overview

The video encoding stage (Stage 4) of the media processing pipeline compresses MKV files using HandBrakeCLI with H.265/HEVC encoding. This stage runs after initial MKV processing (Stage 3) and before final muxing (Stage 5).

## Features

- **Resolution-based encoding presets** (1080p/4K)
- **Dynamic bitrate calculation** with quality floors
- **Interactive user confirmation** with parameter override options
- **Robust error handling** and state management
- **SQLite database integration** for pipeline state tracking
- **Audio passthrough preservation**

## Files Created

### Core Scripts
- `04_video_encoder.py` - Main video encoding script
- `setup_video_encoding.py` - Setup and management utility  
- `test_video_encoder.py` - Test suite for verification

### Configuration
- `_internal/config/video_encoder_config.json` - Encoding settings and parameters

## Prerequisites

1. **HandBrakeCLI**: Must be installed at `_internal/tools/HandBrakeCLI.exe`
2. **Pipeline Database**: SQLite database with movie state information
3. **Input Files**: Movies in "final_mux_pending" status from Stage 3
4. **Directory Structure**: Workspace folders properly set up

## Quick Start

### 1. Test Installation
```powershell
python test_video_encoder.py
```

### 2. Setup Encoding Stage
```powershell
python setup_video_encoding.py
```
This will:
- Check HandBrake installation
- Show movies ready for encoding
- Update movie statuses if needed
- Create output directories

### 3. Run Video Encoding
```powershell
# Interactive mode (recommended)
python 04_video_encoder.py

# Auto-confirm mode (for batch processing)
python 04_video_encoder.py --auto-confirm

# Dry run to see what would be done
python 04_video_encoder.py --dry-run
```

## Encoding Parameters

### Default Settings
- **Video Codec**: H.265 (x265)
- **Encoding Speed**: slow (good quality/speed balance)
- **Profile/Level**: Main @ 5.1
- **Audio**: Passthrough (preserves original quality)
- **Mode**: 2-pass average bitrate encoding

### Bitrate Calculation
- **1080p**: Minimum 9 Mbps, typically 50% of original bitrate
- **4K**: Default 20 Mbps
- **Algorithm**: `max(original_bitrate * 0.5, minimum_floor)`

### Resolution Detection
- **1080p**: Uses "H.265 MKV 1080p30" preset
- **4K**: Uses "H.265 MKV 2160p60 4K" preset

## User Interaction

For each movie, the script shows:
```
Ready to encode: The Matrix (1999) [1080p]
Input file: The Matrix (1999).mkv
File size: 25.3 GB
Duration: 2.3 hours

Proposed encoding settings:
  • Video codec: H.265 (x265), preset: slow
  • Target bitrate: 11500 kbps (11.5 Mbps)
  • Encoder profile: main, level: 5.1
  • Two-pass encoding: Yes (turbo first pass)
  • Audio: pass-through (source audio preserved)

Proceed with these settings? (Y)es/(E)dit/(S)kip/(Q)uit:
```

### Override Options
If you choose "Edit", you can modify:
- Target bitrate (kbps)
- Encoder speed preset (ultrafast/fast/medium/slow/slower/veryslow)
- Turbo first pass (on/off)
- Encoder profile and level

## Output

### File Locations
Encoded files are placed in:
```
workspace/4_ready_for_final_mux/
├── 1080p/
│   └── Movie Title (Year)/
│       └── Movie Title (Year).mkv
└── 4k/
    └── Movie Title (Year)/
        └── Movie Title (Year).mkv
```

### Database Updates
- Status changed to `final_mux_pending`
- File paths updated to point to encoded file
- Timestamps and processing metadata recorded

## Error Handling

### Common Issues
1. **HandBrakeCLI not found**: Check `_internal/tools/HandBrakeCLI.exe` exists
2. **Input file missing**: Verify Stage 3 completed successfully
3. **Encoding failed**: Check logs in `_internal/logs/video_encoder.log`
4. **Database errors**: Ensure pipeline database is accessible

### Recovery
- Failed movies marked as `error_video_encoding`
- Original files always preserved
- Can retry with different settings
- Database maintains complete state history

## Configuration

Edit `_internal/config/video_encoder_config.json` to customize:

```json
{
  "encoding_settings": {
    "min_bitrate_1080p_kbps": 9000,
    "min_bitrate_4k_kbps": 20000,
    "compression_ratio": 0.5,
    "default_preset": "slow"
  }
}
```

## Logging

Logs are written to:
- `_internal/logs/video_encoder.log` - Detailed processing log
- Console output - Progress and user interaction

## Integration with Pipeline

### Database Statuses
- **Input**: `video_encoding_pending`
- **Active**: `video_encoding_active` 
- **Success**: `final_mux_pending`
- **Error**: `error_video_encoding`

### File Flow
```
Stage 3 → processed_va_mkv_path → [ENCODING] → 4_ready_for_final_mux → Stage 5
```

## Performance Notes

- **Encoding Speed**: "slow" preset provides good quality/time balance
- **Two-pass encoding**: Ensures optimal bitrate distribution
- **Turbo first pass**: Speeds up analysis with minimal quality impact
- **Sequential processing**: One movie at a time to avoid resource contention

## Quality Assurance

- Minimum bitrate floors prevent over-compression
- Output file verification checks size and existence
- Original files preserved until final quality check
- User can override any parameter if needed

## Troubleshooting

### HandBrake Issues
```powershell
# Test HandBrake directly
_internal\tools\HandBrakeCLI.exe --version

# Check encoding capabilities
_internal\tools\HandBrakeCLI.exe --help
```

### Database Issues
```powershell
# Check database status
python setup_video_encoding.py
# Choose option 4 to show encoding status
```

### File Permission Issues
- Ensure write access to workspace directory
- Check antivirus not blocking HandBrakeCLI
- Verify sufficient disk space for output

## Next Steps

After successful encoding:
1. Files are in `4_ready_for_final_mux/`
2. Database status is `final_mux_pending`
3. Ready for Stage 5 (final muxing)
4. Original files preserved for quality verification

The encoding stage ensures all videos are compressed to manageable sizes while maintaining high quality, preparing them for the final muxing stage where subtitles and metadata will be added.

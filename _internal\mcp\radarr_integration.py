#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/radarr_integration.py

Radarr MCP Server Integration
Provides movie automation capabilities through Radarr API for enhanced NZB search and download management.
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

class RadarrMCP:
    """
    MCP service for Radarr API integration.
    Handles movie searching, quality profiles, and download management.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        self.session = None
        
        # Radarr configuration
        self.base_url = config.get('radarr', {}).get('base_url', 'http://localhost:7878')
        self.api_key = config.get('radarr', {}).get('api_key', '')
        self.timeout = config.get('radarr', {}).get('timeout', 30)
        
        # Quality profiles for your year-based logic
        self.quality_profiles = {}
        
    async def initialize(self) -> bool:
        """Initialize the Radarr MCP service."""
        try:
            self.logger.info("Initializing Radarr MCP service...")
            
            # Create aiohttp session
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            headers = {
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers,
                connector=aiohttp.TCPConnector(ssl=False)
            )
            
            # Test connection and load quality profiles
            if await self.test_connection():
                await self.load_quality_profiles()
                self.logger.info("Radarr MCP service initialized successfully")
                return True
            else:
                self.logger.error("Failed to connect to Radarr API")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Radarr MCP: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to Radarr API."""
        try:
            url = f"{self.base_url}/api/v3/system/status"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    version = data.get('version', 'Unknown')
                    self.logger.info(f"Connected to Radarr v{version}")
                    return True
                else:
                    self.logger.error(f"Radarr API returned status {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error testing Radarr connection: {e}")
            return False
    
    async def load_quality_profiles(self):
        """Load quality profiles from Radarr."""
        try:
            url = f"{self.base_url}/api/v3/qualityprofile"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    profiles = await response.json()
                    
                    for profile in profiles:
                        self.quality_profiles[profile['name']] = {
                            'id': profile['id'],
                            'name': profile['name'],
                            'qualities': profile['items'],
                            'min_size': profile.get('minSize', 0),
                            'max_size': profile.get('maxSize', 0)
                        }
                    
                    self.logger.info(f"Loaded {len(self.quality_profiles)} quality profiles")
                    
                    # Log available profiles
                    for name in self.quality_profiles.keys():
                        self.logger.info(f"  - {name}")
                        
                else:
                    self.logger.error(f"Failed to load quality profiles: HTTP {response.status}")
                    
        except Exception as e:
            self.logger.error(f"Error loading quality profiles: {e}")
    
    def parse_file_size(self, size_bytes: int) -> str:
        """Convert bytes to human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        
        return f"{size_bytes:.1f} PB"
    
    def determine_quality_from_title(self, title: str) -> str:
        """Determine quality from release title."""
        title_upper = title.upper()
        
        if any(x in title_upper for x in ['4K', '2160P', 'UHD']):
            return '4K'
        elif '1080P' in title_upper:
            return '1080p'
        elif '720P' in title_upper:
            return '720p'
        else:
            return 'Unknown'
    
    async def search_movie_releases(self, title: str, year: int) -> List[Dict[str, Any]]:
        """
        Search for movie releases via Radarr API.
        This uses Prowlarr indexers configured in Radarr.
        """
        try:
            self.logger.info(f"Searching for movie releases: {title} ({year})")
            
            # First, search for the movie in Radarr's database
            movie_search_url = f"{self.base_url}/api/v3/movie/lookup"
            params = {'term': f"{title} {year}"}
            
            async with self.session.get(movie_search_url, params=params) as response:
                if response.status != 200:
                    self.logger.error(f"Movie lookup failed: HTTP {response.status}")
                    return []
                
                movies = await response.json()
                
                if not movies:
                    self.logger.warning(f"No movies found for: {title} ({year})")
                    return []
                
                # Take the first match (most relevant)
                movie = movies[0]
                tmdb_id = movie.get('tmdbId')
                
                self.logger.info(f"Found movie: {movie.get('title')} (TMDb ID: {tmdb_id})")
            
            # Now search for releases of this movie
            release_search_url = f"{self.base_url}/api/v3/release"
            params = {'movieId': tmdb_id} if tmdb_id else {'term': f"{title} {year}"}
            
            async with self.session.get(release_search_url, params=params) as response:
                if response.status != 200:
                    self.logger.error(f"Release search failed: HTTP {response.status}")
                    return []
                
                releases_data = await response.json()
                
                if not releases_data:
                    self.logger.warning(f"No releases found for: {title} ({year})")
                    return []
                
                # Process releases into our format
                releases = []
                
                for release in releases_data:
                    try:
                        # Extract release information
                        release_title = release.get('title', '')
                        size_bytes = release.get('size', 0)
                        indexer = release.get('indexer', 'Unknown')
                        download_url = release.get('downloadUrl', '')
                        seeders = release.get('seeders', 0)
                        leechers = release.get('leechers', 0)
                        age_hours = release.get('ageHours', 0)
                        
                        # Determine quality from title
                        quality = self.determine_quality_from_title(release_title)
                        
                        # Format size for display
                        size_display = self.parse_file_size(size_bytes)
                        
                        processed_release = {
                            'title': release_title,
                            'size_bytes': size_bytes,
                            'size_display': size_display,
                            'quality': quality,
                            'indexer': indexer,
                            'download_url': download_url,
                            'seeders': seeders,
                            'leechers': leechers,
                            'age_hours': age_hours,
                            'guid': release.get('guid', ''),
                            'protocol': release.get('protocol', 'usenet')
                        }
                        
                        releases.append(processed_release)
                        
                    except Exception as e:
                        self.logger.debug(f"Error processing release: {e}")
                        continue
                
                # Sort by size (largest first) - your preference!
                releases.sort(key=lambda x: x.get('size_bytes', 0), reverse=True)
                
                self.logger.info(f"Found {len(releases)} releases for {title} ({year})")
                
                # Log top releases for debugging
                for i, release in enumerate(releases[:5]):
                    self.logger.info(f"  {i+1}. {release['title'][:60]}... - {release['size_display']} ({release['quality']}) [{release['indexer']}]")
                
                return releases
                
        except Exception as e:
            self.logger.error(f"Error searching movie releases: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def select_best_quality_by_year(self, releases: List[Dict[str, Any]], year: int) -> List[Dict[str, Any]]:
        """
        Apply your exact year-based quality selection logic.
        
        Rules:
        - ≤2009: Largest 1080p only (NOT 4K, even if 4K is larger)
        - 2010-2015: BOTH largest 1080p AND largest 4K
        - 2016+: Largest 4K only
        """
        if not releases:
            return []
        
        self.logger.info(f"Applying quality selection logic for year {year}...")
        
        if year <= 2009:
            # ≤2009: Largest 1080p only (NOT 4K, even if 4K is larger)
            self.logger.info("   💡 Logic: ≤2009: Largest 1080p only")
            
            for release in releases:
                if release.get('quality') == '1080p':
                    self.logger.info(f"   ✅ Selected: {release['title'][:60]}... - {release['size_display']} (1080p)")
                    return [release]
            
            # If no 1080p found, return empty (don't fallback to 4K)
            self.logger.warning("   ⚠️ No 1080p releases found for ≤2009 movie")
            return []
        
        elif 2010 <= year <= 2015:
            # 2010-2015: BOTH largest 1080p AND largest 4K
            self.logger.info("   💡 Logic: 2010-2015: BOTH largest 1080p AND largest 4K")
            
            selected = []
            
            # Find largest 1080p
            for release in releases:
                if release.get('quality') == '1080p':
                    selected.append(release)
                    self.logger.info(f"   ✅ Selected 1080p: {release['title'][:60]}... - {release['size_display']}")
                    break
            
            # Find largest 4K
            for release in releases:
                if release.get('quality') == '4K':
                    selected.append(release)
                    self.logger.info(f"   ✅ Selected 4K: {release['title'][:60]}... - {release['size_display']}")
                    break
            
            return selected
        
        else:  # 2016+
            # 2016+: Largest 4K only
            self.logger.info("   💡 Logic: 2016+: Largest 4K only")
            
            for release in releases:
                if release.get('quality') == '4K':
                    self.logger.info(f"   ✅ Selected: {release['title'][:60]}... - {release['size_display']} (4K)")
                    return [release]
            
            # If no 4K found, fallback to largest 1080p
            for release in releases:
                if release.get('quality') == '1080p':
                    self.logger.info(f"   ⚠️ No 4K found, selected 1080p: {release['title'][:60]}... - {release['size_display']}")
                    return [release]
            
            self.logger.warning("   ⚠️ No suitable releases found for 2016+ movie")
            return []
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        if not self.session or self.session.closed:
            return False
        
        return await self.test_connection()
    
    async def download_release(self, release: Dict[str, Any], movie_title: str, year: int) -> bool:
        """
        Download a release via Radarr API.
        This will send the NZB to your configured download client (SABnzbd).
        """
        try:
            self.logger.info(f"Downloading release: {release['title'][:60]}... - {release['size_display']}")

            download_url = f"{self.base_url}/api/v3/release"

            download_data = {
                'guid': release.get('guid'),
                'indexerId': release.get('indexerId', 1),  # Default indexer
                'title': release.get('title'),
                'size': release.get('size_bytes', 0),
                'downloadUrl': release.get('download_url'),
                'protocol': release.get('protocol', 'usenet')
            }

            async with self.session.post(download_url, json=download_data) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"✅ Successfully sent to download client: {release['title'][:60]}...")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Download failed: HTTP {response.status} - {error_text}")
                    return False

        except Exception as e:
            self.logger.error(f"Error downloading release: {e}")
            return False

    async def get_download_queue(self) -> List[Dict[str, Any]]:
        """Get current download queue from Radarr."""
        try:
            url = f"{self.base_url}/api/v3/queue"

            async with self.session.get(url) as response:
                if response.status == 200:
                    queue_data = await response.json()

                    queue_items = []
                    for item in queue_data.get('records', []):
                        queue_items.append({
                            'title': item.get('title', ''),
                            'status': item.get('status', ''),
                            'progress': item.get('sizeleft', 0),
                            'total_size': item.get('size', 0),
                            'eta': item.get('timeleft', ''),
                            'download_client': item.get('downloadClient', '')
                        })

                    return queue_items
                else:
                    self.logger.error(f"Failed to get download queue: HTTP {response.status}")
                    return []

        except Exception as e:
            self.logger.error(f"Error getting download queue: {e}")
            return []

    async def create_nzb_file(self, release: Dict[str, Any], output_dir: str) -> Optional[str]:
        """
        Create NZB file for manual download (fallback option).
        This maintains compatibility with your existing workflow.
        """
        try:
            import os

            # Create safe filename
            safe_title = "".join(c for c in release['title'] if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            nzb_filename = f"{safe_title}.nzb"
            nzb_path = os.path.join(output_dir, nzb_filename)

            # Create basic NZB content (this would normally come from the indexer)
            nzb_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<nzb xmlns="http://www.newzbin.com/DTD/2003/nzb">
    <head>
        <meta type="title">{release['title']}</meta>
        <meta type="size">{release['size_bytes']}</meta>
        <meta type="indexer">{release['indexer']}</meta>
    </head>
    <file poster="{release['indexer']}" date="{int(datetime.now().timestamp())}" subject="{release['title']}">
        <groups>
            <group>alt.binaries.movies</group>
        </groups>
        <segments>
            <segment bytes="{release['size_bytes']}" number="1">{release['guid']}</segment>
        </segments>
    </file>
</nzb>'''

            # Write NZB file
            os.makedirs(output_dir, exist_ok=True)
            with open(nzb_path, 'w', encoding='utf-8') as f:
                f.write(nzb_content)

            self.logger.info(f"✅ Created NZB file: {nzb_path}")
            return nzb_path

        except Exception as e:
            self.logger.error(f"Error creating NZB file: {e}")
            return None

    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Radarr MCP service...")
        if self.session and not self.session.closed:
            await self.session.close()

#!/usr/bin/env python3
"""
PlexMovieAutomator/02_download_and_organize.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"     Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("       Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

"""
PlexMovieAutomator/src/02_download_and_organize.py

UNIFIED STAGE 02: Modern download monitoring using Radarr API integration.

This unified script replaces multiple O2 files with one clean, working implementation:
- Monitors Radarr for download progress and completion
- Updates pipeline state when movies complete
- Detects completed movies in Plex directories  
- Transitions completed movies to MKV processing stage
- Handles download errors and failures

Consolidates: 02_modern_download_monitor.py + 02_radarr_download_monitor.py
"""

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

import sys
import json
import logging
import asyncio
import aiohttp
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any

# Import SQLite state manager
try:
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    # Fallback import path
    sys.path.insert(0, str(Path(__file__).parent / "_internal"))
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

try:
    from _internal.utils.common_helpers import (
        get_path_setting,
        get_setting,
        get_video_resolution,
        generate_plex_movie_name,
        safe_move_file,
        ensure_dir_exists,
        safe_delete_folder
    )
except ImportError:
    try:
        # Try alternative import path
        from utils.common_helpers import (
            get_path_setting,
            get_setting,
            get_video_resolution,
            generate_plex_movie_name,
            safe_move_file,
            ensure_dir_exists,
            safe_delete_folder
        )
    except ImportError:
        # Fallback for testing - create basic implementations
        def get_path_setting(section, key, settings_dict=None, default=None):
            return settings_dict.get(section, {}).get(key, default) if settings_dict else default

        def get_setting(section, key, settings_dict=None, default=None, expected_type=str):
            return settings_dict.get(section, {}).get(key, default) if settings_dict else default

        # Add placeholder functions for other helpers
        def get_video_resolution(file_path, ffprobe_path, logger_instance=None):
            return 1920, 1080  # Default to 1080p

        def generate_plex_movie_name(title, year, id_value=None, id_type="tmdb"):
            return f"{title} ({year})"

        def safe_move_file(src, dest, logger_instance=None):
            import shutil
            try:
                shutil.move(src, dest)
                return True
            except Exception:
                return False

        def ensure_dir_exists(dir_path, logger_instance=None):
            from pathlib import Path
            try:
                Path(dir_path).mkdir(parents=True, exist_ok=True)
                return True
            except Exception:
                return False

        def safe_delete_folder(folder_path, ignore_errors=True):
            import shutil
            try:
                shutil.rmtree(folder_path, ignore_errors=ignore_errors)
                return True
            except Exception:
                return False

# Import new robust state management
try:
    from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
    from _internal.utils.idempotent_helpers import create_idempotent_operations
except ImportError:
    try:
        from _internal.utils.filesystem_first_state_manager import create_filesystem_first_manager
        from utils.idempotent_helpers import create_idempotent_operations
    except ImportError:
        # Fallback - will use legacy system
        create_robust_state_manager = None
        create_idempotent_operations = None

# --- Global Logger ---
logger = None

def find_completed_downloads_simple(sabnzbd_complete_dir, tracked_movies, logger):
    """
    Simple filesystem-first detection that finds completed downloads
    and matches them to tracked movies using basic title matching.
    
    This function solves the nested folder issue by using rglob() 
    and the detection timing issue by prioritizing filesystem truth.
    """
    completed_matches = []
    
    if not Path(sabnzbd_complete_dir).exists():
        logger.warning(f"Download directory does not exist: {sabnzbd_complete_dir}")
        return completed_matches
    
    logger.info(f"     Scanning download directory: {sabnzbd_complete_dir}")
    
    # Scan for completed files
    for item in Path(sabnzbd_complete_dir).iterdir():
        if item.is_dir():
            # Find largest movie file (handles nested folders automatically with rglob)
            movie_files = list(item.rglob("*.mkv")) + list(item.rglob("*.mp4")) + list(item.rglob("*.avi"))
            
            if movie_files:
                # Get the largest file (main movie)
                main_file = max(movie_files, key=lambda f: f.stat().st_size)
                file_size = main_file.stat().st_size
                
                # Only consider files > 500MB as real movies (filters out samples)
                if file_size > 500 * 1024 * 1024:
                    logger.info(f"     Found completed file: {item.name} -> {main_file.name} ({round(file_size / (1024**3), 2)} GB)")
                    
                    # Try to match with tracked movies
                    for movie in tracked_movies:
                        title = movie.get("cleaned_title", "").lower()
                        year = str(movie.get("year", ""))
                        folder_name = item.name.lower()
                        
                        # Method 1: Simple title + year matching
                        if title and title in folder_name:
                            if not year or year in folder_name:
                                completed_matches.append({
                                    "movie": movie,
                                    "main_file": main_file,
                                    "download_dir": item,
                                    "folder_name": item.name
                                })
                                logger.info(f"    MATCHED: {movie.get('cleaned_title')} -> {item.name}")
                                break
                        
                        # Method 2: Fuzzy word matching if no direct match
                        elif title:
                            movie_words = set(title.replace(".", " ").replace("-", " ").split())
                            folder_words = set(folder_name.replace(".", " ").replace("-", " ").split())
                            
                            # If 70% of movie title words appear in folder name
                            if len(movie_words & folder_words) >= len(movie_words) * 0.7:
                                completed_matches.append({
                                    "movie": movie,
                                    "main_file": main_file,
                                    "download_dir": item,
                                    "folder_name": item.name
                                })
                                logger.info(f"    FUZZY MATCHED: {movie.get('cleaned_title')} -> {item.name}")
                                break
    
    return completed_matches

# Original cleanup_sabnzbd_history function has been replaced with enhanced version at line 482


async def cleanup_completed_downloads_from_radarr(movie, radarr_session, radarr_url, headers, logger):
    """
    ENHANCED: Comprehensive cleanup that works with title matching when IDs are missing.
    
    After successful organization, clean up Radarr queue AND movie record to enable re-downloads.
    Uses multiple matching strategies to find the movie in Radarr.
    """
    try:
        # Get movie identification data
        radarr_id = movie.get("paths", {}).get("radarr_id") if isinstance(movie.get("paths"), dict) else None
        movie_title = movie.get("title") or movie.get("cleaned_title", "Unknown")
        tmdb_id = movie.get("tmdb_id")
        year = movie.get("year")
        
        logger.info(f"🧹 Starting enhanced Radarr cleanup for: {movie_title}")
        logger.info(f"   Available IDs: radarr_id={radarr_id}, tmdb_id={tmdb_id}, year={year}")

        # STEP 1: Get all Radarr movies and queue items first
        logger.info(f"🔍 Step 1: Getting Radarr movies and queue...")
        
        # Get current movies
        async with radarr_session.get(f"{radarr_url}/api/v3/movie", headers=headers) as response:
            if response.status == 200:
                radarr_movies = await response.json()
                logger.info(f"   Retrieved {len(radarr_movies)} movies from Radarr")
            else:
                logger.error(f"   Failed to get Radarr movies: HTTP {response.status}")
                return False
        
        # Get current queue
        async with radarr_session.get(f"{radarr_url}/api/v3/queue", headers=headers) as response:
            if response.status == 200:
                queue_data = await response.json()
                queue_records = queue_data.get("records", [])
                logger.info(f"   Retrieved {len(queue_records)} queue items from Radarr")
            else:
                logger.error(f"   Failed to get Radarr queue: HTTP {response.status}")
                queue_records = []

        # STEP 2: Find matching movie using multiple strategies
        logger.info(f"🎯 Step 2: Finding movie using multiple matching strategies...")
        
        target_movie = None
        matching_strategy = "none"
        
        # Strategy 1: Match by Radarr ID (most reliable)
        if radarr_id:
            target_movie = next((m for m in radarr_movies if m.get('id') == int(radarr_id)), None)
            if target_movie:
                matching_strategy = "radarr_id"
                logger.info(f"   ✅ Found by Radarr ID: {radarr_id}")
        
        # Strategy 2: Match by TMDB ID
        if not target_movie and tmdb_id:
            target_movie = next((m for m in radarr_movies if m.get('tmdbId') == int(tmdb_id)), None)
            if target_movie:
                matching_strategy = "tmdb_id"
                logger.info(f"   ✅ Found by TMDB ID: {tmdb_id}")
        
        # Strategy 3: Match by title and year (fuzzy matching)
        if not target_movie and movie_title and year:
            movie_title_lower = movie_title.lower().strip()
            for radarr_movie in radarr_movies:
                radarr_title = radarr_movie.get('title', '').lower().strip()
                radarr_year = radarr_movie.get('year')
                
                # Exact title + year match
                if radarr_title == movie_title_lower and radarr_year == year:
                    target_movie = radarr_movie
                    matching_strategy = "title_year_exact"
                    logger.info(f"   ✅ Found by exact title+year: {radarr_movie.get('title')} ({radarr_year})")
                    break
                
                # Fuzzy title match (contains) + year match
                elif movie_title_lower in radarr_title and radarr_year == year:
                    target_movie = radarr_movie
                    matching_strategy = "title_year_fuzzy"
                    logger.info(f"   ✅ Found by fuzzy title+year: {radarr_movie.get('title')} ({radarr_year})")
                    break

        # STEP 3: Clean up queue items (even if movie not found in library)
        logger.info(f"🗑️ Step 3: Cleaning up queue items...")
        
        # Find queue items by movie ID (if we found the movie)
        queue_items_to_remove = []
        if target_movie:
            movie_id = target_movie.get('id')
            queue_items_to_remove = [
                item for item in queue_records 
                if item.get("movieId") == movie_id
            ]
            logger.info(f"   Found {len(queue_items_to_remove)} queue items by movie ID")
        
        # Also find queue items by title matching (backup method)
        title_queue_matches = []
        if movie_title:
            movie_words = set(movie_title.lower().replace('.', ' ').replace('-', ' ').split())
            for item in queue_records:
                item_title = item.get('title', '').lower()
                # If 70% of movie title words appear in queue item title
                item_words = set(item_title.replace('.', ' ').replace('-', ' ').split())
                if len(movie_words & item_words) >= len(movie_words) * 0.7:
                    title_queue_matches.append(item)
            
            logger.info(f"   Found {len(title_queue_matches)} additional queue items by title matching")
            
            # Combine and deduplicate
            all_queue_items = queue_items_to_remove + title_queue_matches
            seen_ids = set()
            queue_items_to_remove = []
            for item in all_queue_items:
                item_id = item.get('id')
                if item_id not in seen_ids:
                    queue_items_to_remove.append(item)
                    seen_ids.add(item_id)

        # Remove queue items
        if queue_items_to_remove:
            logger.info(f"   Removing {len(queue_items_to_remove)} queue items...")
            for queue_item in queue_items_to_remove:
                queue_id = queue_item.get("id")
                item_title = queue_item.get("title", "Unknown")
                download_id = queue_item.get("downloadId", "")
                
                # Delete with removeFromClient=true to also clean SABnzbd
                delete_url = f"{radarr_url}/api/v3/queue/{queue_id}?removeFromClient=true&blocklist=false"
                
                async with radarr_session.delete(delete_url, headers=headers) as delete_response:
                    if delete_response.status == 200:
                        logger.info(f"   ✅ Removed queue item: {item_title} (ID: {queue_id})")
                    else:
                        logger.warning(f"   ❌ Failed to remove queue item {queue_id}: HTTP {delete_response.status}")
        else:
            logger.info(f"   No queue items found to remove")

        # STEP 4: Remove movie from library (if found)
        if target_movie:
            movie_id = target_movie.get('id')
            movie_radarr_title = target_movie.get('title')
            
            logger.info(f"🗑️ Step 4: Removing movie from Radarr library...")
            logger.info(f"   Movie: {movie_radarr_title} (ID: {movie_id}, Strategy: {matching_strategy})")
            
            # Remove movie file reference first (if exists)
            if target_movie.get('hasFile', False):
                movie_file_id = target_movie.get('movieFile', {}).get('id')
                if movie_file_id:
                    logger.info(f"   Removing movie file reference (ID: {movie_file_id})")
                    delete_file_url = f"{radarr_url}/api/v3/moviefile/{movie_file_id}"
                    
                    async with radarr_session.delete(delete_file_url, headers=headers) as delete_response:
                        if delete_response.status == 200:
                            logger.info(f"   ✅ Removed movie file reference")
                        else:
                            logger.warning(f"   ⚠️ Failed to remove movie file reference: HTTP {delete_response.status}")
            
            # Remove movie completely
            delete_movie_url = f"{radarr_url}/api/v3/movie/{movie_id}?deleteFiles=false&addImportExclusion=false"
            
            async with radarr_session.delete(delete_movie_url, headers=headers) as delete_response:
                if delete_response.status == 200:
                    logger.info(f"   ✅ Completely removed movie from Radarr library")
                    logger.info(f"🎉 Radarr cleanup complete for {movie_title}")
                    return True
                else:
                    logger.warning(f"   ❌ Failed to remove movie from library: HTTP {delete_response.status}")
                    return False
        else:
            logger.warning(f"   Could not find movie in Radarr library - queue cleanup only")
            return len(queue_items_to_remove) > 0  # Success if we cleaned up queue items

    except Exception as e:
        logger.error(f"Error in enhanced Radarr cleanup for {movie_title}: {e}")
        return False


async def cleanup_sabnzbd_history(movie, settings_dict, logger):
    """
    ENHANCED: Remove movie download history from SABnzbd using multiple matching strategies.
    
    After successful organization, clean up SABnzbd history to prevent confusion
    and allow clean re-downloads of the same movie.
    """
    try:
        # Get SABnzbd settings
        # Support both flat and nested settings_dict keys
        sabnzbd_url = settings_dict.get("sabnzbd_url") or settings_dict.get("sabnzbd", {}).get("url", "")
        sabnzbd_api_key = settings_dict.get("sabnzbd_api_key") or settings_dict.get("sabnzbd", {}).get("api_key", "")
        sabnzbd_url = (sabnzbd_url or "").rstrip("/")
        
        if not sabnzbd_url or not sabnzbd_api_key:
            logger.warning(f"SABnzbd URL or API key not configured (sabnzbd_url={sabnzbd_url}, sabnzbd_api_key={sabnzbd_api_key}), skipping history cleanup")
            return False
        
        movie_title = movie.get("title") or movie.get("cleaned_title", "Unknown")
        nzb_name = movie.get("nzb_name", "")
        
        logger.info(f"🧹 Starting enhanced SABnzbd history cleanup for: {movie_title}")
        logger.info(f"   NZB name: {nzb_name}")
        
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            # Get SABnzbd history
            history_url = f"{sabnzbd_url}/api"
            params = {
                "mode": "history",
                "output": "json",
                "apikey": sabnzbd_api_key,
                "limit": 100  # Get recent history
            }
            
            logger.info(f"🔍 Getting SABnzbd history...")
            async with session.get(history_url, params=params) as response:
                if response.status != 200:
                    logger.error(f"   Failed to get SABnzbd history: HTTP {response.status}")
                    return False
                
                data = await response.json()
                history_slots = data.get("history", {}).get("slots", [])
                logger.info(f"   Retrieved {len(history_slots)} history entries")
                
                # Find matching history entries using multiple strategies
                matching_entries = []
                
                # Strategy 1: Exact NZB name match
                if nzb_name:
                    exact_matches = [
                        slot for slot in history_slots 
                        if slot.get("name", "").lower() == nzb_name.lower()
                    ]
                    matching_entries.extend(exact_matches)
                    if exact_matches:
                        logger.info(f"   ✅ Found {len(exact_matches)} entries by exact NZB name")
                
                # Strategy 2: Title-based fuzzy matching
                if movie_title:
                    movie_words = set(movie_title.lower().replace('.', ' ').replace('-', ' ').split())
                    title_matches = []
                    
                    for slot in history_slots:
                        slot_name = slot.get("name", "").lower()
                        slot_words = set(slot_name.replace('.', ' ').replace('-', ' ').split())
                        
                        # If 70% of movie title words appear in slot name
                        if len(movie_words & slot_words) >= len(movie_words) * 0.7:
                            # Avoid duplicates
                            if not any(slot.get("nzo_id") == existing.get("nzo_id") for existing in matching_entries):
                                title_matches.append(slot)
                    
                    matching_entries.extend(title_matches)
                    if title_matches:
                        logger.info(f"   ✅ Found {len(title_matches)} additional entries by title fuzzy match")
                
                # Remove history entries
                if matching_entries:
                    logger.info(f"🗑️ Removing {len(matching_entries)} SABnzbd history entries...")
                    
                    removed_count = 0
                    for entry in matching_entries:
                        nzo_id = entry.get("nzo_id")
                        entry_name = entry.get("name", "Unknown")
                        
                        if nzo_id:
                            # Delete history entry
                            delete_params = {
                                "mode": "history",
                                "name": "delete",
                                "apikey": sabnzbd_api_key,
                                "value": nzo_id
                            }
                            
                            async with session.get(history_url, params=delete_params) as delete_response:
                                if delete_response.status == 200:
                                    logger.info(f"   ✅ Removed history entry: {entry_name} (ID: {nzo_id})")
                                    removed_count += 1
                                else:
                                    logger.warning(f"   ❌ Failed to remove history entry {nzo_id}: HTTP {delete_response.status}")
                        else:
                            logger.warning(f"   ❌ No NZO ID found for entry: {entry_name}")
                    
                    if removed_count > 0:
                        logger.info(f"🎉 SABnzbd cleanup complete: removed {removed_count} history entries for {movie_title}")
                        return True
                    else:
                        logger.warning(f"Failed to remove any SABnzbd history entries for {movie_title}")
                        return False
                else:
                    logger.info(f"   No matching SABnzbd history entries found for {movie_title}")
                    return True  # No entries to remove is considered success
                    
    except Exception as e:
        logger.error(f"Error in enhanced SABnzbd cleanup for {movie_title}: {e}")
        return False


async def monitor_radarr_downloads(settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    Modern download monitoring using Radarr API with SQLite state management.

    Replaces the complex NZB handling workflow with simple Radarr API monitoring.
    Radarr automatically handles: NZB sending, download monitoring, extraction, file organization.

    Our job: Monitor progress, update pipeline state, handle completion.
    Uses SQLite for reliable state management and filesystem scanning for truth.

    Args:
        settings_dict: Configuration settings
        logger_instance: Logger for output  
        mcp_manager: Optional MCP manager for enhanced features
        
    Returns:
        bool: True if monitoring completed successfully, False otherwise
    """
    global logger
    logger = logger_instance

    # --- Normalize SABnzbd credentials for robust access ---
    def normalize_sabnzbd_settings(settings_dict):
        # If flat keys missing, but section exists, copy them
        sab_section = settings_dict.get("sabnzbd", {})
        if "sabnzbd_url" not in settings_dict and "url" in sab_section:
            settings_dict["sabnzbd_url"] = sab_section["url"]
        if "sabnzbd_api_key" not in settings_dict and "api_key" in sab_section:
            settings_dict["sabnzbd_api_key"] = sab_section["api_key"]
        return settings_dict

    settings_dict = normalize_sabnzbd_settings(settings_dict)

    # Import Path locally to avoid scope issues
    from pathlib import Path
    from datetime import datetime, timezone

    logger.info("===== Starting Modern Radarr Download Monitoring with SQLite =====")
    logger.info("     ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state")

    # Initialize filesystem-first state manager
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # Discover current movies by scanning filesystem
    logger.info("Discovering movies by scanning filesystem...")
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    total_movies = sum(len(movies) for movies in movies_by_stage.values())
    logger.info(f"Found {total_movies} movies across {len(movies_by_stage)} stages")

    # MCP Enhancement: Initialize services
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Get Radarr configuration
    radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
    radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)
    
    if not radarr_api_key:
        logger.error("Radarr API key not configured - cannot monitor downloads")
        return False

    # ENHANCED: Get SABnzbd complete_raw directory for filesystem scanning
    sabnzbd_complete_dir = None
    if "Paths" in settings_dict and "download_complete_raw_dir" in settings_dict["Paths"]:
        raw_path = settings_dict["Paths"]["download_complete_raw_dir"]
        if "%(download_client_active_dir)s" in raw_path:
            base_dir = settings_dict["Paths"].get("download_client_active_dir", "workspace/1_downloading")
            sabnzbd_complete_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
        else:
            sabnzbd_complete_dir = Path(raw_path)
    else:
        sabnzbd_complete_dir = Path("workspace/1_downloading/complete_raw")
    
    logger.info(f"     SABnzbd complete directory: {sabnzbd_complete_dir}")
    logger.info(f"     Radarr API endpoint: {radarr_url}")

    # SMART STATE VALIDATION: Fix broken pipeline states automatically
    logger.info("     SMART STATE VALIDATION: Checking for inconsistent states...")
    
    # First, get current Radarr status to detect active downloads
    headers = {'X-Api-Key': radarr_api_key}
    timeout = aiohttp.ClientTimeout(total=30)
    
    try:
        async with aiohttp.ClientSession(timeout=timeout) as session:
            # Get current Radarr movie list and queue
            async with session.get(f"{radarr_url}/api/v3/movie", headers=headers) as response:
                if response.status == 200:
                    radarr_movies = await response.json()
                    logger.info(f"Retrieved {len(radarr_movies)} movies from Radarr")
                else:
                    logger.error(f"Failed to get Radarr movies: HTTP {response.status}")
                    radarr_movies = []
            
            # Get queue status for logging purposes
            async with session.get(f"{radarr_url}/api/v3/queue", headers=headers) as response:
                if response.status == 200:
                    queue_data = await response.json()
                    active_downloads = len(queue_data.get('records', []))
                    if active_downloads > 0:
                        logger.info(f"     Active downloads in Radarr queue: {active_downloads}")
                    else:
                        logger.info("No active downloads in Radarr queue")
                else:
                    logger.warning(f"Failed to get Radarr queue: HTTP {response.status}")
                
    except Exception as e:
        logger.error(f"Error in Radarr connection check: {e}")
    # Get movies from filesystem that are in download states
    download_states = ["radarr_added_searching", "radarr_downloading", "download_initiated_client", "downloading", "download_completed"]
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    movies_to_monitor = movies_by_stage.get('downloading', []) + movies_by_stage.get('queued', [])

    logger.info(f"Found {len(movies_to_monitor)} movies in download states to monitor")

    # Perform smart validation on SQLite data
    logger.info("     SMART STATE VALIDATION: Checking for inconsistent states...")
    for movie in movies_to_monitor:
        unique_id = movie.get("unique_id")
        title = movie.get("title", "Unknown")
        year = movie.get("year", "")
        status = movie.get("status", "unknown")
        paths = movie.get("paths", {})
        organized_path = paths.get("organized_mkv_path") if isinstance(paths, dict) else None
        
        # Check for broken "mkv_processing_pending" states where file doesn't exist
        if status == "mkv_processing_pending" and organized_path and not Path(organized_path).exists():
            logger.warning(f"     SMART VALIDATION: Fixing broken state for {title} ({year})")
            logger.warning(f"   Claims to be organized at: {organized_path}")
            logger.warning(f"   But file doesn't exist!")
            
            # Update status in SQLite to trigger re-processing
            update_movie_status_filesystem(
                unique_id=unique_id,
                new_status="error_organizing_movie",
                error_message="Smart validation: Movie missing from organized location"
            )
            logger.info(f"       Reset {title} to error state for re-processing")

    # Re-fetch movies after validation updates
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    movies_to_monitor = movies_by_stage.get('downloading', []) + movies_by_stage.get('queued', [])

    logger.info(f"Found {len(movies_to_monitor)} movies in download states to monitor")

    # Helper function to sync pipeline state (filesystem-first approach)
    def sync_pipeline_state():
        try:
            # Refresh movies discovery from filesystem
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            total_movies = sum(len(movies) for movies in movies_by_stage.values())
            logger.info(f"Pipeline state refreshed - found {total_movies} movies")
        except Exception as e:
            logger.error(f"Failed to sync pipeline state: {e}")

    # Helper function to update movie status with filesystem-first approach
    def update_movie_status_filesystem(unique_id: str, new_status: str, error_message: str = None, **additional_data):
        try:
            # Find the movie directory to set stage marker
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            movie_dir = None
            
            # Search through all stages to find the movie
            for stage_movies in movies_by_stage.values():
                for movie in stage_movies:
                    if movie.get('unique_id') == unique_id or movie.get('title') == unique_id:
                        movie_dir = Path(movie.get('movie_directory', ''))
                        break
                if movie_dir:
                    break
            
            if movie_dir and movie_dir.exists():
                # Map status to appropriate stage marker
                stage_mapping = {
                    'downloading': 'downloading',
                    'download_completed': 'organized',
                    'organized': 'organized',
                    'mkv_processing_pending': 'organized'
                }
                stage = stage_mapping.get(new_status, new_status)
                
                success = filesystem_manager.set_stage_marker(movie_dir, stage, {
                    'status': new_status,
                    'error_message': error_message,
                    **additional_data
                })
                
                if success:
                    logger.debug(f"Updated movie {unique_id} status to {new_status}")
                else:
                    logger.error(f"Failed to update movie {unique_id} status")
            else:
                logger.warning(f"Could not find movie directory for {unique_id}")
        except Exception as e:
            logger.error(f"Error updating movie status: {e}")

    # Filter movies that are actively downloading (not completed)
    active_download_movies = [m for m in movies_to_monitor if m.get("status") != "download_completed"]

    if not active_download_movies:
        logger.info("No movies currently in download states")
    else:
        logger.info(f"Monitoring {len(active_download_movies)} movie(s) for download progress")

    try:
        # MCP Enhancement: Initialize services
        sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
        memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
        github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

        # Get Radarr configuration
        radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
        radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)

        if not radarr_api_key:
            logger.error("Radarr API key not configured - cannot monitor downloads")
            return False

        headers = {'X-Api-Key': radarr_api_key}
        
        # ENHANCED: Also check filesystem for completed downloads (addresses detection issue)
        logger.info("     ENHANCED: Checking both Radarr API and filesystem for completed downloads")
        logger.info(f"     Will scan SABnzbd directory: {sabnzbd_complete_dir}")

        # FILESYSTEM-FIRST DETECTION: Check for actual completed files
        completed_files_found = []
        if Path(sabnzbd_complete_dir).exists():
            for item in Path(sabnzbd_complete_dir).iterdir():
                if item.is_dir():
                    # Look for MKV files in the directory
                    mkv_files = list(item.rglob("*.mkv"))
                    for mkv_file in mkv_files:
                        if mkv_file.stat().st_size > 7 * 1024 * 1024 * 1024:  # > 7GB
                            completed_files_found.append({
                                "folder_name": item.name,
                                "mkv_path": mkv_file,
                                "size_gb": round(mkv_file.stat().st_size / (1024**3), 2)
                            })
                            logger.info(f"     Found completed movie: {item.name} ({round(mkv_file.stat().st_size / (1024**3), 2)} GB)")

        if completed_files_found:
            logger.info(f"     FILESYSTEM DETECTION: Found {len(completed_files_found)} completed movies ready for organization")
        else:
            logger.info("     No completed movies found in filesystem")
        # ENHANCED: Simplified and robust detection logic
        # Priority 1: Filesystem scan for completed files (MOST RELIABLE)
        # Priority 2: Radarr API status (secondary confirmation)
        
        logger.info("     ROBUST DETECTION: Scanning filesystem for completed downloads...")
        
        # Use the async detection function with filesystem manager
        completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
        
        logger.info(f"     FILESYSTEM SCAN: Found {len(completed_matches)} completed downloads with matched movies")
        
        # Process matched completed downloads
        organized_count = 0
        for match in completed_matches:
            movie = match["movie_data"]
            main_file = match["mkv_file_path"]
            download_dir = match["download_folder_path"]
            folder_name = download_dir.name
            
            # Handle both regular and auto-generated movie entries
            movie_id = movie.get("unique_id") or movie.get("id") or f"auto_{folder_name}"
            title = movie.get("cleaned_title") or movie.get("title", "Unknown")
            year = movie.get("year", "")
            
            logger.info(f"     Organizing completed movie: {title} ({year}) from {folder_name}")
            
            # Update status to download_completed in SQLite
            update_movie_status_filesystem(
                unique_id=movie_id,
                new_status="download_completed",
                completed_movie_file=str(main_file),
                file_size_bytes=main_file.stat().st_size
            )
            
            # Organize the movie file
            success = await _organize_completed_movie(movie, str(main_file), download_dir, settings_dict, logger)
            
            if success:
                organized_count += 1
                logger.info(f"✅ Successfully organized: {title} ({year})")
                
                # FILESYSTEM-FIRST: No need to update status - .organized marker is sufficient
                # The marker file created in _organize_completed_movie indicates readiness for next stage
                
                # 🧹 CLEANUP: Remove from Radarr and SABnzbd to enable re-downloads
                logger.info(f"🧹 Starting comprehensive cleanup for {title} ({year})")
                try:
                    # Create session for cleanup operations
                    timeout = aiohttp.ClientTimeout(total=30)
                    async with aiohttp.ClientSession(timeout=timeout) as cleanup_session:
                        headers = {'X-Api-Key': get_setting("Radarr", "api_key", settings_dict=settings_dict)}
                        radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
                        
                        # Clean up Radarr (queue, movie file reference, monitoring status)
                        radarr_cleanup_success = await cleanup_completed_downloads_from_radarr(
                            movie, cleanup_session, radarr_url, headers, logger
                        )
                        
                        # Clean up SABnzbd history
                        sabnzbd_cleanup_success = await cleanup_sabnzbd_history(movie, settings_dict, logger)
                        
                        if radarr_cleanup_success and sabnzbd_cleanup_success:
                            logger.info(f"✅ Complete cleanup successful for {title} - ready for re-download if needed")
                        elif radarr_cleanup_success:
                            logger.info(f"✅ Radarr cleanup successful, SABnzbd cleanup had issues")
                        elif sabnzbd_cleanup_success:
                            logger.info(f"✅ SABnzbd cleanup successful, Radarr cleanup had issues")
                        else:
                            logger.warning(f"⚠️ Both cleanup operations had issues for {title}")
                            
                except Exception as e:
                    logger.error(f"Error during cleanup for {title}: {e}")
                    # Don't fail the whole process if cleanup fails
            else:
                logger.error(f"    Failed to organize: {title} ({year})")
                update_movie_status_filesystem(
                    unique_id=movie_id,
                    new_status="error_organizing_movie",
                    error_message="Failed to organize completed download"
                )
        
        logger.info(f"     ORGANIZATION SUMMARY: {organized_count} movies organized successfully")
        
        # Optional: Still check Radarr API for additional status updates
        # But filesystem detection takes priority
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.get(f"{radarr_url}/api/v3/movie", headers=headers) as response:
                    if response.status == 200:
                        radarr_movies = await response.json()
                        logger.info(f"     Radarr API: Retrieved {len(radarr_movies)} movies for status sync")
                        
                        # Sync Radarr status for remaining movies (not yet filesystem-detected)
                        remaining_movies = [m for m in active_download_movies if m.get("status") != "download_completed"]
                        
                        for movie in remaining_movies:
                            movie_id = movie["unique_id"]
                            cleaned_title = movie.get("cleaned_title", "Unknown")
                            year = movie.get("year", "")
                            tmdb_id = movie.get("tmdb_id")
                            radarr_id = movie.get("paths", {}).get("radarr_id")
                            
                            # Find corresponding Radarr movie
                            radarr_movie = None
                            if radarr_id:
                                radarr_movie = next((m for m in radarr_movies if m.get('id') == radarr_id), None)
                            
                            if not radarr_movie and tmdb_id:
                                radarr_movie = next((m for m in radarr_movies if m.get('tmdbId') == tmdb_id), None)
                            
                            if radarr_movie:
                                downloaded = radarr_movie.get('hasFile', False)
                                if downloaded:
                                    movie_file = radarr_movie.get('movieFile', {})
                                    if movie_file:
                                        logger.info(f"     Radarr confirms download: {cleaned_title} ({year})")
                                        update_movie_status_filesystem(
                                            unique_id=movie_id,
                                            new_status="download_completed",
                                            radarr_file_path=movie_file.get('path', '')
                                        )
                    else:
                        logger.warning(f"Radarr API returned HTTP {response.status} - using filesystem detection only")
            except Exception as e:
                logger.warning(f"Error checking Radarr API: {e}")
        
        # Final sync and cleanup
        sync_pipeline_state()
        
        # Close database connection
        metadata_db.close()
        
        logger.info("===== Finished Modern Radarr Download Monitoring =====")
        if organized_count > 0:
            logger.info(f"     SUCCESS: {organized_count} movies organized and ready for MKV processing")
        else:
            logger.info("    No new completed downloads found this run")
        
        return True

    except Exception as e:
        logger.error(f"Critical error in download monitoring: {e}", exc_info=True)
        
        # Close SQLite connection on error
        try:
            if 'metadata_db' in locals():
                metadata_db.close()
        except:
            pass
            
        return False


async def find_completed_downloads_simple(sabnzbd_complete_dir: Path, filesystem_manager, logger) -> List[Dict]:
    """
    Simple filesystem-first detection for completed downloads.
    Uses recursive search to handle nested folder structures.
    Enhanced with movie_import_helper-style detection and stage analysis.
    
    Args:
        sabnzbd_complete_dir: Path to SABnzbd complete directory
        filesystem_manager: SQLite state manager for database operations
        logger: Logger instance
        
    Returns:
        List of completed movies found in filesystem
    """
    
    def analyze_processing_stage(movie_dir: Path) -> Dict:
        """Analyze what processing stage a movie is at based on its content"""
        issues = []
        
        # Find video files
        video_files = []
        for ext in ['.mkv', '.mp4', '.avi', '.m4v']:
            video_files.extend(list(movie_dir.rglob(f"*{ext}")))
        
        # Find subtitle files
        subtitle_files = []
        for ext in ['.srt', '.sup']:
            subtitle_files.extend(list(movie_dir.rglob(f"*{ext}")))
        
        # Find processed directories
        audio_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'audio' in d.name.lower()]
        subtitle_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'subtitle' in d.name.lower()]
        
        # Analyze video processing
        has_processed_video = any('processed' in vf.name.lower() for vf in video_files)
        has_raw_video = any('processed' not in vf.name.lower() for vf in video_files)
        
        # Analyze subtitle processing
        has_srt = any(sf.suffix.lower() == '.srt' for sf in subtitle_files)
        has_sup = any(sf.suffix.lower() == '.sup' for sf in subtitle_files)
        has_subtitle_dirs = len(subtitle_dirs) > 0
        has_audio_dirs = len(audio_dirs) > 0
        
        # Determine processing stage and target
        if has_processed_video and has_subtitle_dirs and has_audio_dirs:
            return {
                'stage': 'mkv_complete',
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        elif has_processed_video and (has_srt or has_subtitle_dirs):
            return {
                'stage': 'mkv_complete',
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        elif has_processed_video:
            if has_sup and not has_srt:
                issues.append("Has .sup files that may need conversion to .srt")
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        elif has_raw_video and (has_audio_dirs or has_subtitle_dirs):
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        elif has_raw_video:
            return {
                'stage': 'organized',
                'markers': ['.organized'],
                'issues': issues
            }
        else:
            issues.append("No clear video files found")
            return {
                'stage': 'manual_review',
                'markers': [],
                'issues': issues
            }
    
    completed_movies = []
    
    if not sabnzbd_complete_dir.exists():
        logger.warning(f"Complete directory does not exist: {sabnzbd_complete_dir}")
        return completed_movies
    
    logger.info(f"     Scanning for completed downloads in: {sabnzbd_complete_dir}")
    
    # Use rglob to recursively find all MKV files (handles nested folders automatically)
    for mkv_file in sabnzbd_complete_dir.rglob("*.mkv"):
        try:
            # Get the top-level download folder
            # For: complete_raw/Movie.Folder/Movie.Folder/movie.mkv
            # We want: Movie.Folder (the top-level one)
            relative_path = mkv_file.relative_to(sabnzbd_complete_dir)
            top_level_folder = relative_path.parts[0]
            folder_path = sabnzbd_complete_dir / top_level_folder
            
            # Extract title and year from folder name
            folder_name = folder_path.name
            
            # Try to match with database entries
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            all_movies = []
            for stage_movies in movies_by_stage.values():
                all_movies.extend(stage_movies)
            matched_movie = None
            
            logger.info(f"     Trying to match: {folder_name}")
            logger.info(f"     Against {len(all_movies)} tracked movies")
            
            # Enhanced matching with multiple strategies
            for db_movie in all_movies:
                title = db_movie.get('title', '').lower().strip()
                cleaned_title = db_movie.get('cleaned_title', '').lower().strip()
                year = str(db_movie.get('year', ''))
                
                logger.debug(f"       Checking: '{title}' ({year}) vs '{folder_name.lower()}'")
                
                # Strategy 1: Direct title + year match
                if title and title in folder_name.lower() and year in folder_name:
                    matched_movie = db_movie
                    logger.info(f"    ✅ MATCHED (title+year): {title} ({year}) -> {folder_name}")
                    break
                    
                # Strategy 2: Cleaned title + year match  
                elif cleaned_title and cleaned_title in folder_name.lower() and year in folder_name:
                    matched_movie = db_movie
                    logger.info(f"    ✅ MATCHED (cleaned+year): {cleaned_title} ({year}) -> {folder_name}")
                    break
                    
                # Strategy 3: Fuzzy word matching
                elif title:
                    title_words = set(title.replace(".", " ").replace("-", " ").split())
                    folder_words = set(folder_name.lower().replace(".", " ").replace("-", " ").split())
                    
                    # If 70% of title words appear in folder name + year match
                    if len(title_words & folder_words) >= len(title_words) * 0.7 and year in folder_name:
                        matched_movie = db_movie
                        logger.info(f"    ✅ MATCHED (fuzzy+year): {title} ({year}) -> {folder_name}")
                        break
            
            if matched_movie:
                size_gb = mkv_file.stat().st_size / (1024**3)
                completed_movies.append({
                    'movie_data': matched_movie,
                    'mkv_file_path': mkv_file,
                    'download_folder_path': folder_path,
                    'size_gb': round(size_gb, 2)
                })
                logger.info(f"    ✅ Found completed: {matched_movie.get('title')} ({matched_movie.get('year')}) - {size_gb:.2f} GB")
            else:
                # REDUNDANCY FEATURE: Auto-add unmatched movies to tracking
                logger.warning(f"     ❌ No database match for: {folder_name}")
                logger.info(f"     🔄 REDUNDANCY: Auto-adding to tracking system...")
                
                # ENHANCED: Extract title and year using multiple patterns (from movie_import_helper)
                import re
                extracted_title = ""
                extracted_year = ""
                
                # Try multiple patterns for better title/year extraction
                patterns = [
                    r'^(.+?)\s*\((\d{4})\).*$',  # Title (Year)
                    r'^(.+?)\s+(\d{4}).*$',      # Title Year
                    r'^(.+?)\.(\d{4})\..*$',     # Title.Year.quality
                ]
                
                for pattern in patterns:
                    match = re.match(pattern, folder_name)
                    if match:
                        extracted_title = match.group(1).strip().replace('.', ' ')
                        extracted_year = int(match.group(2))
                        break
                
                # Fallback: Check for movie indicators even without clear year
                if not extracted_title:
                    movie_indicators = ['bluray', 'bdrip', 'webrip', 'dvdrip', 'hdtv', '1080p', '720p', '4k', 'remux']
                    if any(indicator in folder_name.lower() for indicator in movie_indicators):
                        # Clean up the folder name
                        clean_name = folder_name
                        for tag in movie_indicators + ['x264', 'x265', 'h264', 'h265']:
                            clean_name = re.sub(rf'\b{tag}\b', '', clean_name, flags=re.IGNORECASE)
                        
                        # Try to find year in cleaned name
                        year_match = re.search(r'\b(\d{4})\b', clean_name)
                        if year_match:
                            extracted_year = int(year_match.group(1))
                            extracted_title = clean_name[:year_match.start()].strip(' .-_')
                        else:
                            extracted_title = clean_name.strip(' .-_')
                            extracted_year = 2024  # Default fallback year
                
                if extracted_title:
                    logger.info(f"    📝 Extracted: '{extracted_title}' ({extracted_year})")
                    
                    # Analyze what processing stage this movie is at
                    stage_info = analyze_processing_stage(folder_path)
                    logger.info(f"    🔍 Detected stage: {stage_info['stage']}")
                    if stage_info['issues']:
                        for issue in stage_info['issues']:
                            logger.info(f"    ⚠️  Issue: {issue}")
                    
                    # Create a basic movie entry with consistent structure
                    auto_unique_id = f"auto_{folder_name}"
                    auto_movie = {
                        'unique_id': auto_unique_id,
                        'id': auto_unique_id,  # Add both for compatibility
                        'title': extracted_title,
                        'cleaned_title': extracted_title,
                        'year': extracted_year,
                        'tmdb_id': None,
                        'status': 'download_completed',
                        'auto_added': True,
                        'source': 'filesystem_fallback',
                        'detected_stage': stage_info['stage'],
                        'recommended_markers': stage_info['markers']
                    }
                    
                    # Add to database immediately for consistency
                    try:
                        filesystem_manager.metadata_db.save_movie_metadata(
                            unique_id=auto_unique_id,
                            title=extracted_title,
                            year=extracted_year,
                            metadata={'source': 'filesystem_fallback', 'auto_added': True}
                        )
                        logger.info(f"    ✅ Added to database: {extracted_title} ({extracted_year})")
                    except Exception as db_error:
                        logger.warning(f"    ⚠️  Database add failed: {db_error}")
                    
                    size_gb = mkv_file.stat().st_size / (1024**3)
                    completed_movies.append({
                        'movie_data': auto_movie,
                        'mkv_file_path': mkv_file,
                        'download_folder_path': folder_path,
                        'size_gb': round(size_gb, 2)
                    })
                    
                    logger.info(f"    ✅ AUTO-ADDED: {extracted_title} ({extracted_year}) - {size_gb:.2f} GB")
                    logger.info(f"       Source: filesystem_fallback (no prior tracking required)")
                else:
                    logger.warning(f"       Could not extract title/year from: {folder_name}")
                    logger.info(f"       Skipping this file (needs manual review)")
                
        except Exception as e:
            logger.warning(f"Error processing {mkv_file}: {e}")
    
    logger.info(f"Found {len(completed_movies)} completed downloads with database matches")
    return completed_movies

async def _organize_completed_movie(movie: dict, main_movie_file_path: str, download_dir: Path, settings_dict: dict, logger_instance) -> bool:
    """
    Organizes a completed movie download by moving it to the MKV processing directory.

    Args:
        movie: Movie data dictionary
        main_movie_file_path: Path to the main movie file
        download_dir: The download directory containing the movie
        settings_dict: Settings dictionary
        logger_instance: Logger instance

    Returns:
        bool: True if organization was successful, False otherwise
    """
    try:
        movie_id = movie["unique_id"]
        title = movie.get("title", "Unknown")  # Use 'title' instead of 'cleaned_title'
        year = movie.get("year", "")
        tmdb_id = movie.get("tmdb_id")

        # Get required paths and settings
        mkv_output_base_dir = None
        if "Paths" in settings_dict and "mkv_processing_output_dir" in settings_dict["Paths"]:
            mkv_output_base_dir = Path(settings_dict["Paths"]["mkv_processing_output_dir"])
        else:
            mkv_output_base_dir = Path("workspace/2_downloaded_and_organized")

        issues_hold_dir = None
        if "Paths" in settings_dict and "issues_hold_dir" in settings_dict["Paths"]:
            issues_hold_dir = Path(settings_dict["Paths"]["issues_hold_dir"])
        else:
            issues_hold_dir = Path("workspace/issues_hold")

        issues_org_failed_dir = issues_hold_dir / "organization_failed"

        # Get ffprobe path for resolution detection
        ffprobe_exe_path = settings_dict.get("Executables", {}).get("ffprobe_path", "ffprobe")

        # Detect resolution

        width, _ = get_video_resolution(main_movie_file_path, ffprobe_exe_path, logger_instance)
        if width and width >= 3840:
            resolution = "4K"
        elif width and width >= 1920:
            resolution = "1080p"
        else:
            resolution = "SD_or_unknown"
            logger_instance.warning(f"Detected non-HD resolution for {title}: {width}px wide.")

        # Generate simple movie folder and file names (without TMDB ID)
        # Simple naming: 'Movie Title (Year)' without ID suffix
        import re
        sanitized_title = re.sub(r'[<>:"/\\|?*]', '', title).strip()
        year_str = str(year) if year else "UnknownYear"
        plex_folder_name = f"{sanitized_title} ({year_str})"
        extension = Path(main_movie_file_path).suffix
        plex_filename = f"{plex_folder_name}{extension}"

        # Create destination paths
        final_movie_parent_dir = mkv_output_base_dir / resolution / plex_folder_name
        final_movie_file_path = final_movie_parent_dir / plex_filename

        # Check if destination already exists
        if final_movie_parent_dir.exists():
            logger_instance.warning(f"Destination folder '{final_movie_parent_dir}' already exists. Skipping organization to avoid overwriting.")
            return False

        # Move and organize the file
        logger_instance.info(f"     Organizing '{download_dir.name}' to '{final_movie_file_path}'")

        if ensure_dir_exists(final_movie_parent_dir, logger_instance):
            if safe_move_file(main_movie_file_path, str(final_movie_file_path), logger_instance=logger_instance):
                # Cleanup the raw download folder
                logger_instance.info("Successfully organized movie file. Cleaning up raw download folder.")
                safe_delete_folder(str(download_dir), ignore_errors=True)

                # ENHANCED: Create appropriate stage markers based on detected stage
                try:
                    organized_marker = final_movie_parent_dir / ".organized"
                    organized_marker.write_text("")
                    logger_instance.info(f"    Created .organized marker file for filesystem-first state management")
                    
                    # Also create additional markers if the movie was detected to be at a more advanced stage
                    if movie.get('detected_stage') in ['mkv_complete']:
                        mkv_complete_marker = final_movie_parent_dir / ".mkv_complete"
                        mkv_complete_marker.write_text("")
                        logger_instance.info(f"    Created .mkv_complete marker (movie appears pre-processed)")
                    
                    # Add metadata to marker files for tracking
                    import json
                    marker_data = {
                        'created_by': 'script_02_enhanced',
                        'timestamp': datetime.now().isoformat(),
                        'auto_added': movie.get('auto_added', False),
                        'detected_stage': movie.get('detected_stage', 'organized'),
                        'source': movie.get('source', 'pipeline')
                    }
                    
                    # Update organized marker with metadata
                    with open(organized_marker, 'w') as f:
                        json.dump(marker_data, f, indent=2)
                        
                except Exception as e:
                    logger_instance.warning(f"    Could not create marker files: {e}")

                logger_instance.info(f"    Successfully organized {title} ({year}) to {resolution} folder")
                return True
            else:
                logger_instance.error(f"Failed to move movie file for {title}. Moving raw download to issues hold.")
                safe_move_file(str(download_dir), str(issues_org_failed_dir / download_dir.name), logger_instance=logger_instance)
                return False
        else:
            logger_instance.error(f"Could not create destination directory for {title}. Moving raw download to issues hold.")
            safe_move_file(str(download_dir), str(issues_org_failed_dir / download_dir.name), logger_instance=logger_instance)
            return False

    except Exception as e:
        logger_instance.error(f"Error organizing movie {movie.get('title', 'Unknown')}: {e}")
        return False


# Main stage function for orchestrator compatibility
def run_download_and_organize_stage(movies_data_list: List[Dict[str, Any]], settings_dict: Dict[str, Any], logger_instance: logging.Logger, mcp_manager=None) -> bool:
    # UNIFIED Stage 02: Download monitoring and organization
    return asyncio.run(monitor_radarr_downloads(settings_dict, logger_instance, mcp_manager))

if __name__ == "__main__":
    # Standalone execution for Pipeline 2 - Download and Organize monitoring
    # Preserves all MCP capabilities while allowing manual testing
    print("🎯 UNIFIED Stage 02: Download and Organize")
    print("=" * 50)
    print("✅ Consolidated from multiple O2 scripts into one unified implementation")
    print("🚀 Modern Radarr API integration")
    print("📊 Simplified workflow: Radarr → SABnzbd → Plex")
    print("🧹 Clean, maintainable codebase")
    
    import asyncio
    import logging
    from pathlib import Path
    
    # Import settings loader with fallback
    try:
        from utils.common_helpers import load_settings
    except ImportError:
        import sys
        sys.path.insert(0, str(Path(__file__).parent.parent))
        from src.utils.common_helpers import load_settings
    
    async def robust_download_monitor():
        """
        New robust download monitor using file-based discovery.
        No manual JSON editing required!
        """
        # Setup logging for robust execution
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        robust_logger = logging.getLogger("robust_pipeline_02")

        robust_logger.info("     Starting Robust Download Monitor (No Manual JSON Editing!)")

        # Initialize robust state management
        if create_robust_state_manager is None:
            robust_logger.warning("Robust state management not available, falling back to legacy system")
            return await standalone_download_monitor()

        state_manager = create_filesystem_first_manager()
        idempotent_ops = create_idempotent_operations(state_manager)

        # Discover movies by scanning filesystem
        robust_logger.info("     Discovering movies by scanning filesystem...")
        movies_by_stage = state_manager.discover_movies_by_stage()

        # Process completed downloads
        download_completed = movies_by_stage.get('download_completed', [])
        robust_logger.info(f"Found {len(download_completed)} movies ready for organization")

        if not download_completed:
            robust_logger.info("No movies found ready for organization")
            return

        # Process each movie
        for movie_info in download_completed:
            movie_name = f"{movie_info.get('title', 'Unknown')} ({movie_info.get('year', 'Unknown')})"
            robust_logger.info(f"     Processing: {movie_name}")

            try:
                # Use idempotent organization (safe to run multiple times)
                success, updated_info = idempotent_ops.safe_organize_movie(movie_info)

                if success:
                    robust_logger.info(f"    Successfully organized: {movie_name}")

                    # TODO: Add Radarr cleanup here if needed
                    # await cleanup_completed_downloads_from_radarr(...)

                else:
                    robust_logger.error(f"    Failed to organize: {movie_name}")

            except Exception as e:
                robust_logger.error(f"Error processing {movie_name}: {e}")

        robust_logger.info("     Robust download monitoring complete!")

    async def standalone_download_monitor():
        # Setup logging for standalone execution
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        standalone_logger = logging.getLogger("standalone_pipeline_02")

        try:
            standalone_logger.info("===== Starting Standalone Pipeline 02 Execution =====")

            # Load settings
            settings_dict = load_settings("_internal/config/settings.ini")
            standalone_logger.info("Settings loaded successfully")

            # Run SQLite-based monitoring
            success = await monitor_radarr_downloads(settings_dict, standalone_logger, mcp_manager=None)

            if success:
                standalone_logger.info("    Pipeline 02 completed successfully")
            else:
                standalone_logger.error("    Pipeline 02 failed")

            standalone_logger.info("===== Finished Standalone Pipeline 02 Execution =====")

        except Exception as e:
            standalone_logger.error(f"Error in standalone execution: {e}", exc_info=True)
    
    # Run enhanced standalone monitoring instead of robust (which has detection issues)
    asyncio.run(standalone_download_monitor())

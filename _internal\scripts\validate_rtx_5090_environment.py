#!/usr/bin/env python3
"""
RTX 5090 Environment Validation Script

Validates that the environment is properly configured for RTX 5090 OCR optimization
based on research findings.
"""

import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_pytorch_rtx_5090_support():
    """Check if PyTorch supports RTX 5090 (sm_120)"""
    try:
        import torch
        
        logger.info(f"PyTorch version: {torch.__version__}")
        
        # Check CUDA availability
        if not torch.cuda.is_available():
            logger.error("❌ CUDA not available in PyTorch")
            logger.error("Install PyTorch 2.7.0+ with CUDA 12.8: pip install torch --index-url https://download.pytorch.org/whl/cu128")
            return False
        
        # Check CUDA version
        cuda_version = torch.version.cuda if hasattr(torch.version, 'cuda') else "Unknown"
        logger.info(f"CUDA version: {cuda_version}")
        
        # Check if we have RTX 5090
        if torch.cuda.device_count() > 0:
            gpu_name = torch.cuda.get_device_name(0)
            logger.info(f"GPU: {gpu_name}")
            
            if "5090" in gpu_name:
                logger.info("✅ RTX 5090 detected")
                
                # Test a simple CUDA operation to verify sm_120 support
                try:
                    test_tensor = torch.randn(10, 10, device='cuda')
                    result = torch.matmul(test_tensor, test_tensor)
                    logger.info("✅ CUDA operations working (sm_120 support confirmed)")
                    return True
                except Exception as e:
                    logger.error(f"❌ CUDA operation failed: {e}")
                    logger.error("This suggests PyTorch lacks sm_120 support for RTX 5090")
                    return False
            else:
                logger.warning(f"⚠️ Non-RTX 5090 GPU detected: {gpu_name}")
                return True  # Still valid, just not optimal
        else:
            logger.error("❌ No CUDA devices found")
            return False
            
    except ImportError:
        logger.error("❌ PyTorch not installed")
        return False

def check_ngc_models_environment():
    """Check if NGC Models environment is properly configured"""
    try:
        import onnxruntime as ort
        
        # Check ONNX Runtime version
        version = ort.__version__
        logger.info(f"ONNX Runtime version: {version}")
        
        # Check for TensorRT execution provider
        available_providers = ort.get_available_providers()
        logger.info(f"Available execution providers: {', '.join(available_providers)}")
        
        if 'TensorrtExecutionProvider' in available_providers:
            logger.info("✅ TensorRT execution provider available for maximum performance")
        else:
            logger.warning("⚠️ TensorRT execution provider not available - install TensorRT for best performance")
            
        if 'CUDAExecutionProvider' in available_providers:
            logger.info("✅ CUDA execution provider available")
            
            # Test basic GPU functionality
            try:
                import torch
                if torch.cuda.is_available():
                    gpu_name = torch.cuda.get_device_name(0)
                    logger.info(f"✅ NGC Models GPU environment ready on {gpu_name}")
                    return True
                else:
                    logger.error("❌ CUDA not available for PyTorch")
                    return False
            except Exception as e:
                logger.error(f"❌ GPU test failed: {e}")
                return False
        else:
            logger.error("❌ CUDA execution provider not available")
            logger.error("Please install ONNX Runtime with CUDA support")
            return False
            
    except ImportError:
        logger.error("❌ ONNX Runtime not installed")
        logger.error("Please install: pip install onnxruntime-gpu")
        return False

def check_image_processing_libraries():
    """Check if image processing libraries are available"""
    libraries = {
        'PIL': 'Pillow',
        'numpy': 'numpy', 
        'cv2': 'opencv-python',
        'pysrt': 'pysrt'
    }
    
    all_good = True
    
    for module, package in libraries.items():
        try:
            __import__(module)
            logger.info(f"✅ {module} available")
        except ImportError:
            logger.error(f"❌ {module} not available (install: pip install {package})")
            all_good = False
    
    return all_good

def test_image_preprocessing():
    """Test image preprocessing capabilities (P->RGB conversion)"""
    try:
        from PIL import Image
        import numpy as np
        
        # Create a test palette image
        test_img = Image.new('P', (100, 50), color=0)
        test_img.putpalette([
            255, 255, 255,  # White
            0, 0, 0,         # Black
        ] + [128, 128, 128] * 254)  # Fill remaining palette
        
        # Test conversion to RGB
        rgb_img = test_img.convert('RGB')
        
        # Test conversion to numpy array
        img_array = np.array(rgb_img)
        
        logger.info("✅ Image preprocessing test successful (P->RGB conversion)")
        return True
        
    except Exception as e:
        logger.error(f"❌ Image preprocessing test failed: {e}")
        return False

def check_memory_optimization_settings():
    """Check if memory optimization settings are appropriate"""
    try:
        import torch
        
        if torch.cuda.is_available():
            # Check available GPU memory
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            logger.info(f"GPU Memory: {gpu_memory_gb:.1f} GB")
            
            if gpu_memory_gb >= 24:  # RTX 5090 has 32GB
                logger.info("✅ High-memory GPU detected, optimal batch sizes available")
                
                # Test memory allocation
                try:
                    # Allocate a moderate amount of memory (research shows 20GB is safe)
                    test_size = int(20 * 1024**3 / 4)  # 20GB in float32 elements
                    test_tensor = torch.randn(test_size, device='cuda', dtype=torch.float16)  # Use FP16
                    del test_tensor
                    torch.cuda.empty_cache()
                    
                    logger.info("✅ Memory allocation test passed")
                    return True
                except Exception as e:
                    logger.warning(f"⚠️ Memory allocation test failed: {e}")
                    logger.info("This is normal - indicates conservative memory management")
                    return True
            else:
                logger.warning(f"⚠️ GPU has only {gpu_memory_gb:.1f}GB memory")
                logger.info("Will use smaller batch sizes for compatibility")
                return True
        else:
            logger.error("❌ No CUDA GPU available for memory test")
            return False
            
    except Exception as e:
        logger.error(f"Memory optimization check failed: {e}")
        return False

def main():
    """Main validation process"""
    logger.info("🔍 RTX 5090 Environment Validation")
    logger.info("=" * 50)
    
    all_checks_passed = True
    
    # Check PyTorch RTX 5090 support
    logger.info("Checking PyTorch RTX 5090 support...")
    if not check_pytorch_rtx_5090_support():
        all_checks_passed = False
    
    # Check NGC Models environment
    logger.info("\nChecking NGC Models environment...")
    if not check_ngc_models_environment():
        all_checks_passed = False
    
    # Check supporting libraries
    logger.info("\nChecking supporting libraries...")
    if not check_image_processing_libraries():
        all_checks_passed = False
    
    # Test image preprocessing
    logger.info("\nTesting image preprocessing...")
    if not test_image_preprocessing():
        all_checks_passed = False
    
    # Check memory optimization
    logger.info("\nChecking memory optimization settings...")
    if not check_memory_optimization_settings():
        all_checks_passed = False
    
    # Final result
    logger.info("\n" + "=" * 50)
    if all_checks_passed:
        logger.info("✅ Environment validation PASSED")
        logger.info("🚀 Ready for RTX 5090 optimized OCR processing!")
        return True
    else:
        logger.error("❌ Environment validation FAILED")
        logger.error("Please fix the issues above before running OCR processing")
        logger.info("\n💡 Quick fix command:")
        logger.info("python _internal/scripts/install_rtx_5090_requirements.py")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

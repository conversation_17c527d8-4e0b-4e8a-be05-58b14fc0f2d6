#!/usr/bin/env python3
"""
PlexMovieAutomator/mcp/mcp_pipeline_orchestrator.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the root directory (parent of mcp)
    root_dir = Path(__file__).parent.parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

# Setup paths for clean imports
sys.path.insert(0, str(Path(__file__).parent.parent / "_internal"))

"""
PlexMovieAutomator/mcp/mcp_pipeline_orchestrator.py

Simplified MCP-enabled pipeline orchestrator that focuses on MCP integration
without relying on the existing pipeline stages that may have missing dependencies.
"""

import os
import sys
import asyncio
import argparse
import logging
import time
import json
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

# Add the project root to Python path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import MCP components
from mcp.mcp_manager import MCPManager

class SimplifiedPipelineOrchestrator:
    """
    Simplified orchestrator that demonstrates MCP integration capabilities
    without relying on the existing pipeline stages.
    """
    
    def __init__(self, config_path: str = "_internal/config/settings.ini"):
        self.config_path = Path(config_path)
        self.settings = None
        self.logger = None
        self.mcp_manager = None
        
        # Mock movie data for demonstration
        self.movies_data = [
            {
                "unique_id": "movie_001",
                "cleaned_title": "The Matrix",
                "year": 1999,
                "status": "intake_pending",
                "tmdb_id": 603,
                "created_timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "unique_id": "movie_002", 
                "cleaned_title": "Inception",
                "year": 2010,
                "status": "mkv_processing_pending",
                "tmdb_id": 27205,
                "created_timestamp": datetime.now(timezone.utc).isoformat()
            }
        ]
        
        # Performance tracking
        self.pipeline_start_time = None
        self.total_movies_processed = 0
        
    async def initialize(self):
        """Initialize the orchestrator with settings, logging, and MCP connections."""
        try:
            # Create minimal settings
            self.settings = {
                "Logging": {"log_level": "INFO", "log_directory": "logs"},
                "Orchestrator": {"pipeline_interval_seconds": "300"},
                "MCP": {"enabled": "true", "max_concurrent_connections": "10"}
            }
            
            # Setup logging
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('logs/mcp_pipeline.log', mode='a')
                ]
            )
            self.logger = logging.getLogger("mcp_pipeline_orchestrator")
            self.logger.info("=== MCP-Enabled Plex Movie Automator Pipeline Starting ===")
            
            # Ensure logs directory exists
            Path("logs").mkdir(exist_ok=True)
            
            # Initialize MCP Manager
            self.mcp_manager = MCPManager(self.settings, self.logger)
            await self.mcp_manager.initialize()

            # Initialize Notion Pipeline Integration
            from mcp.notion_pipeline_integration import NotionPipelineIntegration
            self.notion_integration = NotionPipelineIntegration(self.mcp_manager, self.logger)
            if not await self.notion_integration.initialize():
                self.logger.warning("Notion Pipeline Integration failed to initialize - continuing without Notion tracking")
                self.notion_integration = None
            else:
                self.logger.info("Notion Pipeline Integration initialized successfully")

            self.logger.info("MCP Pipeline orchestrator initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize orchestrator: {e}")
            else:
                print(f"Failed to initialize orchestrator: {e}")
            return False
    
    async def demonstrate_mcp_capabilities(self):
        """Demonstrate MCP capabilities with the movie data."""
        try:
            self.pipeline_start_time = time.time()
            self.logger.info("=== Demonstrating MCP Capabilities ===")
            
            # 1. Sequential Thinking - Create processing tasks
            if 'sequential_thinking' in self.mcp_manager.services:
                self.logger.info("--- Sequential Thinking Demonstration ---")
                seq_service = self.mcp_manager.services['sequential_thinking']
                
                for movie in self.movies_data:
                    if movie['status'] == 'mkv_processing_pending':
                        # Create MKV processing task
                        task_id = await seq_service.create_task(
                            task_type="mkv_processing",
                            movie_id=movie['unique_id'],
                            pipeline_stage="03_mkv_processor"
                        )
                        
                        if task_id:
                            self.logger.info(f"Created MKV processing task for {movie['cleaned_title']}")
                            
                            # Start the task
                            await seq_service.start_task(task_id)
                            
                            # Wait for completion
                            await asyncio.sleep(2)
                            
                            # Check status
                            status = await seq_service.get_task_status(task_id)
                            self.logger.info(f"Task status: {status['status']} ({status['progress_percentage']:.1f}%)")
            
            # 2. Memory Manager - Store processing preferences
            if 'memory_manager' in self.mcp_manager.services:
                self.logger.info("--- Memory Manager Demonstration ---")
                memory_service = self.mcp_manager.services['memory_manager']
                
                # Store user preferences
                preferences = {
                    "preferred_resolution": "1080p",
                    "subtitle_languages": ["en", "es"],
                    "quality_threshold": 8.0,
                    "auto_download_posters": True
                }
                
                await memory_service.store_memory(
                    category="processing_preferences",
                    key="user_defaults",
                    value=preferences,
                    tags=["user", "preferences", "pipeline"]
                )
                
                # Store processing history for each movie
                for movie in self.movies_data:
                    processing_history = {
                        "title": movie['cleaned_title'],
                        "year": movie['year'],
                        "tmdb_id": movie['tmdb_id'],
                        "last_processed": datetime.now(timezone.utc).isoformat(),
                        "processing_time_seconds": 120,
                        "success": True
                    }
                    
                    await memory_service.store_memory(
                        category="movie_metadata",
                        key=f"history_{movie['unique_id']}",
                        value=processing_history,
                        tags=["history", "processing", movie['cleaned_title'].lower()]
                    )
                
                # Demonstrate memory retrieval
                retrieved_prefs = await memory_service.retrieve_memory(
                    "processing_preferences", "user_defaults"
                )
                self.logger.info(f"Retrieved preferences: {retrieved_prefs}")
                
                # Search for movie histories
                movie_histories = await memory_service.search_memories(
                    category="movie_metadata",
                    tags=["history"]
                )
                self.logger.info(f"Found {len(movie_histories)} movie processing histories")
                
                # Get memory statistics
                stats = await memory_service.get_memory_stats()
                self.logger.info(f"Memory stats: {stats['total_memories']} total memories")
            
            # 3. Demonstrate MCP service coordination
            self.logger.info("--- MCP Service Coordination ---")
            
            # Get overall service status
            service_status = self.mcp_manager.get_service_status()
            self.logger.info(f"MCP Services: {service_status['total_services']} total, "
                           f"{len([s for s in service_status['services'].values() if s['health']['status'] == 'healthy'])} healthy")
            
            # Broadcast a health check to all services
            health_results = await self.mcp_manager.broadcast_to_services('health_check')
            healthy_services = sum(1 for result in health_results.values() if result is True)
            self.logger.info(f"Health check results: {healthy_services}/{len(health_results)} services healthy")
            
            # Calculate total processing time
            cycle_time = time.time() - self.pipeline_start_time
            self.logger.info(f"=== MCP Demonstration Completed in {cycle_time:.2f} seconds ===")
            
            return True
            
        except Exception as e:
            self.logger.error(f"MCP demonstration failed: {e}")
            return False
    
    async def run_continuous_demo(self, interval_seconds: int = 60):
        """Run continuous MCP demonstration."""
        self.logger.info(f"Starting continuous MCP demo with {interval_seconds}s intervals")
        
        try:
            cycle_count = 0
            while True:
                cycle_count += 1
                self.logger.info(f"=== MCP Demo Cycle {cycle_count} ===")
                
                success = await self.demonstrate_mcp_capabilities()
                
                if success:
                    self.logger.info(f"Demo cycle {cycle_count} completed successfully")
                else:
                    self.logger.warning(f"Demo cycle {cycle_count} had errors")
                
                self.logger.info(f"Waiting {interval_seconds} seconds before next cycle...")
                await asyncio.sleep(interval_seconds)
                
        except KeyboardInterrupt:
            self.logger.info("Continuous demo interrupted by user")
        except Exception as e:
            self.logger.error(f"Continuous demo failed: {e}")
    
    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        return {
            "total_movies": len(self.movies_data),
            "movies_by_status": {
                status: len([m for m in self.movies_data if m.get('status') == status])
                for status in set(m.get('status', 'unknown') for m in self.movies_data)
            },
            "mcp_service_status": self.mcp_manager.get_service_status() if self.mcp_manager else None,
            "pipeline_uptime": time.time() - self.pipeline_start_time if self.pipeline_start_time else 0
        }
    
    async def cleanup(self):
        """Cleanup resources before shutdown."""
        if self.logger:
            self.logger.info("Cleaning up MCP pipeline orchestrator...")
        
        # Cleanup MCP connections
        if self.mcp_manager:
            await self.mcp_manager.cleanup()
        
        if self.logger:
            self.logger.info("MCP pipeline orchestrator cleanup completed")


async def main():
    """Main entry point for the MCP pipeline orchestrator."""
    parser = argparse.ArgumentParser(description="MCP-Enabled Plex Movie Automator Pipeline")
    parser.add_argument("--run-mode", choices=["once", "continuous"], default="once",
                       help="Run mode: 'once' for single demo, 'continuous' for ongoing demo")
    parser.add_argument("--interval", type=int, default=60,
                       help="Interval in seconds for continuous mode")
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug logging")
    
    args = parser.parse_args()
    
    # Initialize orchestrator
    orchestrator = SimplifiedPipelineOrchestrator()
    
    try:
        # Initialize the orchestrator
        if not await orchestrator.initialize():
            print("Failed to initialize MCP pipeline orchestrator")
            return 1
        
        # Set debug logging if requested
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            orchestrator.logger.info("Debug logging enabled")
        
        # Run based on mode
        if args.run_mode == "once":
            success = await orchestrator.demonstrate_mcp_capabilities()
            
            # Print final statistics
            stats = orchestrator.get_pipeline_statistics()
            orchestrator.logger.info("=== Final Pipeline Statistics ===")
            orchestrator.logger.info(f"Total movies: {stats['total_movies']}")
            orchestrator.logger.info(f"Movies by status: {stats['movies_by_status']}")
            
            return 0 if success else 1
            
        elif args.run_mode == "continuous":
            await orchestrator.run_continuous_demo(args.interval)
            return 0
            
    except KeyboardInterrupt:
        if orchestrator.logger:
            orchestrator.logger.info("Pipeline interrupted by user")
        return 0
    except Exception as e:
        if orchestrator.logger:
            orchestrator.logger.error(f"Pipeline failed: {e}")
        else:
            print(f"Pipeline failed: {e}")
        return 1
    finally:
        await orchestrator.cleanup()


if __name__ == "__main__":
    # Run the async main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

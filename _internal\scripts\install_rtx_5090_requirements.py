#!/usr/bin/env python3
"""
RTX 5090 Optimization Requirements Installer

Based on research findings, this script installs the correct versions:
1. PyTorch 2.7.0+ with CUDA 12.8 support for RTX 5090 (sm_120)
2. NVIDIA NGC Models (OCDNet v2.4 + OCRNet v2.1.1) for state-of-the-art OCR
3. Required dependencies for optimal performance

This addresses the key compatibility issues identified in the research.
"""

import subprocess
import sys
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("Python 3.8+ required for RTX 5090 optimizations")
        return False
    logger.info(f"Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def install_pytorch_rtx_5090():
    """Install PyTorch 2.7.0+ with CUDA 12.8 support for RTX 5090"""
    logger.info("Installing PyTorch 2.7.0+ with CUDA 12.8 support for RTX 5090...")
    
    # PyTorch 2.7.0+ with CUDA 12.8 - supports RTX 5090 sm_120
    pytorch_install_cmd = [
        sys.executable, "-m", "pip", "install", "--upgrade",
        "torch", "torchvision", "torchaudio",
        "--index-url", "https://download.pytorch.org/whl/cu128"
    ]
    
    try:
        logger.info("Running: " + " ".join(pytorch_install_cmd))
        result = subprocess.run(pytorch_install_cmd, check=True, capture_output=True, text=True)
        logger.info("PyTorch installation successful")
        logger.debug(f"Output: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"PyTorch installation failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def install_ngc_models_optimized():
    """Install NVIDIA NGC Models dependencies for state-of-the-art OCR"""
    logger.info("Installing NGC Models dependencies (OCDNet v2.4 + OCRNet v2.1.1)...")
    
    ngc_install_cmd = [
        sys.executable, "-m", "pip", "install", "--upgrade",
        "onnxruntime-gpu>=1.20.0",
        "onnx>=1.17.0",
        "transformers>=4.46.0",
        "numpy>=1.24.0"
    ]
    
    try:
        logger.info("Running: " + " ".join(ngc_install_cmd))
        result = subprocess.run(ngc_install_cmd, check=True, capture_output=True, text=True)
        logger.info("NGC Models dependencies installation successful")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"NGC Models installation failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False

def install_supporting_libraries():
    """Install supporting libraries for optimal OCR performance"""
    logger.info("Installing supporting libraries...")
    
    libraries = [
        "pillow>=9.0.0",  # Image processing
        "numpy>=1.21.0",  # Array processing
        "opencv-python>=4.5.0",  # Computer vision
        "pysrt>=1.1.2",  # SRT file handling
    ]
    
    for lib in libraries:
        try:
            install_cmd = [sys.executable, "-m", "pip", "install", "--upgrade", lib]
            logger.info(f"Installing {lib}...")
            result = subprocess.run(install_cmd, check=True, capture_output=True, text=True)
            logger.info(f"{lib} installed successfully")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install {lib}: {e}")
            return False
    
    return True

def verify_installations():
    """Verify all installations and GPU compatibility"""
    logger.info("Verifying installations...")
    
    # Test PyTorch CUDA
    try:
        import torch
        logger.info(f"PyTorch version: {torch.__version__}")
        
        if torch.cuda.is_available():
            logger.info(f"CUDA available: {torch.cuda.is_available()}")
            logger.info(f"CUDA version: {torch.version.cuda}")
            
            if torch.cuda.device_count() > 0:
                gpu_name = torch.cuda.get_device_name(0)
                logger.info(f"GPU detected: {gpu_name}")
                
                if "5090" in gpu_name:
                    logger.info("✅ RTX 5090 detected and CUDA available!")
                elif "RTX" in gpu_name:
                    logger.warning("⚠️ RTX GPU detected but not RTX 5090")
                else:
                    logger.warning("⚠️ Non-RTX GPU detected")
            else:
                logger.error("❌ No CUDA devices found")
        else:
            logger.error("❌ CUDA not available")
            logger.error("This indicates PyTorch was not installed with CUDA 12.8 support")
            return False
            
    except ImportError as e:
        logger.error(f"PyTorch import failed: {e}")
        return False
    
    # Test NGC Models dependencies
    try:
        import onnxruntime as ort
        logger.info(f"ONNX Runtime version: {ort.__version__}")
        
        # Check for TensorRT provider
        providers = ort.get_available_providers()
        if 'TensorrtExecutionProvider' in providers:
            logger.info("✅ TensorRT execution provider available")
        else:
            logger.warning("⚠️ TensorRT execution provider not available")
            
        if 'CUDAExecutionProvider' in providers:
            logger.info("✅ CUDA execution provider available")
        else:
            logger.warning("⚠️ CUDA execution provider not available")
            
    except ImportError as e:
        logger.error(f"ONNX Runtime import failed: {e}")
        return False
    
    # Test other libraries
    try:
        import PIL
        import numpy
        import cv2
        import pysrt
        logger.info("✅ All supporting libraries imported successfully")
    except ImportError as e:
        logger.error(f"Supporting library import failed: {e}")
        return False
    
    return True

def main():
    """Main installation process"""
    logger.info("🎮 RTX 5090 OCR Optimization Setup")
    logger.info("=" * 50)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install PyTorch with CUDA 12.8
    logger.info("Step 1: Installing PyTorch with RTX 5090 support...")
    if not install_pytorch_rtx_5090():
        logger.error("❌ PyTorch installation failed")
        return False
    
    # Install NGC Models dependencies
    logger.info("Step 2: Installing NGC Models dependencies...")
    if not install_ngc_models_optimized():
        logger.error("❌ NGC Models installation failed")
        return False
    
    # Install supporting libraries
    logger.info("Step 3: Installing supporting libraries...")
    if not install_supporting_libraries():
        logger.error("❌ Supporting libraries installation failed")
        return False
    
    # Verify installations
    logger.info("Step 4: Verifying installations...")
    if not verify_installations():
        logger.error("❌ Installation verification failed")
        return False
    
    logger.info("✅ RTX 5090 OCR optimization setup completed successfully!")
    logger.info("🚀 You can now run the optimized subtitle handler")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

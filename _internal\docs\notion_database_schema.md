# Notion Database Schema for Plex Movie Automator

## Overview

This document outlines the comprehensive Notion database schema for the Plex Movie Automator. The schema is designed to track movies throughout the processing pipeline, store metadata, track processing history, and provide analytics.

## Database Structure

The Notion integration consists of multiple linked databases with relationships between them:

1. **Movies Database** - Main database for all movies
2. **Processing History** - Detailed processing logs for each movie
3. **Pipeline Stages** - Configuration and metrics for each pipeline stage
4. **User Preferences** - User-configurable settings
5. **Analytics Dashboard** - Performance metrics and statistics
6. **Error Tracking** - Detailed error logs and resolution status

## 1. Movies Database

The primary database for tracking all movies processed by the system.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Title | Title | Movie title (primary field) |
| Year | Number | Release year |
| TMDB ID | Number | The Movie Database ID |
| IMDB ID | Text | IMDB ID (tt1234567) |
| Unique ID | Text | System-generated unique identifier |
| Status | Select | Current processing status |
| Quality | Select | Video quality (4K, 1080p, 720p, etc.) |
| Runtime | Number | Movie runtime in minutes |
| Genres | Multi-select | Movie genres |
| Director | Text | Movie director |
| Cast | Multi-select | Main cast members |
| Poster | Files & Media | Movie poster image |
| Backdrop | Files & Media | Movie backdrop image |
| Overview | Text | Movie plot summary |
| Added Date | Date | Date added to the system |
| Completed Date | Date | Date processing completed |
| Processing Time | Number | Total processing time in minutes |
| File Size | Number | Final file size in GB |
| File Path | Text | Path to the final movie file |
| Subtitles | Multi-select | Available subtitle languages |
| Audio Tracks | Multi-select | Available audio languages and formats |
| Processing History | Relation | Link to Processing History database |
| Error Count | Number | Number of errors encountered |
| Rating | Number | User rating (1-10) |
| Tags | Multi-select | Custom tags for organization |
| Watch Status | Select | Watched, Unwatched, In Progress |
| Notes | Text | Additional notes |

### Status Options

- Pending Intake
- Metadata Lookup
- NZB Search
- NZB Downloaded
- Downloading
- Download Complete
- MKV Processing
- Subtitle Processing
- Final Mux
- Poster & QC
- Complete
- Error
- Cancelled

## 2. Processing History Database

Detailed logs of each processing step for every movie.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Movie | Relation | Link to Movies database |
| Stage | Select | Pipeline stage (01-05) |
| Operation | Text | Specific operation performed |
| Status | Select | Success, Warning, Error, Info |
| Timestamp | Date | Date and time of the event |
| Duration | Number | Operation duration in seconds |
| Message | Text | Detailed log message |
| Error Details | Text | Error details if applicable |
| Retry Count | Number | Number of retry attempts |
| Memory ID | Text | Related MCP memory ID |
| Task ID | Text | Related sequential task ID |
| Input Data | Text | Input data for the operation (JSON) |
| Output Data | Text | Output data from the operation (JSON) |
| User Action Required | Checkbox | Indicates if user action is needed |
| Resolution | Select | Not Required, Pending, Resolved |

## 3. Pipeline Stages Database

Configuration and metrics for each pipeline stage.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Stage | Select | Pipeline stage (01-05) |
| Name | Text | Stage name |
| Description | Text | Stage description |
| Enabled | Checkbox | Whether the stage is enabled |
| Success Rate | Formula | Percentage of successful operations |
| Average Duration | Number | Average processing time in seconds |
| Last Run | Date | Last execution timestamp |
| Total Processed | Number | Total items processed |
| Successful | Number | Number of successful operations |
| Failed | Number | Number of failed operations |
| Configuration | Text | Stage-specific configuration (JSON) |
| Dependencies | Multi-select | Dependencies on other stages |
| MCP Services | Multi-select | MCP services used by this stage |
| Common Errors | Text | Frequently encountered errors |
| Optimization Notes | Text | Notes for performance optimization |

## 4. User Preferences Database

User-configurable settings for the pipeline.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Category | Select | Preference category |
| Name | Text | Setting name |
| Value | Text | Setting value |
| Description | Text | Setting description |
| Data Type | Select | String, Number, Boolean, JSON, etc. |
| Default Value | Text | Default value |
| Options | Text | Available options (for selects) |
| Stage | Select | Related pipeline stage |
| Last Modified | Date | Last modification timestamp |
| Modified By | Text | User who last modified the setting |
| Requires Restart | Checkbox | Whether changes require system restart |
| Advanced | Checkbox | Whether this is an advanced setting |
| UI Group | Select | Grouping for UI display |

### Categories

- General
- Intake
- Download
- MKV Processing
- Subtitle Handling
- Final Mux
- Poster & QC
- OCR
- Notifications
- System
- MCP

## 5. Analytics Dashboard Database

Performance metrics and statistics for the pipeline.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Metric | Text | Metric name |
| Category | Select | Metric category |
| Value | Number | Current value |
| Unit | Select | Measurement unit |
| Trend | Select | Increasing, Decreasing, Stable |
| Period | Select | Daily, Weekly, Monthly, All-time |
| Date | Date | Measurement date |
| Chart Type | Select | Line, Bar, Pie, etc. |
| Related Stage | Select | Related pipeline stage |
| Description | Text | Metric description |
| Goal | Number | Target value |
| Alert Threshold | Number | Threshold for alerts |
| Data Source | Text | Source of the metric data |
| Calculation | Text | Formula or method used |

### Categories

- Performance
- Quality
- Efficiency
- Resource Usage
- Success Rate
- Error Rate
- Processing Time
- File Size
- OCR Quality
- System Health

## 6. Error Tracking Database

Detailed error logs and resolution status.

### Properties

| Property Name | Type | Description |
|---------------|------|-------------|
| Error ID | Text | Unique error identifier |
| Movie | Relation | Related movie |
| Stage | Select | Pipeline stage where error occurred |
| Error Type | Select | Type of error |
| Severity | Select | Critical, High, Medium, Low |
| Status | Select | New, In Progress, Resolved, Won't Fix |
| Timestamp | Date | When the error occurred |
| Message | Text | Error message |
| Stack Trace | Text | Error stack trace |
| Frequency | Number | How many times this error has occurred |
| Last Occurrence | Date | When this error last occurred |
| Resolution | Text | How the error was resolved |
| Resolution Date | Date | When the error was resolved |
| Assigned To | Text | Person assigned to fix the error |
| GitHub Issue | Text | Related GitHub issue URL |
| Notes | Text | Additional notes |

## Database Relationships

The following diagram illustrates the relationships between the databases:

```
Movies Database
    ↓ 1:N
Processing History Database
    ↑ N:1
    ↓ N:1
Pipeline Stages Database
    ↑ 1:N
    ↓ 1:N
User Preferences Database
    ↑ N:1
    ↓ N:1
Analytics Dashboard Database
    ↑ N:1
    ↓ N:1
Error Tracking Database
    ↑ N:1
```

## Views

Each database will have multiple views for different use cases:

### Movies Database Views

1. **All Movies** - Complete list of all movies
2. **Currently Processing** - Movies currently in the pipeline
3. **Completed Movies** - Successfully processed movies
4. **Error Movies** - Movies with errors
5. **By Stage** - Movies grouped by current stage
6. **By Quality** - Movies grouped by quality
7. **Recently Added** - Recently added movies
8. **Recently Completed** - Recently completed movies
9. **By Genre** - Movies grouped by genre
10. **By Year** - Movies grouped by release year

### Processing History Views

1. **All Events** - Complete processing history
2. **By Movie** - History grouped by movie
3. **By Stage** - History grouped by pipeline stage
4. **Errors Only** - Error events only
5. **Recent Events** - Recent processing events
6. **Requiring Action** - Events requiring user action
7. **Unresolved Issues** - Unresolved issues

### Pipeline Stages Views

1. **All Stages** - Overview of all pipeline stages
2. **Performance Metrics** - Stages with performance metrics
3. **Configuration** - Stage configuration details
4. **Dependencies** - Stage dependencies visualization

### User Preferences Views

1. **All Preferences** - Complete list of all preferences
2. **By Category** - Preferences grouped by category
3. **By Stage** - Preferences grouped by pipeline stage
4. **Advanced Settings** - Advanced settings only
5. **Recently Modified** - Recently modified preferences

### Analytics Dashboard Views

1. **Overview** - Key performance indicators
2. **Performance Trends** - Performance trends over time
3. **Quality Metrics** - Quality-related metrics
4. **Resource Usage** - Resource usage metrics
5. **Success Rates** - Success rates by stage
6. **Processing Times** - Processing times by stage
7. **Daily Report** - Daily performance report
8. **Weekly Report** - Weekly performance report
9. **Monthly Report** - Monthly performance report

### Error Tracking Views

1. **All Errors** - Complete list of all errors
2. **Active Errors** - Unresolved errors
3. **By Severity** - Errors grouped by severity
4. **By Stage** - Errors grouped by pipeline stage
5. **Recently Occurred** - Recently occurred errors
6. **Recently Resolved** - Recently resolved errors
7. **By Frequency** - Errors sorted by frequency

## Implementation Notes

1. The Notion API will be used to create, update, and query these databases.
2. Each pipeline stage will update the relevant databases during processing.
3. The MCP Manager will coordinate database updates across all stages.
4. Batch operations will be used to minimize API calls.
5. Caching will be implemented to reduce API usage.
6. Error handling will ensure database integrity even during API failures.
7. Periodic synchronization will ensure consistency between the local state and Notion.
8. Custom views and dashboards will be created for easy monitoring.
9. Automation will be used to generate reports and alerts.
10. User-friendly forms will be created for preference management.

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/notification_system.py

Notification System MCP Service
Sends notifications for download completions, failures, and pipeline events.
Supports multiple notification channels: <PERSON>rd, Slack, Email, Desktop, etc.
"""

import logging
import asyncio
import aiohttp
import json
import smtplib
from typing import Dict, List, Optional, Any
from datetime import datetime
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class NotificationSystemMCP:
    """
    MCP service for sending notifications across multiple channels.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        self.session = None
        
        # Notification channels configuration
        self.channels = config.get('notifications', {}).get('channels', {})
        self.enabled_channels = [ch for ch, cfg in self.channels.items() if cfg.get('enabled', False)]
        
        # Message templates
        self.templates = {
            'download_completed': {
                'title': '✅ Download Completed',
                'message': '{title} ({quality}) has finished downloading!\nSize: {size}\nTime: {duration}'
            },
            'download_failed': {
                'title': '❌ Download Failed',
                'message': '{title} download failed!\nError: {error}\nTime: {timestamp}'
            },
            'pipeline_summary': {
                'title': '📊 Pipeline Summary',
                'message': 'Pipeline completed!\n🎬 Movies: {movies}\n📺 TV Shows: {tv_shows}\n⏱️ Duration: {duration}'
            },
            'quality_upgrade': {
                'title': '⬆️ Quality Upgrade',
                'message': '{title} upgraded from {old_quality} to {new_quality}!\nNew size: {size}'
            }
        }
        
    async def initialize(self) -> bool:
        """Initialize the Notification System MCP service."""
        try:
            self.logger.info("Initializing Notification System MCP service...")
            
            # Create aiohttp session for web-based notifications
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {'Content-Type': 'application/json'}
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers,
                connector=aiohttp.TCPConnector(ssl=False)
            )
            
            self.logger.info(f"Notification system initialized with channels: {', '.join(self.enabled_channels)}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Notification System MCP: {e}")
            return False
    
    async def send_notification(self, event_type: str, data: Dict[str, Any], priority: str = 'normal'):
        """
        Send notification to all enabled channels.
        
        Args:
            event_type: Type of event (download_completed, download_failed, etc.)
            data: Event data to include in notification
            priority: Notification priority (low, normal, high, urgent)
        """
        try:
            if event_type not in self.templates:
                self.logger.warning(f"Unknown notification event type: {event_type}")
                return
            
            template = self.templates[event_type]
            title = template['title']
            message = template['message'].format(**data)
            
            # Send to all enabled channels
            tasks = []
            for channel in self.enabled_channels:
                task = self._send_to_channel(channel, title, message, data, priority)
                tasks.append(task)
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                # Log results
                for i, result in enumerate(results):
                    channel = self.enabled_channels[i]
                    if isinstance(result, Exception):
                        self.logger.error(f"Failed to send notification to {channel}: {result}")
                    else:
                        self.logger.info(f"Notification sent to {channel}: {title}")
            
        except Exception as e:
            self.logger.error(f"Error sending notification: {e}")
    
    async def _send_to_channel(self, channel: str, title: str, message: str, data: Dict[str, Any], priority: str):
        """Send notification to a specific channel."""
        channel_config = self.channels.get(channel, {})
        
        if channel == 'discord':
            await self._send_discord(channel_config, title, message, data, priority)
        elif channel == 'slack':
            await self._send_slack(channel_config, title, message, data, priority)
        elif channel == 'email':
            await self._send_email(channel_config, title, message, data, priority)
        elif channel == 'desktop':
            await self._send_desktop(channel_config, title, message, data, priority)
        elif channel == 'webhook':
            await self._send_webhook(channel_config, title, message, data, priority)
        else:
            self.logger.warning(f"Unknown notification channel: {channel}")
    
    async def _send_discord(self, config: Dict[str, Any], title: str, message: str, data: Dict[str, Any], priority: str):
        """Send notification to Discord webhook."""
        webhook_url = config.get('webhook_url')
        if not webhook_url:
            raise ValueError("Discord webhook URL not configured")
        
        # Create Discord embed
        color = self._get_priority_color(priority)
        
        embed = {
            "title": title,
            "description": message,
            "color": color,
            "timestamp": datetime.now().isoformat(),
            "footer": {
                "text": "Plex Movie Automator"
            }
        }
        
        # Add fields for additional data
        if 'title' in data:
            embed["fields"] = [
                {"name": "Title", "value": data['title'], "inline": True}
            ]
        
        if 'quality' in data:
            embed["fields"].append(
                {"name": "Quality", "value": data['quality'], "inline": True}
            )
        
        if 'size' in data:
            embed["fields"].append(
                {"name": "Size", "value": data['size'], "inline": True}
            )
        
        payload = {
            "embeds": [embed],
            "username": "Plex Automator"
        }
        
        async with self.session.post(webhook_url, json=payload) as response:
            if response.status not in [200, 204]:
                raise Exception(f"Discord webhook returned status {response.status}")
    
    async def _send_slack(self, config: Dict[str, Any], title: str, message: str, data: Dict[str, Any], priority: str):
        """Send notification to Slack webhook."""
        webhook_url = config.get('webhook_url')
        if not webhook_url:
            raise ValueError("Slack webhook URL not configured")
        
        # Create Slack message
        color = self._get_priority_color_slack(priority)
        
        attachment = {
            "color": color,
            "title": title,
            "text": message,
            "timestamp": int(datetime.now().timestamp()),
            "footer": "Plex Movie Automator"
        }
        
        # Add fields for additional data
        fields = []
        if 'title' in data:
            fields.append({"title": "Title", "value": data['title'], "short": True})
        if 'quality' in data:
            fields.append({"title": "Quality", "value": data['quality'], "short": True})
        if 'size' in data:
            fields.append({"title": "Size", "value": data['size'], "short": True})
        
        if fields:
            attachment["fields"] = fields
        
        payload = {
            "attachments": [attachment],
            "username": "Plex Automator"
        }
        
        async with self.session.post(webhook_url, json=payload) as response:
            if response.status != 200:
                raise Exception(f"Slack webhook returned status {response.status}")
    
    async def _send_email(self, config: Dict[str, Any], title: str, message: str, data: Dict[str, Any], priority: str):
        """Send email notification."""
        smtp_server = config.get('smtp_server')
        smtp_port = config.get('smtp_port', 587)
        username = config.get('username')
        password = config.get('password')
        from_email = config.get('from_email', username)
        to_emails = config.get('to_emails', [])
        
        if not all([smtp_server, username, password, to_emails]):
            raise ValueError("Email configuration incomplete")
        
        # Create email message
        msg = MIMEMultipart()
        msg['From'] = from_email
        msg['To'] = ', '.join(to_emails)
        msg['Subject'] = f"[Plex Automator] {title}"
        
        # Create HTML body
        html_body = f"""
        <html>
        <body>
            <h2>{title}</h2>
            <p>{message.replace(chr(10), '<br>')}</p>
            
            <hr>
            <p><small>Sent by Plex Movie Automator at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</small></p>
        </body>
        </html>
        """
        
        msg.attach(MIMEText(html_body, 'html'))
        
        # Send email (run in thread to avoid blocking)
        def send_email():
            try:
                server = smtplib.SMTP(smtp_server, smtp_port)
                server.starttls()
                server.login(username, password)
                server.send_message(msg)
                server.quit()
            except Exception as e:
                raise Exception(f"Failed to send email: {e}")
        
        # Run in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, send_email)
    
    async def _send_desktop(self, config: Dict[str, Any], title: str, message: str, data: Dict[str, Any], priority: str):
        """Send desktop notification."""
        try:
            # Try to use plyer for cross-platform notifications
            from plyer import notification
            
            notification.notify(
                title=title,
                message=message,
                timeout=config.get('timeout', 10),
                app_name="Plex Movie Automator"
            )
            
        except ImportError:
            # Fallback to Windows toast notifications
            try:
                import win10toast
                toaster = win10toast.ToastNotifier()
                toaster.show_toast(
                    title,
                    message,
                    duration=config.get('timeout', 10),
                    threaded=True
                )
            except ImportError:
                raise Exception("No desktop notification library available (install plyer or win10toast)")
    
    async def _send_webhook(self, config: Dict[str, Any], title: str, message: str, data: Dict[str, Any], priority: str):
        """Send notification to custom webhook."""
        webhook_url = config.get('url')
        if not webhook_url:
            raise ValueError("Webhook URL not configured")
        
        payload = {
            "title": title,
            "message": message,
            "priority": priority,
            "timestamp": datetime.now().isoformat(),
            "data": data,
            "source": "plex_movie_automator"
        }
        
        async with self.session.post(webhook_url, json=payload) as response:
            if response.status not in [200, 201, 202]:
                raise Exception(f"Webhook returned status {response.status}")
    
    def _get_priority_color(self, priority: str) -> int:
        """Get Discord embed color based on priority."""
        colors = {
            'low': 0x95a5a6,      # Gray
            'normal': 0x3498db,   # Blue
            'high': 0xf39c12,     # Orange
            'urgent': 0xe74c3c    # Red
        }
        return colors.get(priority, colors['normal'])
    
    def _get_priority_color_slack(self, priority: str) -> str:
        """Get Slack attachment color based on priority."""
        colors = {
            'low': '#95a5a6',     # Gray
            'normal': '#3498db',  # Blue
            'high': '#f39c12',    # Orange
            'urgent': '#e74c3c'   # Red
        }
        return colors.get(priority, colors['normal'])
    
    async def send_download_completed(self, title: str, quality: str, size: str, duration: str):
        """Send download completed notification."""
        await self.send_notification('download_completed', {
            'title': title,
            'quality': quality,
            'size': size,
            'duration': duration
        }, priority='normal')
    
    async def send_download_failed(self, title: str, error: str):
        """Send download failed notification."""
        await self.send_notification('download_failed', {
            'title': title,
            'error': error,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }, priority='high')
    
    async def send_pipeline_summary(self, movies_count: int, tv_count: int, duration: str):
        """Send pipeline summary notification."""
        await self.send_notification('pipeline_summary', {
            'movies': movies_count,
            'tv_shows': tv_count,
            'duration': duration
        }, priority='low')
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        return self.session is not None and not self.session.closed
    
    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Notification System MCP service...")
        if self.session and not self.session.closed:
            await self.session.close()

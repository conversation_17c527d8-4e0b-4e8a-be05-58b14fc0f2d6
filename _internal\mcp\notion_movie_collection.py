#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/notion_movie_collection.py

Notion Movie Collection Management for Plex Movie Automator
Implements complete movie collection tracking with metadata, quality information, file paths, and processing history.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field

@dataclass
class MovieMetadata:
    """Represents comprehensive movie metadata."""
    title: str
    year: int = None
    tmdb_id: int = None
    imdb_id: str = None
    runtime: int = None
    genres: List[str] = field(default_factory=list)
    director: str = None
    cast: List[str] = field(default_factory=list)
    overview: str = None
    poster_url: str = None
    backdrop_url: str = None
    release_date: str = None
    original_language: str = None
    production_companies: List[str] = field(default_factory=list)
    vote_average: float = None
    vote_count: int = None
    popularity: float = None
    budget: int = None
    revenue: int = None
    tagline: str = None
    status: str = None
    homepage: str = None
    collection: str = None

@dataclass
class MovieFile:
    """Represents a movie file with technical details."""
    file_path: str
    file_size: float = None  # in GB
    resolution: str = None
    quality: str = None
    video_codec: str = None
    audio_codec: str = None
    audio_channels: str = None
    subtitles: List[str] = field(default_factory=list)
    audio_tracks: List[str] = field(default_factory=list)
    container: str = None
    bit_depth: int = None
    color_space: str = None
    frame_rate: float = None
    aspect_ratio: str = None
    duration: int = None  # in seconds

@dataclass
class MovieProcessingHistory:
    """Represents a movie's processing history."""
    events: List[Dict[str, Any]] = field(default_factory=list)
    
    def add_event(self, stage: str, operation: str, status: str, 
                 message: str, timestamp: str = None, duration: float = None,
                 error_details: str = None) -> None:
        """Add a processing event."""
        if not timestamp:
            timestamp = datetime.now(timezone.utc).isoformat()
        
        self.events.append({
            "stage": stage,
            "operation": operation,
            "status": status,
            "message": message,
            "timestamp": timestamp,
            "duration": duration,
            "error_details": error_details
        })
    
    def get_latest_event(self) -> Optional[Dict[str, Any]]:
        """Get the latest processing event."""
        if not self.events:
            return None
        
        return sorted(self.events, key=lambda e: e.get("timestamp", ""), reverse=True)[0]
    
    def get_events_by_stage(self, stage: str) -> List[Dict[str, Any]]:
        """Get all events for a specific stage."""
        return [e for e in self.events if e.get("stage") == stage]
    
    def get_events_by_status(self, status: str) -> List[Dict[str, Any]]:
        """Get all events with a specific status."""
        return [e for e in self.events if e.get("status") == status]
    
    def get_error_events(self) -> List[Dict[str, Any]]:
        """Get all error events."""
        return self.get_events_by_status("error")
    
    def get_success_events(self) -> List[Dict[str, Any]]:
        """Get all success events."""
        return self.get_events_by_status("success")

@dataclass
class Movie:
    """Represents a complete movie with all its information."""
    unique_id: str
    notion_page_id: str = None
    status: str = "Pending"
    metadata: MovieMetadata = None
    file: MovieFile = None
    processing_history: MovieProcessingHistory = field(default_factory=MovieProcessingHistory)
    added_date: str = None
    completed_date: str = None
    processing_time: float = None  # in minutes
    error_count: int = 0
    rating: float = None
    tags: List[str] = field(default_factory=list)
    watch_status: str = "Unwatched"
    notes: str = None
    
    def __post_init__(self):
        if not self.added_date:
            self.added_date = datetime.now(timezone.utc).isoformat()
        if not self.metadata:
            self.metadata = MovieMetadata(title="Unknown")
        if not self.file:
            self.file = MovieFile(file_path="")

class NotionMovieCollection:
    """
    Comprehensive Notion movie collection management for the Plex Movie Automator.
    Implements complete movie collection tracking with metadata, quality information, file paths, and processing history.
    """
    
    def __init__(self, notion_service, logger: logging.Logger):
        self.notion_service = notion_service
        self.logger = logger
        self.movies_db_id = notion_service.movies_db_id if notion_service else None
        self.history_db_id = notion_service.history_db_id if notion_service else None
        self.movies: Dict[str, Movie] = {}
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the Notion movie collection."""
        try:
            self.logger.info("Initializing Notion Movie Collection...")
            
            if not self.notion_service or not self.notion_service.initialized:
                self.logger.error("Notion service not initialized")
                return False
            
            if not self.movies_db_id:
                self.logger.warning("Movies database ID not provided - collection management disabled")
                return False
            
            # Verify movies database exists
            movies_db = self.notion_service.databases.get("movies")
            if not movies_db:
                self.logger.error("Movies database not found")
                return False
            
            # Load existing movies from Notion
            await self._load_existing_movies()
            
            self.initialized = True
            self.logger.info(f"Notion Movie Collection initialized successfully with {len(self.movies)} movies")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Notion Movie Collection: {e}")
            return False
    
    async def _load_existing_movies(self) -> None:
        """Load existing movies from Notion."""
        try:
            # Query all movies from Notion
            response = await self.notion_service._make_api_request(
                method="POST",
                endpoint=f"/databases/{self.movies_db_id}/query",
                data={"page_size": 100}  # Adjust as needed
            )
            
            if not response or "results" not in response:
                self.logger.warning("No movies found in Notion database")
                return
            
            # Process each movie
            for page in response["results"]:
                try:
                    movie = await self._parse_notion_page(page)
                    if movie:
                        self.movies[movie.unique_id] = movie
                except Exception as e:
                    self.logger.error(f"Error parsing movie page: {e}")
            
            self.logger.info(f"Loaded {len(self.movies)} movies from Notion")
            
            # Handle pagination if needed
            while response.get("has_more", False) and response.get("next_cursor"):
                response = await self.notion_service._make_api_request(
                    method="POST",
                    endpoint=f"/databases/{self.movies_db_id}/query",
                    data={
                        "page_size": 100,
                        "start_cursor": response["next_cursor"]
                    }
                )
                
                if not response or "results" not in response:
                    break
                
                # Process each movie
                for page in response["results"]:
                    try:
                        movie = await self._parse_notion_page(page)
                        if movie:
                            self.movies[movie.unique_id] = movie
                    except Exception as e:
                        self.logger.error(f"Error parsing movie page: {e}")
                
                self.logger.info(f"Loaded {len(self.movies)} movies from Notion (paginated)")
                
        except Exception as e:
            self.logger.error(f"Error loading existing movies: {e}")
    
    async def _parse_notion_page(self, page: Dict[str, Any]) -> Optional[Movie]:
        """Parse a Notion page into a Movie object."""
        try:
            properties = page.get("properties", {})
            
            # Extract unique ID
            unique_id_prop = properties.get("Unique ID", {})
            unique_id = self._extract_rich_text_value(unique_id_prop)
            
            if not unique_id:
                self.logger.warning(f"Movie page missing Unique ID: {page.get('id')}")
                return None
            
            # Create movie object
            movie = Movie(
                unique_id=unique_id,
                notion_page_id=page.get("id")
            )
            
            # Extract basic properties
            title_prop = properties.get("Title", {})
            title = self._extract_title_value(title_prop)
            
            year_prop = properties.get("Year", {})
            year = self._extract_number_value(year_prop)
            
            status_prop = properties.get("Status", {})
            status = self._extract_select_value(status_prop)
            
            # Set basic properties
            movie.status = status or "Pending"
            
            # Create metadata
            movie.metadata = MovieMetadata(
                title=title or "Unknown",
                year=year
            )
            
            # Extract additional metadata
            tmdb_id_prop = properties.get("TMDB ID", {})
            movie.metadata.tmdb_id = self._extract_number_value(tmdb_id_prop)
            
            imdb_id_prop = properties.get("IMDB ID", {})
            movie.metadata.imdb_id = self._extract_rich_text_value(imdb_id_prop)
            
            runtime_prop = properties.get("Runtime", {})
            movie.metadata.runtime = self._extract_number_value(runtime_prop)
            
            genres_prop = properties.get("Genres", {})
            movie.metadata.genres = self._extract_multi_select_values(genres_prop)
            
            director_prop = properties.get("Director", {})
            movie.metadata.director = self._extract_rich_text_value(director_prop)
            
            cast_prop = properties.get("Cast", {})
            movie.metadata.cast = self._extract_multi_select_values(cast_prop)
            
            overview_prop = properties.get("Overview", {})
            movie.metadata.overview = self._extract_rich_text_value(overview_prop)
            
            # Extract file information
            file_path_prop = properties.get("File Path", {})
            file_path = self._extract_rich_text_value(file_path_prop)
            
            quality_prop = properties.get("Quality", {})
            quality = self._extract_select_value(quality_prop)
            
            file_size_prop = properties.get("File Size", {})
            file_size = self._extract_number_value(file_size_prop)
            
            subtitles_prop = properties.get("Subtitles", {})
            subtitles = self._extract_multi_select_values(subtitles_prop)
            
            audio_tracks_prop = properties.get("Audio Tracks", {})
            audio_tracks = self._extract_multi_select_values(audio_tracks_prop)
            
            # Create file object
            movie.file = MovieFile(
                file_path=file_path or "",
                file_size=file_size,
                quality=quality,
                subtitles=subtitles,
                audio_tracks=audio_tracks
            )
            
            # Extract additional properties
            added_date_prop = properties.get("Added Date", {})
            movie.added_date = self._extract_date_value(added_date_prop)
            
            completed_date_prop = properties.get("Completed Date", {})
            movie.completed_date = self._extract_date_value(completed_date_prop)
            
            processing_time_prop = properties.get("Processing Time", {})
            movie.processing_time = self._extract_number_value(processing_time_prop)
            
            error_count_prop = properties.get("Error Count", {})
            movie.error_count = self._extract_number_value(error_count_prop) or 0
            
            rating_prop = properties.get("Rating", {})
            movie.rating = self._extract_number_value(rating_prop)
            
            tags_prop = properties.get("Tags", {})
            movie.tags = self._extract_multi_select_values(tags_prop)
            
            watch_status_prop = properties.get("Watch Status", {})
            movie.watch_status = self._extract_select_value(watch_status_prop) or "Unwatched"
            
            notes_prop = properties.get("Notes", {})
            movie.notes = self._extract_rich_text_value(notes_prop)
            
            return movie
            
        except Exception as e:
            self.logger.error(f"Error parsing Notion page: {e}")
            return None
    
    def _extract_title_value(self, property_data: Dict[str, Any]) -> Optional[str]:
        """Extract value from a title property."""
        if not property_data or property_data.get("type") != "title":
            return None
        
        title = property_data.get("title", [])
        if not title:
            return None
        
        return title[0].get("plain_text", "")
    
    def _extract_rich_text_value(self, property_data: Dict[str, Any]) -> Optional[str]:
        """Extract value from a rich text property."""
        if not property_data or property_data.get("type") != "rich_text":
            return None
        
        rich_text = property_data.get("rich_text", [])
        if not rich_text:
            return None
        
        return rich_text[0].get("plain_text", "")
    
    def _extract_number_value(self, property_data: Dict[str, Any]) -> Optional[float]:
        """Extract value from a number property."""
        if not property_data or property_data.get("type") != "number":
            return None
        
        return property_data.get("number")
    
    def _extract_select_value(self, property_data: Dict[str, Any]) -> Optional[str]:
        """Extract value from a select property."""
        if not property_data or property_data.get("type") != "select":
            return None
        
        select = property_data.get("select")
        if not select:
            return None
        
        return select.get("name")
    
    def _extract_multi_select_values(self, property_data: Dict[str, Any]) -> List[str]:
        """Extract values from a multi-select property."""
        if not property_data or property_data.get("type") != "multi_select":
            return []
        
        multi_select = property_data.get("multi_select", [])
        return [item.get("name", "") for item in multi_select if item.get("name")]
    
    def _extract_date_value(self, property_data: Dict[str, Any]) -> Optional[str]:
        """Extract value from a date property."""
        if not property_data or property_data.get("type") != "date":
            return None
        
        date = property_data.get("date")
        if not date:
            return None
        
        return date.get("start")
    
    async def add_movie(self, movie: Movie) -> bool:
        """Add a movie to the collection."""
        if not self.initialized:
            return False
        
        try:
            # Check if movie already exists
            if movie.unique_id in self.movies:
                self.logger.warning(f"Movie already exists in collection: {movie.unique_id}")
                return await self.update_movie(movie)
            
            # Create movie in Notion
            properties = self._movie_to_notion_properties(movie)
            
            response = await self.notion_service._make_api_request(
                method="POST",
                endpoint="/pages",
                data={
                    "parent": {"database_id": self.movies_db_id},
                    "properties": properties
                }
            )
            
            if response and "id" in response:
                movie.notion_page_id = response["id"]
                self.movies[movie.unique_id] = movie
                
                self.logger.info(f"Added movie to collection: {movie.metadata.title} ({movie.unique_id})")
                return True
            else:
                self.logger.error(f"Failed to add movie to collection: {movie.metadata.title}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error adding movie to collection: {e}")
            return False
    
    async def update_movie(self, movie: Movie) -> bool:
        """Update a movie in the collection."""
        if not self.initialized:
            return False
        
        try:
            # Check if movie exists
            existing_movie = self.movies.get(movie.unique_id)
            if not existing_movie:
                self.logger.warning(f"Movie not found in collection: {movie.unique_id}")
                return await self.add_movie(movie)
            
            # Use existing page ID
            if not movie.notion_page_id and existing_movie.notion_page_id:
                movie.notion_page_id = existing_movie.notion_page_id
            
            if not movie.notion_page_id:
                self.logger.error(f"Movie missing Notion page ID: {movie.unique_id}")
                return False
            
            # Update movie in Notion
            properties = self._movie_to_notion_properties(movie)
            
            response = await self.notion_service._make_api_request(
                method="PATCH",
                endpoint=f"/pages/{movie.notion_page_id}",
                data={"properties": properties}
            )
            
            if response and "id" in response:
                self.movies[movie.unique_id] = movie
                
                self.logger.info(f"Updated movie in collection: {movie.metadata.title} ({movie.unique_id})")
                return True
            else:
                self.logger.error(f"Failed to update movie in collection: {movie.metadata.title}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error updating movie in collection: {e}")
            return False
    
    def _movie_to_notion_properties(self, movie: Movie) -> Dict[str, Any]:
        """Convert a Movie object to Notion properties."""
        properties = {
            "Title": {"title": [{"text": {"content": movie.metadata.title or "Unknown"}}]},
            "Unique ID": {"rich_text": [{"text": {"content": movie.unique_id}}]},
            "Status": {"select": {"name": movie.status}}
        }
        
        # Add metadata properties
        if movie.metadata:
            if movie.metadata.year:
                properties["Year"] = {"number": movie.metadata.year}
            
            if movie.metadata.tmdb_id:
                properties["TMDB ID"] = {"number": movie.metadata.tmdb_id}
            
            if movie.metadata.imdb_id:
                properties["IMDB ID"] = {"rich_text": [{"text": {"content": movie.metadata.imdb_id}}]}
            
            if movie.metadata.runtime:
                properties["Runtime"] = {"number": movie.metadata.runtime}
            
            if movie.metadata.genres:
                properties["Genres"] = {"multi_select": [{"name": genre} for genre in movie.metadata.genres]}
            
            if movie.metadata.director:
                properties["Director"] = {"rich_text": [{"text": {"content": movie.metadata.director}}]}
            
            if movie.metadata.cast:
                properties["Cast"] = {"multi_select": [{"name": actor} for actor in movie.metadata.cast[:5]]}  # Limit to top 5
            
            if movie.metadata.overview:
                properties["Overview"] = {"rich_text": [{"text": {"content": movie.metadata.overview[:2000]}}]}  # Limit length
        
        # Add file properties
        if movie.file:
            if movie.file.file_path:
                properties["File Path"] = {"rich_text": [{"text": {"content": movie.file.file_path}}]}
            
            if movie.file.quality:
                properties["Quality"] = {"select": {"name": movie.file.quality}}
            
            if movie.file.file_size is not None:
                properties["File Size"] = {"number": movie.file.file_size}
            
            if movie.file.subtitles:
                properties["Subtitles"] = {"multi_select": [{"name": sub} for sub in movie.file.subtitles]}
            
            if movie.file.audio_tracks:
                properties["Audio Tracks"] = {"multi_select": [{"name": track} for track in movie.file.audio_tracks]}
        
        # Add additional properties
        if movie.added_date:
            properties["Added Date"] = {"date": {"start": movie.added_date}}
        
        if movie.completed_date:
            properties["Completed Date"] = {"date": {"start": movie.completed_date}}
        
        if movie.processing_time is not None:
            properties["Processing Time"] = {"number": movie.processing_time}
        
        if movie.error_count is not None:
            properties["Error Count"] = {"number": movie.error_count}
        
        if movie.rating is not None:
            properties["Rating"] = {"number": movie.rating}
        
        if movie.tags:
            properties["Tags"] = {"multi_select": [{"name": tag} for tag in movie.tags]}
        
        if movie.watch_status:
            properties["Watch Status"] = {"select": {"name": movie.watch_status}}
        
        if movie.notes:
            properties["Notes"] = {"rich_text": [{"text": {"content": movie.notes[:2000]}}]}  # Limit length
        
        return properties
    
    async def get_movie(self, movie_id: str) -> Optional[Movie]:
        """Get a movie from the collection."""
        return self.movies.get(movie_id)
    
    async def add_processing_event(self, movie_id: str, stage: str, operation: str,
                                  status: str, message: str, error_details: str = None,
                                  duration: float = None) -> bool:
        """Add a processing event to a movie's history."""
        if not self.initialized:
            return False
        
        try:
            # Get movie
            movie = self.movies.get(movie_id)
            if not movie:
                self.logger.warning(f"Movie not found for processing event: {movie_id}")
                return False
            
            # Add event to movie history
            movie.processing_history.add_event(
                stage=stage,
                operation=operation,
                status=status,
                message=message,
                error_details=error_details,
                duration=duration
            )
            
            # Update error count if this is an error
            if status.lower() == "error":
                movie.error_count += 1
            
            # Add processing history entry to Notion
            if self.history_db_id and movie.notion_page_id:
                await self.notion_service.add_processing_history(
                    movie_id=movie_id,
                    notion_page_id=movie.notion_page_id,
                    stage=stage,
                    operation=operation,
                    status=status,
                    message=message,
                    error_details=error_details,
                    duration=duration
                )
            
            # Update movie in collection
            self.movies[movie_id] = movie
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding processing event: {e}")
            return False
    
    async def search_movies(self, query: str = None, status: str = None, 
                           quality: str = None, genre: str = None) -> List[Movie]:
        """Search for movies in the collection."""
        results = []
        
        for movie in self.movies.values():
            # Apply filters
            if query and query.lower() not in movie.metadata.title.lower():
                continue
            
            if status and movie.status != status:
                continue
            
            if quality and (not movie.file or movie.file.quality != quality):
                continue
            
            if genre and (not movie.metadata or genre not in movie.metadata.genres):
                continue
            
            results.append(movie)
        
        return results
    
    async def get_collection_statistics(self) -> Dict[str, Any]:
        """Get statistics about the movie collection."""
        stats = {
            "total_movies": len(self.movies),
            "by_status": {},
            "by_quality": {},
            "by_genre": {},
            "average_runtime": 0,
            "total_file_size": 0,
            "error_rate": 0
        }
        
        total_runtime = 0
        total_file_size = 0
        error_count = 0
        runtime_count = 0
        genres = {}
        
        for movie in self.movies.values():
            # Count by status
            status = movie.status
            stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
            
            # Count by quality
            if movie.file and movie.file.quality:
                quality = movie.file.quality
                stats["by_quality"][quality] = stats["by_quality"].get(quality, 0) + 1
            
            # Count by genre
            if movie.metadata and movie.metadata.genres:
                for genre in movie.metadata.genres:
                    genres[genre] = genres.get(genre, 0) + 1
            
            # Accumulate runtime
            if movie.metadata and movie.metadata.runtime:
                total_runtime += movie.metadata.runtime
                runtime_count += 1
            
            # Accumulate file size
            if movie.file and movie.file.file_size:
                total_file_size += movie.file.file_size
            
            # Count errors
            if movie.error_count > 0:
                error_count += 1
        
        # Calculate averages
        if runtime_count > 0:
            stats["average_runtime"] = total_runtime / runtime_count
        
        stats["total_file_size"] = total_file_size
        
        if len(self.movies) > 0:
            stats["error_rate"] = (error_count / len(self.movies)) * 100
        
        # Sort genres by count
        stats["by_genre"] = dict(sorted(genres.items(), key=lambda x: x[1], reverse=True))
        
        return stats
    
    async def health_check(self) -> bool:
        """Perform health check on the collection."""
        return self.initialized and self.notion_service and self.notion_service.initialized
    
    async def cleanup(self) -> None:
        """Cleanup the collection."""
        self.logger.info("Cleaning up Notion Movie Collection...")
        self.movies.clear()
        self.initialized = False

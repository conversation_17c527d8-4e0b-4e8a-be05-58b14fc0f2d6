#!/usr/bin/env python3
"""
PlexMovieAutomator Status Dashboard
Quick access to pipeline status from _internal directory
"""

# We're already in _internal, so we can import directly
from utils.status_dashboard import show_pipeline_status, show_movie_details
import sys

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Show details for specific movie
        movie_name = " ".join(sys.argv[1:])
        show_movie_details(movie_name)
    else:
        # Show overall status
        show_pipeline_status()

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/memory_manager.py

Memory Manager MCP Server Integration
Stores and retrieves contextual information across pipeline sessions,
including processing preferences, error patterns, and learned optimizations.
"""

import json
import sqlite3
import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import hashlib

@dataclass
class Memory:
    """Represents a single memory entry."""
    id: str
    category: str
    key: str
    value: Any
    metadata: Dict[str, Any]
    created_time: str
    last_accessed: str
    access_count: int = 0
    ttl_days: Optional[int] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []

class MemoryManagerMCP:
    """
    MCP service for storing and retrieving contextual information across pipeline sessions.
    Provides intelligent memory management with categorization, TTL, and pattern recognition.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        
        # Configuration
        self.max_memories = config.additional_config.get("max_memories", 1000) if config else 1000
        self.default_ttl_days = config.additional_config.get("memory_ttl_days", 30) if config else 30
        self.auto_cleanup = config.additional_config.get("auto_cleanup", True) if config else True
        
        # Database setup
        self.db_path = Path("data/memory_store.db")
        self.db_connection = None
        
        # Memory categories
        self.categories = {
            "processing_preferences": "User and system processing preferences",
            "error_patterns": "Common error patterns and their solutions",
            "optimization_hints": "Performance optimizations and best practices",
            "movie_metadata": "Cached movie metadata and processing history",
            "user_feedback": "User feedback and quality assessments",
            "system_metrics": "System performance and resource usage patterns",
            "pipeline_state": "Pipeline state and configuration snapshots"
        }
        
        # In-memory cache for frequently accessed memories
        self.memory_cache: Dict[str, Memory] = {}
        self.cache_max_size = 100
    
    async def initialize(self) -> bool:
        """Initialize the Memory Manager MCP service."""
        try:
            self.logger.info("Initializing Memory Manager MCP service...")
            
            # Ensure data directory exists
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Initialize database
            await self._initialize_database()
            
            # Load frequently accessed memories into cache
            await self._load_cache()
            
            # Start cleanup task if enabled
            if self.auto_cleanup:
                import asyncio
                asyncio.create_task(self._cleanup_loop())
            
            self.logger.info("Memory Manager MCP service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Memory Manager MCP: {e}")
            return False
    
    async def _initialize_database(self):
        """Initialize the SQLite database for memory storage."""
        self.db_connection = sqlite3.connect(str(self.db_path))
        self.db_connection.row_factory = sqlite3.Row
        
        # Create memories table
        self.db_connection.execute("""
            CREATE TABLE IF NOT EXISTS memories (
                id TEXT PRIMARY KEY,
                category TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                metadata TEXT,
                created_time TEXT NOT NULL,
                last_accessed TEXT NOT NULL,
                access_count INTEGER DEFAULT 0,
                ttl_days INTEGER,
                tags TEXT,
                UNIQUE(category, key)
            )
        """)
        
        # Create indexes for better performance
        self.db_connection.execute("CREATE INDEX IF NOT EXISTS idx_category ON memories(category)")
        self.db_connection.execute("CREATE INDEX IF NOT EXISTS idx_key ON memories(key)")
        self.db_connection.execute("CREATE INDEX IF NOT EXISTS idx_created_time ON memories(created_time)")
        self.db_connection.execute("CREATE INDEX IF NOT EXISTS idx_last_accessed ON memories(last_accessed)")
        
        self.db_connection.commit()
    
    async def store_memory(self, category: str, key: str, value: Any,
                          metadata: Dict[str, Any] = None, ttl_days: int = None,
                          tags: List[str] = None) -> bool:
        """
        Store a memory entry.
        
        Args:
            category: Memory category (e.g., 'processing_preferences')
            key: Unique key within the category
            value: The value to store (will be JSON serialized)
            metadata: Additional metadata about the memory
            ttl_days: Time-to-live in days (uses default if None)
            tags: Tags for categorization and search
            
        Returns:
            True if stored successfully, False otherwise
        """
        try:
            # Generate memory ID
            memory_id = self._generate_memory_id(category, key)
            
            # Prepare data
            current_time = datetime.now(timezone.utc).isoformat()
            value_json = json.dumps(value)
            metadata_json = json.dumps(metadata or {})
            tags_json = json.dumps(tags or [])
            ttl = ttl_days or self.default_ttl_days
            
            # Store in database
            self.db_connection.execute("""
                INSERT OR REPLACE INTO memories 
                (id, category, key, value, metadata, created_time, last_accessed, access_count, ttl_days, tags)
                VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)
            """, (memory_id, category, key, value_json, metadata_json, current_time, current_time, ttl, tags_json))
            
            self.db_connection.commit()
            
            # Update cache
            memory = Memory(
                id=memory_id,
                category=category,
                key=key,
                value=value,
                metadata=metadata or {},
                created_time=current_time,
                last_accessed=current_time,
                access_count=0,
                ttl_days=ttl,
                tags=tags or []
            )
            
            self._update_cache(memory)
            
            self.logger.debug(f"Stored memory: {category}/{key}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to store memory {category}/{key}: {e}")
            return False
    
    async def retrieve_memory(self, category: str, key: str) -> Optional[Any]:
        """
        Retrieve a memory value.
        
        Args:
            category: Memory category
            key: Memory key
            
        Returns:
            The stored value or None if not found
        """
        try:
            memory_id = self._generate_memory_id(category, key)
            
            # Check cache first
            if memory_id in self.memory_cache:
                memory = self.memory_cache[memory_id]
                await self._update_access_stats(memory_id)
                return memory.value
            
            # Query database
            cursor = self.db_connection.execute(
                "SELECT * FROM memories WHERE id = ?", (memory_id,)
            )
            row = cursor.fetchone()
            
            if row:
                # Parse the data
                value = json.loads(row['value'])
                metadata = json.loads(row['metadata'])
                tags = json.loads(row['tags'])
                
                # Create memory object
                memory = Memory(
                    id=row['id'],
                    category=row['category'],
                    key=row['key'],
                    value=value,
                    metadata=metadata,
                    created_time=row['created_time'],
                    last_accessed=row['last_accessed'],
                    access_count=row['access_count'],
                    ttl_days=row['ttl_days'],
                    tags=tags
                )
                
                # Update cache and access stats
                self._update_cache(memory)
                await self._update_access_stats(memory_id)
                
                return value
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to retrieve memory {category}/{key}: {e}")
            return None
    
    async def search_memories(self, category: str = None, tags: List[str] = None,
                             key_pattern: str = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Search for memories based on various criteria.
        
        Args:
            category: Filter by category
            tags: Filter by tags (memories must have all specified tags)
            key_pattern: Filter by key pattern (SQL LIKE pattern)
            limit: Maximum number of results
            
        Returns:
            List of memory dictionaries
        """
        try:
            query = "SELECT * FROM memories WHERE 1=1"
            params = []
            
            if category:
                query += " AND category = ?"
                params.append(category)
            
            if key_pattern:
                query += " AND key LIKE ?"
                params.append(key_pattern)
            
            query += " ORDER BY last_accessed DESC LIMIT ?"
            params.append(limit)
            
            cursor = self.db_connection.execute(query, params)
            rows = cursor.fetchall()
            
            results = []
            for row in rows:
                # Parse data
                value = json.loads(row['value'])
                metadata = json.loads(row['metadata'])
                row_tags = json.loads(row['tags'])
                
                # Filter by tags if specified
                if tags and not all(tag in row_tags for tag in tags):
                    continue
                
                results.append({
                    'id': row['id'],
                    'category': row['category'],
                    'key': row['key'],
                    'value': value,
                    'metadata': metadata,
                    'created_time': row['created_time'],
                    'last_accessed': row['last_accessed'],
                    'access_count': row['access_count'],
                    'tags': row_tags
                })
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search memories: {e}")
            return []
    
    async def delete_memory(self, category: str, key: str) -> bool:
        """Delete a specific memory."""
        try:
            memory_id = self._generate_memory_id(category, key)
            
            # Remove from database
            cursor = self.db_connection.execute("DELETE FROM memories WHERE id = ?", (memory_id,))
            self.db_connection.commit()
            
            # Remove from cache
            if memory_id in self.memory_cache:
                del self.memory_cache[memory_id]
            
            deleted = cursor.rowcount > 0
            if deleted:
                self.logger.debug(f"Deleted memory: {category}/{key}")
            
            return deleted
            
        except Exception as e:
            self.logger.error(f"Failed to delete memory {category}/{key}: {e}")
            return False
    
    async def get_memory_stats(self) -> Dict[str, Any]:
        """Get statistics about stored memories."""
        try:
            cursor = self.db_connection.execute("""
                SELECT 
                    category,
                    COUNT(*) as count,
                    AVG(access_count) as avg_access_count,
                    MAX(last_accessed) as most_recent_access
                FROM memories 
                GROUP BY category
            """)
            
            category_stats = {}
            total_memories = 0
            
            for row in cursor.fetchall():
                category_stats[row['category']] = {
                    'count': row['count'],
                    'avg_access_count': round(row['avg_access_count'], 2),
                    'most_recent_access': row['most_recent_access']
                }
                total_memories += row['count']
            
            return {
                'total_memories': total_memories,
                'categories': category_stats,
                'cache_size': len(self.memory_cache),
                'max_memories': self.max_memories,
                'memory_usage_percent': (total_memories / self.max_memories) * 100
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get memory stats: {e}")
            return {}
    
    def _generate_memory_id(self, category: str, key: str) -> str:
        """Generate a unique memory ID from category and key."""
        combined = f"{category}:{key}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _update_cache(self, memory: Memory):
        """Update the in-memory cache with a memory entry."""
        # Remove oldest entries if cache is full
        if len(self.memory_cache) >= self.cache_max_size:
            # Remove least recently accessed memory
            oldest_id = min(self.memory_cache.keys(), 
                           key=lambda x: self.memory_cache[x].last_accessed)
            del self.memory_cache[oldest_id]
        
        self.memory_cache[memory.id] = memory
    
    async def _update_access_stats(self, memory_id: str):
        """Update access statistics for a memory."""
        current_time = datetime.now(timezone.utc).isoformat()
        
        self.db_connection.execute("""
            UPDATE memories 
            SET last_accessed = ?, access_count = access_count + 1 
            WHERE id = ?
        """, (current_time, memory_id))
        
        self.db_connection.commit()
        
        # Update cache if present
        if memory_id in self.memory_cache:
            self.memory_cache[memory_id].last_accessed = current_time
            self.memory_cache[memory_id].access_count += 1
    
    async def _load_cache(self):
        """Load frequently accessed memories into cache."""
        try:
            cursor = self.db_connection.execute("""
                SELECT * FROM memories 
                ORDER BY access_count DESC, last_accessed DESC 
                LIMIT ?
            """, (self.cache_max_size,))
            
            for row in cursor.fetchall():
                value = json.loads(row['value'])
                metadata = json.loads(row['metadata'])
                tags = json.loads(row['tags'])
                
                memory = Memory(
                    id=row['id'],
                    category=row['category'],
                    key=row['key'],
                    value=value,
                    metadata=metadata,
                    created_time=row['created_time'],
                    last_accessed=row['last_accessed'],
                    access_count=row['access_count'],
                    ttl_days=row['ttl_days'],
                    tags=tags
                )
                
                self.memory_cache[memory.id] = memory
            
            self.logger.info(f"Loaded {len(self.memory_cache)} memories into cache")
            
        except Exception as e:
            self.logger.error(f"Failed to load memory cache: {e}")
    
    async def _cleanup_loop(self):
        """Background task to clean up expired memories."""
        while True:
            try:
                await asyncio.sleep(3600)  # Run every hour
                await self._cleanup_expired_memories()
            except Exception as e:
                self.logger.error(f"Memory cleanup error: {e}")
    
    async def _cleanup_expired_memories(self):
        """Remove expired memories based on TTL."""
        try:
            current_time = datetime.now(timezone.utc)
            
            cursor = self.db_connection.execute("""
                SELECT id, created_time, ttl_days FROM memories 
                WHERE ttl_days IS NOT NULL
            """)
            
            expired_ids = []
            for row in cursor.fetchall():
                created_time = datetime.fromisoformat(row['created_time'].replace('Z', '+00:00'))
                ttl_delta = timedelta(days=row['ttl_days'])
                
                if current_time - created_time > ttl_delta:
                    expired_ids.append(row['id'])
            
            if expired_ids:
                placeholders = ','.join(['?' for _ in expired_ids])
                self.db_connection.execute(f"DELETE FROM memories WHERE id IN ({placeholders})", expired_ids)
                self.db_connection.commit()
                
                # Remove from cache
                for memory_id in expired_ids:
                    if memory_id in self.memory_cache:
                        del self.memory_cache[memory_id]
                
                self.logger.info(f"Cleaned up {len(expired_ids)} expired memories")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup expired memories: {e}")
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        try:
            # Test database connection
            cursor = self.db_connection.execute("SELECT COUNT(*) FROM memories")
            count = cursor.fetchone()[0]
            
            # Test memory operations
            test_key = "health_check_test"
            await self.store_memory("system", test_key, {"test": True})
            result = await self.retrieve_memory("system", test_key)
            await self.delete_memory("system", test_key)
            
            return result is not None and result.get("test") is True
            
        except Exception as e:
            self.logger.error(f"Memory manager health check failed: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Memory Manager MCP service...")
        
        if self.db_connection:
            self.db_connection.close()
        
        self.memory_cache.clear()
        self.logger.info("Memory Manager MCP service cleanup completed")

# PlexMovieAutomator/requirements.txt - ULTRA-MINIMAL NGC Models Subtitle Pipeline

# ONLY Essential subtitle processing dependencies for NGC Models OCR
torch>=2.9.0               # NGC Models inference engine (CUDA 12.8 support)
torchvision>=0.24.0         # NGC Models computer vision components  
onnxruntime-gpu>=1.20.0     # NGC Models ONNX runtime with TensorRT acceleration
pysrt>=1.1.2                # SRT subtitle file generation
Pillow>=10.0.0              # Image processing for subtitle frames
opencv-python>=********     # Computer vision processing
numpy>=1.24.3               # Array operations for image data
#!/usr/bin/env python3
"""
Step 3b: Quality Assessment System for PlexMovieAutomator
Determines when to use NGC OCR vs BDSup2Sub based on quality metrics
"""

import os
import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import logging
from pathlib import Path

@dataclass
class QualityMetrics:
    """Quality assessment metrics for subtitle extraction"""
    confidence_score: float
    text_clarity: float
    region_count: int
    consistency_score: float
    extraction_method: str
    needs_enhancement: bool

class SubtitleQualityAssessor:
    """
    Assess the quality of subtitle extraction results and determine
    if NGC OCR enhancement is needed
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Quality thresholds
        self.confidence_threshold = 0.7
        self.clarity_threshold = 0.6
        self.consistency_threshold = 0.5
        
        # BDSup2Sub quality indicators
        self.bdsup2sub_success_indicators = [
            "Successfully extracted",
            "OCR completed",
            "Text regions found"
        ]
        
        self.bdsup2sub_failure_indicators = [
            "No text found",
            "Low confidence",
            "Extraction failed",
            "Poor quality"
        ]
    
    def assess_bdsup2sub_result(self, log_output: str, extracted_text: List[str]) -> QualityMetrics:
        """
        Assess the quality of BDSup2Sub extraction results
        
        Args:
            log_output: BDSup2Sub log output
            extracted_text: List of extracted text strings
            
        Returns:
            Quality metrics for the extraction
        """
        confidence_score = self._calculate_bdsup2sub_confidence(log_output)
        text_clarity = self._assess_text_clarity(extracted_text)
        region_count = len(extracted_text)
        consistency_score = self._assess_text_consistency(extracted_text)
        
        # Determine if enhancement is needed
        needs_enhancement = (
            confidence_score < self.confidence_threshold or
            text_clarity < self.clarity_threshold or
            consistency_score < self.consistency_threshold or
            region_count == 0
        )
        
        return QualityMetrics(
            confidence_score=confidence_score,
            text_clarity=text_clarity,
            region_count=region_count,
            consistency_score=consistency_score,
            extraction_method="BDSup2Sub",
            needs_enhancement=needs_enhancement
        )
    
    def _calculate_bdsup2sub_confidence(self, log_output: str) -> float:
        """Calculate confidence based on BDSup2Sub log output"""
        confidence = 0.5  # Default neutral confidence
        
        # Check for success indicators
        success_count = sum(1 for indicator in self.bdsup2sub_success_indicators 
                          if indicator.lower() in log_output.lower())
        
        # Check for failure indicators
        failure_count = sum(1 for indicator in self.bdsup2sub_failure_indicators 
                          if indicator.lower() in log_output.lower())
        
        # Adjust confidence based on indicators
        confidence += (success_count * 0.2) - (failure_count * 0.3)
        
        return max(0.0, min(1.0, confidence))
    
    def _assess_text_clarity(self, extracted_text: List[str]) -> float:
        """Assess the clarity of extracted text"""
        if not extracted_text:
            return 0.0
        
        clarity_scores = []
        
        for text in extracted_text:
            # Check for common OCR errors
            error_indicators = ['?', '■', '□', '▪', '●', '○']
            error_count = sum(text.count(indicator) for indicator in error_indicators)
            
            # Check for reasonable text characteristics
            has_letters = any(c.isalpha() for c in text)
            reasonable_length = 3 <= len(text.strip()) <= 200
            no_excessive_special_chars = len([c for c in text if not c.isalnum() and c != ' ']) < len(text) * 0.3
            
            # Calculate clarity score for this text
            score = 1.0
            score -= (error_count * 0.1)  # Penalize OCR artifacts
            score *= 1.0 if has_letters else 0.3
            score *= 1.0 if reasonable_length else 0.5
            score *= 1.0 if no_excessive_special_chars else 0.6
            
            clarity_scores.append(max(0.0, score))
        
        return np.mean(clarity_scores) if clarity_scores else 0.0
    
    def _assess_text_consistency(self, extracted_text: List[str]) -> float:
        """Assess consistency of extracted text"""
        if len(extracted_text) < 2:
            return 1.0  # Single text is consistent by default
        
        # Check for consistent formatting
        lengths = [len(text.strip()) for text in extracted_text]
        avg_length = np.mean(lengths)
        length_variance = np.var(lengths) / (avg_length + 1)
        
        # Check for consistent character types
        has_similar_content = True
        for text in extracted_text:
            if not text.strip():
                has_similar_content = False
                break
        
        consistency = 1.0 - min(0.5, length_variance)
        consistency *= 1.0 if has_similar_content else 0.6
        
        return consistency

class NGC_QualityEnhancer:
    """
    Use NGC OCR models to enhance or validate subtitle extraction
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # TensorRT engine paths (Step 5 optimized engines)
        self.ocdnet_engine = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocdnet_v2.4_optimized.trt")
        self.ocrnet_engine = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocrnet_v2.1.1_optimized.trt")
        
        # Initialize TensorRT-based NGC text detector
        self.text_detector = None
        self._initialize_models()
    
    def _initialize_models(self):
        """Initialize NGC OCR models using TensorRT engines only"""
        try:
            # Import our new TensorRT-only text detector
            from step3_text_detection import NGC_TextDetector
            
            # Initialize text detector with TensorRT engine
            if self.ocdnet_engine.exists():
                self.text_detector = NGC_TextDetector(str(self.ocdnet_engine))
                self.logger.info("NGC TensorRT text detector loaded")
            else:
                self.logger.error(f"TensorRT engine not found: {self.ocdnet_engine}")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize NGC TensorRT models: {e}")
            # Models will remain None, system will gracefully degrade
    
    def enhance_subtitle_extraction(self, video_path: str, 
                                  bdsup2sub_result: List[str],
                                  quality_metrics: QualityMetrics) -> Tuple[List[str], QualityMetrics]:
        """
        Use NGC OCR to enhance subtitle extraction when quality is poor
        
        Args:
            video_path: Path to video file
            bdsup2sub_result: Original BDSup2Sub extraction result
            quality_metrics: Quality assessment of original result
            
        Returns:
            Enhanced text extraction and updated quality metrics
        """
        if not quality_metrics.needs_enhancement:
            return bdsup2sub_result, quality_metrics
        
        if not self.text_detector:
            self.logger.warning("NGC TensorRT detector not available, returning original result")
            return bdsup2sub_result, quality_metrics
        
        try:
            # Extract key frames from video
            frames = self._extract_subtitle_frames(video_path)
            
            if not frames:
                return bdsup2sub_result, quality_metrics
            
            # Run NGC OCR on frames
            ngc_results = []
            for frame_data in frames:
                frame, timestamp = frame_data
                detected_text = self._process_frame_with_ngc(frame)
                if detected_text:
                    ngc_results.extend(detected_text)
            
            # Merge results intelligently
            enhanced_results = self._merge_extraction_results(bdsup2sub_result, ngc_results)
            
            # Assess enhanced quality
            enhanced_metrics = QualityMetrics(
                confidence_score=min(1.0, quality_metrics.confidence_score + 0.3),
                text_clarity=0.8,  # NGC OCR typically has good clarity
                region_count=len(enhanced_results),
                consistency_score=0.9,  # NGC OCR is typically consistent
                extraction_method="BDSup2Sub + NGC OCR",
                needs_enhancement=False
            )
            
            return enhanced_results, enhanced_metrics
            
        except Exception as e:
            self.logger.error(f"NGC enhancement failed: {e}")
            return bdsup2sub_result, quality_metrics
    
    def _extract_subtitle_frames(self, video_path: str, max_frames: int = 10) -> List[Tuple[np.ndarray, float]]:
        """Extract key frames where subtitles are likely present"""
        frames = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # Sample frames from middle portion of video (where subtitles are common)
            start_frame = total_frames // 4
            end_frame = total_frames * 3 // 4
            
            frame_interval = max(1, (end_frame - start_frame) // max_frames)
            
            for frame_num in range(start_frame, end_frame, frame_interval):
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_num)
                ret, frame = cap.read()
                
                if ret:
                    timestamp = frame_num / fps if fps > 0 else 0
                    frames.append((frame, timestamp))
                
                if len(frames) >= max_frames:
                    break
            
            cap.release()
            
        except Exception as e:
            self.logger.error(f"Failed to extract frames: {e}")
        
        return frames
    
    def _process_frame_with_ngc(self, frame: np.ndarray) -> List[str]:
        """Process a single frame with NGC TensorRT text detector"""
        try:
            if not self.text_detector:
                return []
            
            # Use our TensorRT-based text detector
            regions = self.text_detector.detect_subtitle_regions(frame)
            
            # Extract text from detected regions (simplified)
            detected_texts = []
            for region in regions:
                if region.text_content:
                    detected_texts.append(region.text_content)
                else:
                    # For regions without OCR text, use placeholder
                    detected_texts.append(f"[Text detected at {region.bbox}]")
            
            return detected_texts
            
        except Exception as e:
            self.logger.error(f"NGC frame processing failed: {e}")
            return []
    
    def _merge_extraction_results(self, bdsup2sub_results: List[str], 
                                ngc_results: List[str]) -> List[str]:
        """Intelligently merge BDSup2Sub and NGC OCR results"""
        if not bdsup2sub_results:
            return ngc_results
        
        if not ngc_results:
            return bdsup2sub_results
        
        # For now, prefer NGC results if available
        # In practice, would implement sophisticated merging logic
        return ngc_results if ngc_results else bdsup2sub_results

def assess_subtitle_quality(bdsup2sub_log: str, extracted_text: List[str]) -> QualityMetrics:
    """
    Convenience function to assess subtitle extraction quality
    
    Args:
        bdsup2sub_log: Log output from BDSup2Sub
        extracted_text: Extracted text strings
        
    Returns:
        Quality metrics
    """
    assessor = SubtitleQualityAssessor()
    return assessor.assess_bdsup2sub_result(bdsup2sub_log, extracted_text)

def enhance_if_needed(video_path: str, bdsup2sub_result: List[str], 
                     bdsup2sub_log: str) -> Tuple[List[str], QualityMetrics]:
    """
    Main integration function for existing pipeline
    
    Args:
        video_path: Path to video file
        bdsup2sub_result: Original extraction result
        bdsup2sub_log: BDSup2Sub log output
        
    Returns:
        Enhanced results and quality metrics
    """
    # Assess quality
    quality_metrics = assess_subtitle_quality(bdsup2sub_log, bdsup2sub_result)
    
    # Enhance if needed
    if quality_metrics.needs_enhancement:
        enhancer = NGC_QualityEnhancer()
        return enhancer.enhance_subtitle_extraction(video_path, bdsup2sub_result, quality_metrics)
    
    return bdsup2sub_result, quality_metrics

if __name__ == "__main__":
    print("Quality Assessment System - Step 3b")
    print("Ready for integration with PlexMovieAutomator pipeline")

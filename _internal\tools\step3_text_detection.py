#!/usr/bin/env python3
"""
Step 3: Text Detection Integration for PlexMovieAutomator
Subtitle Region Localization with NGC OCDNet Model

This integrates NVIDIA NGC OCR models into the existing BDSup2Sub pipeline
to enhance subtitle extraction accuracy and handle edge cases.
"""

import os
import cv2
import numpy as np
from typing import List, Tuple, Optional
from dataclasses import dataclass
import logging

@dataclass
class SubtitleRegion:
    """Represents a detected subtitle text region"""
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    confidence: float
    frame_number: int
    timestamp: float
    image_crop: Optional[np.ndarray] = None

class NGC_TextDetector:
    """
    NVIDIA NGC OCDNet Text Detection for Subtitle Regions
    
    This class integrates with the existing PlexMovieAutomator pipeline
    to provide enhanced subtitle detection capabilities.
    """
    
    def __init__(self, model_path: str):
        """
        Initialize the NGC text detector with TensorRT engine
        
        Args:
            model_path: Path to the NGC OCDNet TensorRT engine (.trt file)
        """
        self.model_path = model_path
        self.logger = logging.getLogger(__name__)
        
        # Model inference setup
        self.model = None
        self.input_size = (720, 1280)  # Standard HD resolution for processing
        self.confidence_threshold = 0.5
        self.min_region_area = 100  # Minimum pixel area for subtitle regions
        
        # Subtitle-specific filtering
        self.subtitle_region_height_ratio = 0.3  # Bottom 30% of frame for subtitles
        self.max_subtitle_width_ratio = 0.9     # Max 90% frame width
        self.min_subtitle_width_ratio = 0.1     # Min 10% frame width
        
        self._load_model()
    
    def _load_model(self):
        """Load the NGC OCDNet model - TensorRT only for maximum performance"""
        try:
            if not self.model_path.endswith('.trt'):
                raise ValueError(f"Only TensorRT engines supported. Got: {self.model_path}")
            
            self._load_tensorrt_model()
            self.logger.info(f"NGC OCDNet TensorRT engine loaded: {self.model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load NGC TensorRT model: {e}")
            raise
    
    def _load_tensorrt_model(self):
        """Load TensorRT engine for maximum performance"""
        # Add TensorRT lib to PATH for DLL loading
        tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
        current_path = os.environ.get('PATH', '')
        if tensorrt_lib_path not in current_path:
            os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"
        
        import tensorrt as trt
        
        # Create TensorRT logger and runtime
        self.trt_logger = trt.Logger(trt.Logger.WARNING)
        self.trt_runtime = trt.Runtime(self.trt_logger)
        
        # Load engine
        with open(self.model_path, 'rb') as f:
            engine_data = f.read()
        
        self.engine = self.trt_runtime.deserialize_cuda_engine(engine_data)
        self.context = self.engine.create_execution_context()
        
        self.logger.info("TensorRT engine loaded successfully")
    
    def preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """
        Preprocess video frame for OCDNet detection
        
        Args:
            frame: Input video frame (BGR format)
            
        Returns:
            Preprocessed image tensor
        """
        # Resize to standard processing size while maintaining aspect ratio
        height, width = frame.shape[:2]
        target_height, target_width = self.input_size
        
        # Calculate scaling factor
        scale_x = target_width / width
        scale_y = target_height / height
        scale = min(scale_x, scale_y)
        
        # Resize frame
        new_width = int(width * scale)
        new_height = int(height * scale)
        resized = cv2.resize(frame, (new_width, new_height))
        
        # Pad to target size
        padded = np.zeros((target_height, target_width, 3), dtype=np.uint8)
        padded[:new_height, :new_width] = resized
        
        # Convert to RGB and normalize
        rgb_frame = cv2.cvtColor(padded, cv2.COLOR_BGR2RGB)
        normalized = rgb_frame.astype(np.float32) / 255.0
        
        # Create batch dimension and transpose for model input
        batch_input = np.transpose(normalized, (2, 0, 1))  # HWC to CHW
        batch_input = np.expand_dims(batch_input, axis=0)  # Add batch dimension
        
        return batch_input, scale
    
    def detect_subtitle_regions(self, frame: np.ndarray, frame_number: int = 0, 
                              timestamp: float = 0.0) -> List[SubtitleRegion]:
        """
        Detect subtitle text regions in a video frame
        
        Args:
            frame: Input video frame
            frame_number: Frame number in video
            timestamp: Timestamp in seconds
            
        Returns:
            List of detected subtitle regions
        """
        # Preprocess frame
        model_input, scale = self.preprocess_frame(frame)
        
        # Run TensorRT inference
        detection_map = self._run_tensorrt_inference(model_input)
        
        # Post-process detections
        regions = self._post_process_detections(
            detection_map, frame, scale, frame_number, timestamp
        )
        
        return regions
    
    def _run_tensorrt_inference(self, model_input: np.ndarray) -> np.ndarray:
        """Run inference using TensorRT engine"""
        import pycuda.driver as cuda
        import pycuda.autoinit
        
        # Allocate GPU memory
        input_gpu = cuda.mem_alloc(model_input.nbytes)
        output_size = self.input_size[0] * self.input_size[1] * 4  # Assuming float32 output
        output_gpu = cuda.mem_alloc(output_size)
        
        # Copy input to GPU
        cuda.memcpy_htod(input_gpu, model_input)
        
        # Run inference
        self.context.execute_v2([int(input_gpu), int(output_gpu)])
        
        # Copy output back to CPU
        output = np.empty((1, 1, self.input_size[0], self.input_size[1]), dtype=np.float32)
        cuda.memcpy_dtoh(output, output_gpu)
        
        return output[0, 0]  # Remove batch and channel dimensions
    
    def _post_process_detections(self, detection_map: np.ndarray, original_frame: np.ndarray,
                               scale: float, frame_number: int, timestamp: float) -> List[SubtitleRegion]:
        """
        Post-process OCDNet output to extract subtitle regions
        
        Args:
            detection_map: Raw detection map from model
            original_frame: Original input frame
            scale: Scaling factor used in preprocessing
            frame_number: Frame number
            timestamp: Timestamp
            
        Returns:
            List of detected subtitle regions
        """
        # Apply threshold to get binary mask
        binary_mask = (detection_map > self.confidence_threshold).astype(np.uint8) * 255
        
        # Find contours
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        regions = []
        frame_height, frame_width = original_frame.shape[:2]
        
        # Filter contours for subtitle characteristics
        for contour in contours:
            # Get bounding box
            x, y, w, h = cv2.boundingRect(contour)
            
            # Scale back to original frame coordinates
            orig_x = int(x / scale)
            orig_y = int(y / scale)
            orig_w = int(w / scale)
            orig_h = int(h / scale)
            
            # Apply subtitle-specific filters
            if self._is_valid_subtitle_region(orig_x, orig_y, orig_w, orig_h, frame_width, frame_height):
                # Calculate confidence based on contour area
                confidence = cv2.contourArea(contour) / (w * h) if w * h > 0 else 0
                
                # Extract image crop
                crop = original_frame[orig_y:orig_y+orig_h, orig_x:orig_x+orig_w]
                
                region = SubtitleRegion(
                    bbox=(orig_x, orig_y, orig_w, orig_h),
                    confidence=confidence,
                    frame_number=frame_number,
                    timestamp=timestamp,
                    image_crop=crop
                )
                regions.append(region)
        
        # Sort by confidence and position (bottom regions first)
        regions.sort(key=lambda r: (r.bbox[1], -r.confidence))
        
        return regions
    
    def _is_valid_subtitle_region(self, x: int, y: int, w: int, h: int, 
                                frame_width: int, frame_height: int) -> bool:
        """
        Check if detected region matches subtitle characteristics
        
        Args:
            x, y, w, h: Bounding box coordinates
            frame_width, frame_height: Frame dimensions
            
        Returns:
            True if region is likely a subtitle
        """
        # Check minimum area
        if w * h < self.min_region_area:
            return False
        
        # Check if in subtitle region (bottom portion of frame)
        subtitle_y_threshold = frame_height * (1 - self.subtitle_region_height_ratio)
        if y < subtitle_y_threshold:
            return False
        
        # Check width constraints
        width_ratio = w / frame_width
        if width_ratio < self.min_subtitle_width_ratio or width_ratio > self.max_subtitle_width_ratio:
            return False
        
        # Check aspect ratio (subtitles are typically wider than tall)
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio < 2.0:  # Minimum 2:1 width to height ratio
            return False
        
        return True

def integrate_with_bdsup2sub_pipeline(video_path: str, subtitle_track: int = 0) -> List[SubtitleRegion]:
    """
    Integration function for existing BDSup2Sub pipeline
    
    This function can be called from 05_subtitle_handler.py to enhance
    subtitle extraction with NGC OCR detection.
    
    Args:
        video_path: Path to video file
        subtitle_track: Subtitle track number
        
    Returns:
        List of detected subtitle regions
    """
    # Initialize NGC text detector
    model_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\models\ngc_ocr\onnx\ocdnet.onnx"
    detector = NGC_TextDetector(model_path, use_tensorrt=False)
    
    # Open video
    cap = cv2.VideoCapture(video_path)
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    all_regions = []
    frame_number = 0
    
    # Process frames (sample every 0.5 seconds to avoid redundancy)
    frame_skip = int(fps * 0.5) if fps > 0 else 15
    
    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        
        if frame_number % frame_skip == 0:
            timestamp = frame_number / fps if fps > 0 else 0
            
            # Detect subtitle regions
            regions = detector.detect_subtitle_regions(frame, frame_number, timestamp)
            all_regions.extend(regions)
        
        frame_number += 1
    
    cap.release()
    return all_regions

# Integration points for existing pipeline
def enhance_mkv_processor():
    """
    Enhancement for 03_mkv_processor.py
    Add NGC OCR detection as a fallback for problematic subtitle tracks
    """
    pass

def enhance_subtitle_handler():
    """
    Enhancement for 05_subtitle_handler.py
    Use NGC OCR to validate and improve BDSup2Sub extraction results
    """
    pass

if __name__ == "__main__":
    # Test the detection system
    print("NGC OCR Text Detection - Step 3 Implementation")
    print("Integration ready for PlexMovieAutomator pipeline")

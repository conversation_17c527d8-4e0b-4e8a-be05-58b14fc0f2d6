#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/sequential_thinking.py

Sequential Thinking MCP Server Integration
Breaks down complex movie processing tasks into manageable steps with transparent tracking.
"""

import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
from dataclasses import dataclass, asdict
from enum import Enum

class StepStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"

@dataclass
class ProcessingStep:
    """Represents a single step in a sequential processing task."""
    id: str
    name: str
    description: str
    status: StepStatus = StepStatus.PENDING
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    duration_seconds: float = 0.0
    error_message: Optional[str] = None
    output_data: Dict[str, Any] = None
    dependencies: List[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.output_data is None:
            self.output_data = {}
        if self.dependencies is None:
            self.dependencies = []

@dataclass
class SequentialTask:
    """Represents a complete sequential processing task."""
    id: str
    name: str
    description: str
    movie_id: str
    pipeline_stage: str
    steps: List[ProcessingStep]
    status: StepStatus = StepStatus.PENDING
    created_time: str = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    total_duration: float = 0.0
    current_step_index: int = 0
    
    def __post_init__(self):
        if self.created_time is None:
            self.created_time = datetime.now(timezone.utc).isoformat()

class SequentialThinkingMCP:
    """
    MCP service for breaking down complex movie processing tasks into sequential steps.
    Provides transparent tracking and intelligent error handling.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        
        # Task management
        self.active_tasks: Dict[str, SequentialTask] = {}
        self.completed_tasks: Dict[str, SequentialTask] = {}
        self.task_history = []
        
        # Configuration
        self.max_concurrent_tasks = config.additional_config.get("max_concurrent_tasks", 5) if config else 5
        self.max_steps_per_task = config.additional_config.get("max_steps", 50) if config else 50
        self.step_timeout = config.additional_config.get("step_timeout", 60) if config else 60
        self.auto_breakdown = config.additional_config.get("auto_breakdown", True) if config else True
        
        # Predefined task templates for common pipeline operations
        self.task_templates = self._initialize_task_templates()
    
    async def initialize(self) -> bool:
        """Initialize the Sequential Thinking MCP service."""
        try:
            self.logger.info("Initializing Sequential Thinking MCP service...")
            
            # Load any persisted tasks
            await self._load_persisted_tasks()
            
            self.logger.info("Sequential Thinking MCP service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Sequential Thinking MCP: {e}")
            return False
    
    def _initialize_task_templates(self) -> Dict[str, Dict]:
        """Initialize predefined task templates for common pipeline operations."""
        return {
            "mkv_processing": {
                "name": "MKV File Processing",
                "description": "Complete MKV file processing workflow",
                "steps": [
                    {
                        "id": "validate_input",
                        "name": "Validate Input File",
                        "description": "Check if MKV file exists and is accessible"
                    },
                    {
                        "id": "analyze_tracks",
                        "name": "Analyze Media Tracks", 
                        "description": "Inspect video, audio, and subtitle tracks"
                    },
                    {
                        "id": "clean_metadata",
                        "name": "Clean Metadata",
                        "description": "Remove unwanted metadata and tags"
                    },
                    {
                        "id": "select_tracks",
                        "name": "Select Best Tracks",
                        "description": "Choose optimal video and audio tracks"
                    },
                    {
                        "id": "extract_subtitles",
                        "name": "Extract Subtitles",
                        "description": "Extract all subtitle tracks for processing"
                    },
                    {
                        "id": "create_clean_mkv",
                        "name": "Create Clean MKV",
                        "description": "Generate clean video/audio-only MKV file"
                    },
                    {
                        "id": "validate_output",
                        "name": "Validate Output",
                        "description": "Verify the processed MKV file integrity"
                    }
                ]
            },
            "subtitle_processing": {
                "name": "Subtitle Processing",
                "description": "Complete subtitle handling workflow",
                "steps": [
                    {
                        "id": "inventory_subtitles",
                        "name": "Inventory Subtitle Files",
                        "description": "Catalog all extracted subtitle files"
                    },
                    {
                        "id": "identify_formats",
                        "name": "Identify Subtitle Formats",
                        "description": "Determine subtitle formats (SRT, PGS, SUP, etc.)"
                    },
                    {
                        "id": "ocr_image_subs",
                        "name": "OCR Image Subtitles",
                        "description": "Convert PGS/SUP subtitles to SRT using OCR"
                    },
                    {
                        "id": "validate_srt",
                        "name": "Validate SRT Files",
                        "description": "Check SRT files for formatting and timing issues"
                    },
                    {
                        "id": "select_final_subs",
                        "name": "Select Final Subtitles",
                        "description": "Choose best subtitle tracks for final muxing"
                    }
                ]
            },
            "download_monitoring": {
                "name": "Download Monitoring",
                "description": "Monitor and organize completed downloads",
                "steps": [
                    {
                        "id": "scan_download_dir",
                        "name": "Scan Download Directory",
                        "description": "Check for newly completed downloads"
                    },
                    {
                        "id": "identify_movies",
                        "name": "Identify Movie Files",
                        "description": "Find main movie files in download folders"
                    },
                    {
                        "id": "detect_resolution",
                        "name": "Detect Resolution",
                        "description": "Determine video resolution (4K, 1080p, etc.)"
                    },
                    {
                        "id": "organize_files",
                        "name": "Organize Files",
                        "description": "Move and rename files to proper structure"
                    },
                    {
                        "id": "update_status",
                        "name": "Update Movie Status",
                        "description": "Update movie processing status in database"
                    }
                ]
            }
        }
    
    async def create_task(self, task_type: str, movie_id: str, pipeline_stage: str,
                         custom_steps: List[Dict] = None) -> Optional[str]:
        """
        Create a new sequential processing task.
        
        Args:
            task_type: Type of task (must match a template or 'custom')
            movie_id: ID of the movie being processed
            pipeline_stage: Current pipeline stage
            custom_steps: Custom step definitions (if task_type is 'custom')
            
        Returns:
            Task ID if successful, None otherwise
        """
        try:
            # Generate unique task ID
            task_id = f"task_{movie_id}_{pipeline_stage}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Get step definitions
            if task_type in self.task_templates:
                template = self.task_templates[task_type]
                step_defs = template["steps"]
                task_name = template["name"]
                task_description = template["description"]
            elif task_type == "custom" and custom_steps:
                step_defs = custom_steps
                task_name = f"Custom Task for {movie_id}"
                task_description = f"Custom processing task for movie {movie_id}"
            else:
                self.logger.error(f"Unknown task type: {task_type}")
                return None
            
            # Create processing steps
            steps = []
            for i, step_def in enumerate(step_defs):
                step = ProcessingStep(
                    id=step_def["id"],
                    name=step_def["name"],
                    description=step_def["description"],
                    dependencies=step_def.get("dependencies", [])
                )
                steps.append(step)
            
            # Create the task
            task = SequentialTask(
                id=task_id,
                name=task_name,
                description=task_description,
                movie_id=movie_id,
                pipeline_stage=pipeline_stage,
                steps=steps
            )
            
            # Store the task
            self.active_tasks[task_id] = task
            
            self.logger.info(f"Created sequential task {task_id} with {len(steps)} steps")
            return task_id
            
        except Exception as e:
            self.logger.error(f"Failed to create sequential task: {e}")
            return None
    
    async def start_task(self, task_id: str) -> bool:
        """Start executing a sequential task."""
        if task_id not in self.active_tasks:
            self.logger.error(f"Task {task_id} not found")
            return False
        
        task = self.active_tasks[task_id]
        
        try:
            task.status = StepStatus.IN_PROGRESS
            task.start_time = datetime.now(timezone.utc).isoformat()
            
            self.logger.info(f"Started sequential task: {task.name}")
            
            # Start processing steps
            asyncio.create_task(self._execute_task_steps(task))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start task {task_id}: {e}")
            task.status = StepStatus.FAILED
            return False
    
    async def _execute_task_steps(self, task: SequentialTask):
        """Execute all steps in a sequential task."""
        try:
            for i, step in enumerate(task.steps):
                task.current_step_index = i
                
                # Check dependencies
                if not await self._check_step_dependencies(step, task):
                    step.status = StepStatus.SKIPPED
                    continue
                
                # Execute the step
                success = await self._execute_step(step, task)
                
                if not success and step.retry_count < step.max_retries:
                    # Retry the step
                    step.retry_count += 1
                    self.logger.info(f"Retrying step {step.name} (attempt {step.retry_count})")
                    success = await self._execute_step(step, task)
                
                if not success:
                    # Step failed, mark task as failed
                    task.status = StepStatus.FAILED
                    task.end_time = datetime.now(timezone.utc).isoformat()
                    self.logger.error(f"Task {task.name} failed at step: {step.name}")
                    return
            
            # All steps completed successfully
            task.status = StepStatus.COMPLETED
            task.end_time = datetime.now(timezone.utc).isoformat()
            
            # Calculate total duration
            if task.start_time and task.end_time:
                start_dt = datetime.fromisoformat(task.start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(task.end_time.replace('Z', '+00:00'))
                task.total_duration = (end_dt - start_dt).total_seconds()
            
            # Move to completed tasks
            self.completed_tasks[task.id] = task
            del self.active_tasks[task.id]
            
            self.logger.info(f"Task {task.name} completed successfully in {task.total_duration:.2f}s")
            
        except Exception as e:
            task.status = StepStatus.FAILED
            task.end_time = datetime.now(timezone.utc).isoformat()
            self.logger.error(f"Task execution failed: {e}")
    
    async def _check_step_dependencies(self, step: ProcessingStep, task: SequentialTask) -> bool:
        """Check if all dependencies for a step are satisfied."""
        if not step.dependencies:
            return True
        
        for dep_id in step.dependencies:
            # Find the dependency step
            dep_step = next((s for s in task.steps if s.id == dep_id), None)
            if not dep_step:
                self.logger.error(f"Dependency {dep_id} not found for step {step.id}")
                return False
            
            if dep_step.status != StepStatus.COMPLETED:
                self.logger.warning(f"Dependency {dep_id} not completed for step {step.id}")
                return False
        
        return True
    
    async def _execute_step(self, step: ProcessingStep, task: SequentialTask) -> bool:
        """Execute a single processing step."""
        try:
            step.status = StepStatus.IN_PROGRESS
            step.start_time = datetime.now(timezone.utc).isoformat()
            
            self.logger.info(f"Executing step: {step.name}")
            
            # This is where the actual step logic would be implemented
            # For now, we'll simulate step execution
            await asyncio.sleep(1)  # Simulate processing time
            
            # Mark step as completed
            step.status = StepStatus.COMPLETED
            step.end_time = datetime.now(timezone.utc).isoformat()
            
            # Calculate duration
            if step.start_time and step.end_time:
                start_dt = datetime.fromisoformat(step.start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(step.end_time.replace('Z', '+00:00'))
                step.duration_seconds = (end_dt - start_dt).total_seconds()
            
            self.logger.info(f"Step {step.name} completed in {step.duration_seconds:.2f}s")
            return True
            
        except Exception as e:
            step.status = StepStatus.FAILED
            step.end_time = datetime.now(timezone.utc).isoformat()
            step.error_message = str(e)
            
            self.logger.error(f"Step {step.name} failed: {e}")
            return False
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """Get the current status of a task."""
        task = self.active_tasks.get(task_id) or self.completed_tasks.get(task_id)
        if not task:
            return None
        
        return {
            "id": task.id,
            "name": task.name,
            "status": task.status.value,
            "movie_id": task.movie_id,
            "pipeline_stage": task.pipeline_stage,
            "current_step": task.current_step_index,
            "total_steps": len(task.steps),
            "progress_percentage": (task.current_step_index / len(task.steps)) * 100,
            "start_time": task.start_time,
            "end_time": task.end_time,
            "duration": task.total_duration,
            "steps": [
                {
                    "id": step.id,
                    "name": step.name,
                    "status": step.status.value,
                    "duration": step.duration_seconds,
                    "error": step.error_message
                }
                for step in task.steps
            ]
        }
    
    async def get_all_tasks_status(self) -> Dict[str, Any]:
        """Get status of all tasks."""
        return {
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "tasks": {
                task_id: await self.get_task_status(task_id)
                for task_id in list(self.active_tasks.keys()) + list(self.completed_tasks.keys())
            }
        }
    
    async def _load_persisted_tasks(self):
        """Load any persisted tasks from storage."""
        # This would load tasks from a persistent store
        # For now, we'll just log that we're ready
        self.logger.info("Sequential thinking service ready for task processing")
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        try:
            # Check if we can create a simple test task
            test_task_id = await self.create_task("custom", "test", "health_check", [
                {"id": "test_step", "name": "Test Step", "description": "Health check step"}
            ])
            
            if test_task_id:
                # Clean up test task
                if test_task_id in self.active_tasks:
                    del self.active_tasks[test_task_id]
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Sequential thinking health check failed: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Sequential Thinking MCP service...")
        
        # Cancel any running tasks
        for task in self.active_tasks.values():
            task.status = StepStatus.FAILED
            task.end_time = datetime.now(timezone.utc).isoformat()
        
        self.active_tasks.clear()
        self.logger.info("Sequential Thinking MCP service cleanup completed")

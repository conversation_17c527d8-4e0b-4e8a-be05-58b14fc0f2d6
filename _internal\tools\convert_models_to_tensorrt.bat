@echo off
REM Step 4: Convert NGC OCR Models to TensorRT Engines
REM For maximum performance on RTX 5090

echo Starting TensorRT Model Conversion...
echo.

REM Set PATH to include TensorRT libraries
set PATH=%PATH%;C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib

REM Change to workspace directory
cd /d "C:\Users\<USER>\Videos\PlexMovieAutomator"

echo Converting OCDNet model to TensorRT engine...
C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\bin\trtexec.exe ^
  --onnx="_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx" ^
  --saveEngine="_internal\models\ngc_ocr\tensorrt\ocdnet_v2.4.trt" ^
  --fp16 ^
  --memPoolSize=workspace:8192M ^
  --builderOptimizationLevel=5 ^
  --hardwareCompatibilityLevel=ampere+

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OCDNet conversion failed!
    pause
    exit /b 1
)

echo.
echo Converting OCRNet model to TensorRT engine...
C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\bin\trtexec.exe ^
  --onnx="_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx" ^
  --saveEngine="_internal\models\ngc_ocr\tensorrt\ocrnet_v2.1.1.trt" ^
  --fp16 ^
  --memPoolSize=workspace:8192M ^
  --builderOptimizationLevel=5 ^
  --hardwareCompatibilityLevel=ampere+

if %ERRORLEVEL% NEQ 0 (
    echo ERROR: OCRNet conversion failed!
    pause
    exit /b 1
)

echo.
echo TensorRT Model Conversion Complete!
echo Both OCDNet and OCRNet engines have been optimized for RTX 5090
echo.

REM Test the conversions
echo Testing converted engines...
python "_internal\tools\pipeline_integrator.py"

echo.
echo All done! TensorRT engines are ready for maximum performance.
pause

import os
import subprocess
import xml.etree.ElementTree as ET
import pysrt
import shutil
import json
import threading
import time
import re

import PySimpleGUI as sg
from google.cloud import vision_v1
from google.api_core.exceptions import GoogleAPIError, ResourceExhausted

# ─── CONFIG PERSISTENCE ─────────────────────────────────────────────────────
CONFIG_PATH = os.path.join(os.path.expanduser('~'), '.sup2srt_gui_config.json')

def load_config():
    if os.path.exists(CONFIG_PATH):
        try:
            with open(CONFIG_PATH, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            pass
    return {'jar_path': '', 'creds_path': ''}

def save_config(cfg):
    try:
        with open(CONFIG_PATH, 'w', encoding='utf-8') as f:
            json.dump(cfg, f, indent=2)
    except Exception as e:
        print(f"Failed to save config: {e}")

# ─── RATE LIMITING & OCR SETTINGS ─────────────────────────────────────────────
QUOTA_PER_MINUTE = 1800
BATCH_SIZE = 16
MAX_RETRIES = 3
_tokens = QUOTA_PER_MINUTE
_last_refill = time.time()
_bucket_lock = threading.Lock()

def refill_tokens():
    global _tokens, _last_refill
    now = time.time()
    elapsed = now - _last_refill
    if elapsed <= 0:
        return
    with _bucket_lock:
        _tokens = min(QUOTA_PER_MINUTE, _tokens + QUOTA_PER_MINUTE * (elapsed / 60.0))
    _last_refill = now

def acquire_tokens(n):
    global _tokens
    while True:
        refill_tokens()
        with _bucket_lock:
            if _tokens >= n:
                _tokens -= n
                return
        time.sleep(0.01)

# ─── CHECKPOINTING ────────────────────────────────────────────────────────────
def load_checkpoint(sup_file):
    ck = sup_file + ".checkpoint.json"
    if not os.path.exists(ck):
        return None
    with open(ck, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_checkpoint(sup_file, texts, next_idx):
    ck = sup_file + ".checkpoint.json"
    with open(ck, 'w', encoding='utf-8') as f:
        json.dump({'texts': texts, 'next_index': next_idx}, f, ensure_ascii=False, indent=2)

def clear_checkpoint(sup_file):
    ck = sup_file + ".checkpoint.json"
    if os.path.exists(ck): os.remove(ck)

# ─── SUBTITLE CONVERSION CORE ─────────────────────────────────────────────────
def convert_sup_to_png(sup_file, output_folder, jar_path):
    os.makedirs(output_folder, exist_ok=True)
    base = os.path.splitext(os.path.basename(sup_file))[0]
    xml_out = os.path.join(output_folder, base + ".xml")
    subprocess.run(["java", "-jar", jar_path, "-o", xml_out, sup_file],
                   check=True, capture_output=True, text=True)
    return xml_out


def parse_xml_timestamps(xml_file):
    # 1. Read raw XML text
    with open(xml_file, 'r', encoding='utf-8', errors='ignore') as f:
        data = f.read()

    # 2. Escape any '&' not part of a valid entity
    data = re.sub(r'&(?!amp;|lt;|gt;|quot;|apos;)', '&amp;', data)

    # 3. Parse corrected XML
    tree = ET.ElementTree(ET.fromstring(data))
    root = tree.getroot()

    # 4. Extract timestamps and graphic filenames
    stamps, files = [], []
    for ev in root.findall('.//Event'):
        st = ev.get('InTC')
        et = ev.get('OutTC')
        gr = ev.find('Graphic')
        if st and et and gr is not None and gr.text:
            stamps.append((st, et))
            files.append(gr.text)
    return stamps, files


def ocr_batch(image_paths, client):
    acquire_tokens(len(image_paths))
    requests = []
    for path in image_paths:
        with open(path, 'rb') as f:
            content = f.read()
        requests.append({
            'image': {'content': content},
            'features': [{'type_': vision_v1.Feature.Type.TEXT_DETECTION}]
        })
    for attempt in range(1, MAX_RETRIES + 1):
        try:
            return client.batch_annotate_images(requests=requests).responses
        except ResourceExhausted:
            print(f"Quota exhausted; sleeping 60s (attempt {attempt}/{MAX_RETRIES})")
            time.sleep(60)
        except GoogleAPIError as ge:
            backoff = 2 ** attempt
            print(f"OCR error: {ge}; retrying in {backoff}s")
            time.sleep(backoff)
    print("OCR failed after retries")
    return []


def save_to_srt(texts, stamps, output_path):
    subs = pysrt.SubRipFile()
    for i, ((st, et), txt) in enumerate(zip(stamps, texts), 1):
        subs.append(pysrt.SubRipItem(
            index=i,
            start=pysrt.SubRipTime.from_string(st.replace('.', ',')),
            end=pysrt.SubRipTime.from_string(et.replace('.', ',')),
            text=txt
        ))
    subs.save(output_path, encoding='utf-8')

# ─── GUI & EVENT LOOP ────────────────────────────────────────────────────────
def run_conversion(folder, jar_path, creds_path, window, safe_mode):
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = creds_path
    save_config({'jar_path': jar_path, 'creds_path': creds_path})
    client = vision_v1.ImageAnnotatorClient()

    sup_files = []
    for root, _, files in os.walk(folder):
        for fn in files:
            if fn.lower().endswith('.sup'):
                sup_files.append(os.path.join(root, fn))
    total_sup = len(sup_files)
    print(f"Found {total_sup} .sup files")
    window['-PROG-'].update(0)

    for idx, sup in enumerate(sup_files, 1):
        print(f"Processing file {idx}/{total_sup}: {sup}")
        base = os.path.splitext(os.path.basename(sup))[0]
        out_dir = os.path.join(os.path.dirname(sup), base)
        srt_file = os.path.join(os.path.dirname(sup), base + '.srt')

        if os.path.exists(srt_file):
            print(f"Skipping existing: {srt_file}")
            continue

        xml = convert_sup_to_png(sup, out_dir, jar_path)
        stamps, images = parse_xml_timestamps(xml)
        if not images:
            print("No frames detected; skipping.")
            continue

        if safe_mode:
            ck = load_checkpoint(sup)
            if ck:
                texts = ck['texts']; next_i = ck['next_index']
                if len(texts) < len(images): texts.extend([''] * (len(images) - len(texts)))
            else:
                texts = [''] * len(images); next_i = 0
        else:
            texts = [''] * len(images); next_i = 0

        img_paths = [os.path.join(out_dir, img) for img in images]
        total_img = len(img_paths)
        window['-FILEPROG-'].update(0)

        for offset in range(next_i, total_img, BATCH_SIZE):
            batch = img_paths[offset:offset+BATCH_SIZE]
            if safe_mode:
                print(f"OCR batch {offset+1}-{min(offset+BATCH_SIZE, total_img)}/{total_img}")
            responses = ocr_batch(batch, client)
            for i, resp in enumerate(responses):
                idx_txt = offset + i
                texts[idx_txt] = resp.text_annotations[0].description.replace("\n", " ").strip() if resp and resp.text_annotations else ''

            if safe_mode:
                save_checkpoint(sup, texts, offset + len(batch))
                print(f"Checkpoint saved at {offset + len(batch)}/{total_img}")

            window['-FILEPROG-'].update(min(offset + len(batch), total_img) / total_img)
            window['-PROG-'].update(idx / total_sup)

        save_to_srt(texts, stamps, srt_file)

        if safe_mode:
            clear_checkpoint(sup)
        shutil.rmtree(out_dir, ignore_errors=True)
        print(f"Saved: {srt_file}\n")
        window['-FILEPROG-'].update(1)
        window['-PROG-'].update(idx / total_sup)

    print("All conversions complete!")


def gui_main():
    sg.theme('DarkGrey')
    cfg = load_config()
    layout = [
        [sg.Text('SUP → SRT Converter', font=('Any', 16))],
        [sg.Text('Select parent folder:'), sg.Input(key='-FOLDER-'), sg.FolderBrowse()],
        [sg.Text('BDSup2Sub JAR:'), sg.Input(default_text=cfg.get('jar_path',''), key='-JAR-'), sg.FileBrowse(file_types=(('Jar', '*.jar'),))],
        [sg.Text('Google JSON creds:'), sg.Input(default_text=cfg.get('creds_path',''), key='-CREDS-'), sg.FileBrowse(file_types=(('JSON','*.json'),))],
        [sg.Checkbox('Safe Mode (checkpoint & debug logs)', default=True, key='-SAFE-')],
        [sg.Output(size=(80,20))],
        [sg.Text('Overall progress:'), sg.ProgressBar(1, orientation='h', size=(50,10), key='-PROG-')],
        [sg.Text('Current file progress:'), sg.ProgressBar(1, orientation='h', size=(50,10), key='-FILEPROG-')],
        [sg.Button('Start'), sg.Button('Exit')]
    ]

    window = sg.Window('SUP→SRT GUI', layout, finalize=True)
    while True:
        event, vals = window.read()
        if event in (sg.WIN_CLOSED, 'Exit'):
            break
        if event == 'Start':
            fld, jar, creds, safe = vals['-FOLDER-'], vals['-JAR-'], vals['-CREDS-'], vals['-SAFE-']
            if not fld or not jar or not creds:
                sg.popup_error('Please fill all fields')
                continue
            threading.Thread(target=run_conversion,
                             args=(fld, jar, creds, window, safe),
                             daemon=True).start()
    window.close()

if __name__ == '__main__':
    gui_main()

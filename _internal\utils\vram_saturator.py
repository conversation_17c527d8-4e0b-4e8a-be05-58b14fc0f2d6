#!/usr/bin/env python3
"""
VRAM Saturator for RTX 5090 - Force maximum GPU memory utilization

This module forces the RTX 5090 to use 25GB+ VRAM by allocating large tensors
and processing images in memory-intensive ways.
"""

import logging
import numpy as np
from typing import List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class RTX5090VRAMSaturator:
    """Forces RTX 5090 to use maximum VRAM for OCR processing"""
    
    def __init__(self):
        self.vram_tensors = []
        self.torch_available = False
        self.allocated_vram_gb = 0
        
        # Try to import PyTorch for GPU memory allocation
        try:
            import torch
            if torch.cuda.is_available():
                self.torch = torch
                self.torch_available = True
                self.device = torch.device('cuda:0')
                logger.info("🔥 PyTorch CUDA available for VRAM saturation")
            else:
                logger.warning("PyTorch available but CUDA not detected")
        except ImportError:
            logger.info("PyTorch not available for VRAM saturation")
    
    def saturate_vram(self, target_gb: int = 25) -> bool:
        """
        Allocate large tensors to saturate VRAM to target GB
        Enhanced for EXTREME RTX 5090 utilization
        """
        if not self.torch_available:
            logger.warning("Cannot saturate VRAM - PyTorch CUDA not available")
            return False
        
        try:
            logger.info(f"💥 EXTREME VRAM SATURATION: Allocating {target_gb}GB on RTX 5090")
            
            # Calculate how many tensors we need for EXTREME saturation
            tensor_gb = 1.5  # Each tensor ~1.5GB for more granular control
            num_tensors = int(target_gb / tensor_gb)
            
            # Phase 1: Allocate base tensors
            for i in range(num_tensors):
                try:
                    # Create LARGE tensor (1024x1024x384 floats = ~1.5GB)
                    tensor_size = (1024, 1024, 384)
                    tensor = self.torch.randn(tensor_size, device=self.device, dtype=self.torch.float32)
                    self.vram_tensors.append(tensor)
                    self.allocated_vram_gb += tensor_gb
                    
                    if (i + 1) % 5 == 0:
                        logger.info(f"   📈 Phase 1 - Allocated VRAM tensor {i+1}/{num_tensors} (~{tensor_gb}GB each)")
                    
                    # Force GPU to actually allocate the memory
                    _ = tensor.sum()  # Trigger GPU computation
                    
                except Exception as e:
                    logger.warning(f"Could not allocate tensor {i+1}: {e}")
                    break
            
            # Phase 2: EXTREME saturation with HUGE tensors if we have room
            if self.allocated_vram_gb < target_gb * 0.9:
                remaining_gb = target_gb - self.allocated_vram_gb
                if remaining_gb > 3:  # If we have 3GB+ room, add HUGE tensors
                    try:
                        # Create MASSIVE tensor (2048x2048x512 floats = ~8GB)
                        huge_tensor_size = (2048, 2048, 512)
                        huge_tensor = self.torch.randn(huge_tensor_size, device=self.device, dtype=self.torch.float32)
                        self.vram_tensors.append(huge_tensor)
                        self.allocated_vram_gb += 8.0
                        
                        logger.info(f"   💥 Phase 2 - Allocated HUGE VRAM tensor (~8GB)")
                        _ = huge_tensor.sum()  # Force allocation
                        
                    except Exception as e:
                        logger.info(f"Could not allocate HUGE tensor: {e}")
            
            # Phase 3: Fill remaining space with medium tensors
            remaining_attempts = 5
            while self.allocated_vram_gb < target_gb * 0.95 and remaining_attempts > 0:
                try:
                    # Medium tensor (1536x1536x256 floats = ~3GB)
                    medium_tensor_size = (1536, 1536, 256)
                    medium_tensor = self.torch.randn(medium_tensor_size, device=self.device, dtype=self.torch.float32)
                    self.vram_tensors.append(medium_tensor)
                    self.allocated_vram_gb += 3.0
                    
                    logger.info(f"   🔥 Phase 3 - Allocated medium VRAM tensor (~3GB)")
                    _ = medium_tensor.sum()  # Force allocation
                    
                    remaining_attempts -= 1
                    
                except Exception as e:
                    logger.info(f"Phase 3 tensor allocation failed: {e}")
                    break
            
            logger.info(f"🎯 EXTREME VRAM SATURATION SUCCESS: {self.allocated_vram_gb:.1f}GB allocated on RTX 5090")
            logger.info(f"   💾 Allocated {len(self.vram_tensors)} tensors across 3 phases")
            return self.allocated_vram_gb >= target_gb * 0.8  # 80% success rate
            
        except Exception as e:
            logger.error(f"EXTREME VRAM saturation failed: {e}")
            return False
    
    def create_vram_intensive_images(self, images: List[np.ndarray], scale_factor: int = 4) -> List[np.ndarray]:
        """
        Create VRAM-intensive versions of images by upscaling
        """
        logger.info(f"🔥 Creating VRAM-intensive images with {scale_factor}x upscaling")
        
        vram_intensive_images = []
        for i, img in enumerate(images):
            try:
                # Upscale image to consume more VRAM
                if len(img.shape) == 3:  # Color image
                    h, w, c = img.shape
                    # Use numpy repeat for memory-intensive upscaling
                    upscaled = np.repeat(np.repeat(img, scale_factor, axis=0), scale_factor, axis=1)
                else:  # Grayscale
                    h, w = img.shape
                    upscaled = np.repeat(np.repeat(img, scale_factor, axis=0), scale_factor, axis=1)
                
                vram_intensive_images.append(upscaled)
                
                if (i + 1) % 50 == 0:
                    estimated_mb = upscaled.nbytes / (1024 * 1024)
                    logger.info(f"   📈 Created VRAM-intensive image {i+1}/{len(images)} ({estimated_mb:.1f}MB each)")
                    
            except Exception as e:
                logger.warning(f"Failed to create VRAM-intensive image {i+1}: {e}")
                vram_intensive_images.append(img)  # Use original if upscaling fails
        
        total_vram_mb = sum(img.nbytes for img in vram_intensive_images) / (1024 * 1024)
        logger.info(f"💾 Total VRAM-intensive image data: {total_vram_mb:.1f}MB ({total_vram_mb/1024:.2f}GB)")
        
        return vram_intensive_images
    
    def release_vram(self):
        """Release allocated VRAM tensors"""
        if self.torch_available and self.vram_tensors:
            logger.info(f"🧹 Releasing {len(self.vram_tensors)} VRAM tensors ({self.allocated_vram_gb:.1f}GB)")
            
            for tensor in self.vram_tensors:
                del tensor
            
            self.vram_tensors.clear()
            
            # Force garbage collection
            if hasattr(self.torch.cuda, 'empty_cache'):
                self.torch.cuda.empty_cache()
            
            self.allocated_vram_gb = 0
            logger.info("✅ VRAM tensors released")
    
    def get_vram_status(self) -> dict:
        """Get current VRAM allocation status"""
        status = {
            'torch_available': self.torch_available,
            'allocated_tensors': len(self.vram_tensors),
            'allocated_vram_gb': self.allocated_vram_gb
        }
        
        if self.torch_available:
            try:
                allocated = self.torch.cuda.memory_allocated(self.device) / (1024**3)  # GB
                reserved = self.torch.cuda.memory_reserved(self.device) / (1024**3)   # GB
                status.update({
                    'cuda_allocated_gb': allocated,
                    'cuda_reserved_gb': reserved
                })
            except Exception:
                pass
        
        return status

# Global VRAM saturator instance
vram_saturator = RTX5090VRAMSaturator()

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/notion_pipeline_integration.py

Pipeline-Wide Notion Integration for Plex Movie Automator
Integrates Notion tracking across all pipeline stages with real-time updates, progress tracking, and status management.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field

@dataclass
class NotionMovieEntry:
    """Represents a movie entry in Notion with all its properties."""
    notion_page_id: str
    unique_id: str
    title: str
    year: int = None
    status: str = "Pending Intake"
    quality: str = None
    tmdb_id: int = None
    imdb_id: str = None
    runtime: int = None
    genres: List[str] = field(default_factory=list)
    director: str = None
    cast: List[str] = field(default_factory=list)
    overview: str = None
    added_date: str = None
    completed_date: str = None
    processing_time: float = None
    file_size: float = None
    file_path: str = None
    subtitles: List[str] = field(default_factory=list)
    audio_tracks: List[str] = field(default_factory=list)
    error_count: int = 0
    rating: float = None
    tags: List[str] = field(default_factory=list)
    watch_status: str = "Unwatched"
    notes: str = None
    
    def to_notion_properties(self) -> Dict[str, Any]:
        """Convert to Notion properties format."""
        properties = {}
        
        # Title (required)
        if self.title:
            properties["Title"] = {"title": [{"text": {"content": self.title}}]}
        
        # Basic properties
        if self.year:
            properties["Year"] = {"number": self.year}
        if self.status:
            properties["Status"] = {"select": {"name": self.status}}
        if self.quality:
            properties["Quality"] = {"select": {"name": self.quality}}
        if self.tmdb_id:
            properties["TMDB ID"] = {"number": self.tmdb_id}
        if self.imdb_id:
            properties["IMDB ID"] = {"rich_text": [{"text": {"content": self.imdb_id}}]}
        if self.unique_id:
            properties["Unique ID"] = {"rich_text": [{"text": {"content": self.unique_id}}]}
        if self.runtime:
            properties["Runtime"] = {"number": self.runtime}
        if self.director:
            properties["Director"] = {"rich_text": [{"text": {"content": self.director}}]}
        if self.overview:
            properties["Overview"] = {"rich_text": [{"text": {"content": self.overview}}]}
        if self.file_path:
            properties["File Path"] = {"rich_text": [{"text": {"content": self.file_path}}]}
        if self.processing_time:
            properties["Processing Time"] = {"number": self.processing_time}
        if self.file_size:
            properties["File Size"] = {"number": self.file_size}
        if self.error_count:
            properties["Error Count"] = {"number": self.error_count}
        if self.rating:
            properties["Rating"] = {"number": self.rating}
        if self.notes:
            properties["Notes"] = {"rich_text": [{"text": {"content": self.notes}}]}
        if self.watch_status:
            properties["Watch Status"] = {"select": {"name": self.watch_status}}
        
        # Multi-select properties
        if self.genres:
            properties["Genres"] = {"multi_select": [{"name": genre} for genre in self.genres]}
        if self.cast:
            properties["Cast"] = {"multi_select": [{"name": actor} for actor in self.cast]}
        if self.subtitles:
            properties["Subtitles"] = {"multi_select": [{"name": sub} for sub in self.subtitles]}
        if self.audio_tracks:
            properties["Audio Tracks"] = {"multi_select": [{"name": track} for track in self.audio_tracks]}
        if self.tags:
            properties["Tags"] = {"multi_select": [{"name": tag} for tag in self.tags]}
        
        # Date properties
        if self.added_date:
            properties["Added Date"] = {"date": {"start": self.added_date}}
        if self.completed_date:
            properties["Completed Date"] = {"date": {"start": self.completed_date}}
        
        return properties

class NotionPipelineIntegration:
    """
    Comprehensive Notion integration for the entire pipeline.
    Provides real-time tracking, progress updates, and analytics across all stages.
    """
    
    def __init__(self, mcp_manager, logger: logging.Logger):
        self.mcp_manager = mcp_manager
        self.logger = logger
        self.notion_service = None
        self.movie_entries: Dict[str, NotionMovieEntry] = {}
        self.stage_mappings = {
            "pending_metadata_lookup": "Metadata Lookup",
            "pending_nzb_search": "NZB Search", 
            "nzb_downloaded": "NZB Downloaded",
            "download_initiated_client": "Downloading",
            "mkv_ready_for_processing": "Download Complete",
            "mkv_processing_pending": "MKV Processing",
            "mkv_processing_active": "MKV Processing",
            "subtitle_ocr_pending": "Subtitle Processing",
            "subtitle_processing_active": "Subtitle Processing",
            "final_mux_pending": "Final Mux",
            "final_mux_active": "Final Mux",
            "poster_pending": "Poster & QC",
            "poster_qc_active": "Poster & QC",
            "awaiting_quality_check": "Complete",
            "error_metadata_lookup": "Error",
            "error_nzb_search": "Error",
            "error_nzb_download": "Error",
            "error_organizing_download": "Error",
            "error_mkv_processing": "Error",
            "error_subtitle_processing": "Error",
            "error_final_mux": "Error",
            "error_poster_qc_prep": "Error"
        }
        self.initialized = False
    
    async def initialize(self) -> bool:
        """Initialize the Notion pipeline integration."""
        try:
            self.logger.info("Initializing Notion Pipeline Integration...")
            
            # Get Notion service from MCP manager
            self.notion_service = self.mcp_manager.services.get('notion_database')
            if not self.notion_service:
                self.logger.error("Notion Database service not available")
                return False
            
            # Wait for Notion service to be initialized
            if not self.notion_service.initialized:
                self.logger.info("Waiting for Notion service to initialize...")
                for _ in range(30):  # Wait up to 30 seconds
                    if self.notion_service.initialized:
                        break
                    await asyncio.sleep(1)
                
                if not self.notion_service.initialized:
                    self.logger.error("Notion service failed to initialize")
                    return False
            
            self.initialized = True
            self.logger.info("Notion Pipeline Integration initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Notion Pipeline Integration: {e}")
            return False
    
    async def create_movie_entry(self, movie_data: Dict[str, Any]) -> Optional[str]:
        """Create a new movie entry in Notion and track it locally."""
        if not self.initialized:
            self.logger.error("Notion Pipeline Integration not initialized")
            return None
        
        try:
            # Extract movie information
            unique_id = movie_data.get("unique_id", "")
            title = movie_data.get("cleaned_title", movie_data.get("raw_title_input", "Unknown"))
            year = movie_data.get("year")
            
            # Create NotionMovieEntry
            movie_entry = NotionMovieEntry(
                notion_page_id="",  # Will be set after creation
                unique_id=unique_id,
                title=title,
                year=year,
                status=self._map_status(movie_data.get("status", "pending_metadata_lookup")),
                added_date=datetime.now(timezone.utc).isoformat()
            )
            
            # Add metadata if available
            metadata = movie_data.get("metadata_details", {})
            if metadata:
                movie_entry.tmdb_id = metadata.get("tmdb_id")
                movie_entry.imdb_id = metadata.get("imdb_id")
                movie_entry.runtime = metadata.get("runtime")
                movie_entry.genres = metadata.get("genres", [])
                movie_entry.director = metadata.get("director")
                movie_entry.cast = metadata.get("cast", [])[:5]  # Limit to top 5 cast members
                movie_entry.overview = metadata.get("overview")
            
            # Create entry in Notion
            notion_page_id = await self.notion_service.create_movie_entry(movie_entry.to_notion_properties())
            
            if notion_page_id:
                movie_entry.notion_page_id = notion_page_id
                self.movie_entries[unique_id] = movie_entry
                
                self.logger.info(f"Created Notion entry for movie: {title} (ID: {notion_page_id})")
                return notion_page_id
            else:
                self.logger.error(f"Failed to create Notion entry for movie: {title}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating movie entry in Notion: {e}")
            return None
    
    async def update_movie_status(self, movie_id: str, status: str, additional_data: Dict = None) -> bool:
        """Update a movie's status and additional properties in Notion."""
        if not self.initialized:
            return False
        
        try:
            # Map internal status to Notion status
            notion_status = self._map_status(status)
            
            # Get or create movie entry
            movie_entry = self.movie_entries.get(movie_id)
            if not movie_entry:
                # Try to find existing entry in Notion
                notion_page_id = await self.notion_service._find_movie_by_unique_id(movie_id)
                if notion_page_id:
                    # Create local entry for tracking
                    movie_entry = NotionMovieEntry(
                        notion_page_id=notion_page_id,
                        unique_id=movie_id,
                        title="Unknown",  # Will be updated with additional_data
                        status=notion_status
                    )
                    self.movie_entries[movie_id] = movie_entry
                else:
                    self.logger.warning(f"Movie not found in Notion: {movie_id}")
                    return False
            
            # Update local entry
            movie_entry.status = notion_status
            
            # Update additional properties
            if additional_data:
                self._update_movie_entry_properties(movie_entry, additional_data)
            
            # Update in Notion
            update_data = {"Status": notion_status}
            if additional_data:
                update_data.update(additional_data)
            
            success = await self.notion_service.update_movie_status(movie_id, notion_status, update_data)
            
            if success:
                self.logger.debug(f"Updated movie status in Notion: {movie_id} -> {notion_status}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating movie status in Notion: {e}")
            return False
    
    async def add_processing_event(self, movie_id: str, stage: str, operation: str, 
                                  status: str, message: str, error_details: str = None,
                                  duration: float = None, task_id: str = None) -> bool:
        """Add a processing event to the movie's history in Notion."""
        if not self.initialized:
            return False
        
        try:
            # Get movie entry
            movie_entry = self.movie_entries.get(movie_id)
            if not movie_entry:
                self.logger.warning(f"Movie entry not found for processing event: {movie_id}")
                return False
            
            # Add processing history entry
            history_id = await self.notion_service.add_processing_history(
                movie_id=movie_id,
                notion_page_id=movie_entry.notion_page_id,
                stage=stage,
                operation=operation,
                status=status,
                message=message,
                error_details=error_details,
                duration=duration,
                task_id=task_id
            )
            
            if history_id:
                self.logger.debug(f"Added processing event to Notion: {operation} for {movie_id}")
                
                # Update error count if this is an error
                if status.lower() == "error":
                    movie_entry.error_count += 1
                    await self.update_movie_status(movie_id, movie_entry.status, {"Error Count": movie_entry.error_count})
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error adding processing event to Notion: {e}")
            return False
    
    async def update_movie_completion(self, movie_id: str, file_path: str, 
                                     file_size: float, processing_time: float,
                                     quality: str = None, subtitles: List[str] = None,
                                     audio_tracks: List[str] = None) -> bool:
        """Update movie completion details in Notion."""
        if not self.initialized:
            return False
        
        try:
            movie_entry = self.movie_entries.get(movie_id)
            if not movie_entry:
                return False
            
            # Update local entry
            movie_entry.status = "Complete"
            movie_entry.completed_date = datetime.now(timezone.utc).isoformat()
            movie_entry.file_path = file_path
            movie_entry.file_size = file_size
            movie_entry.processing_time = processing_time / 60  # Convert to minutes
            
            if quality:
                movie_entry.quality = quality
            if subtitles:
                movie_entry.subtitles = subtitles
            if audio_tracks:
                movie_entry.audio_tracks = audio_tracks
            
            # Update in Notion
            update_data = {
                "Status": "Complete",
                "Completed Date": movie_entry.completed_date,
                "File Path": file_path,
                "File Size": file_size,
                "Processing Time": movie_entry.processing_time
            }
            
            if quality:
                update_data["Quality"] = quality
            if subtitles:
                update_data["Subtitles"] = subtitles
            if audio_tracks:
                update_data["Audio Tracks"] = audio_tracks
            
            success = await self.notion_service.update_movie_status(movie_id, "Complete", update_data)
            
            if success:
                self.logger.info(f"Updated movie completion in Notion: {movie_entry.title}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating movie completion in Notion: {e}")
            return False
    
    async def get_movie_statistics(self) -> Dict[str, Any]:
        """Get comprehensive movie statistics from Notion."""
        if not self.initialized:
            return {}
        
        try:
            stats = {
                "total_movies": len(self.movie_entries),
                "by_status": {},
                "by_quality": {},
                "average_processing_time": 0,
                "total_file_size": 0,
                "error_rate": 0
            }
            
            total_processing_time = 0
            total_file_size = 0
            error_count = 0
            completed_count = 0
            
            for movie_entry in self.movie_entries.values():
                # Count by status
                status = movie_entry.status
                stats["by_status"][status] = stats["by_status"].get(status, 0) + 1
                
                # Count by quality
                if movie_entry.quality:
                    quality = movie_entry.quality
                    stats["by_quality"][quality] = stats["by_quality"].get(quality, 0) + 1
                
                # Accumulate metrics
                if movie_entry.processing_time:
                    total_processing_time += movie_entry.processing_time
                    completed_count += 1
                
                if movie_entry.file_size:
                    total_file_size += movie_entry.file_size
                
                if movie_entry.error_count > 0:
                    error_count += 1
            
            # Calculate averages
            if completed_count > 0:
                stats["average_processing_time"] = total_processing_time / completed_count
            
            stats["total_file_size"] = total_file_size
            
            if len(self.movie_entries) > 0:
                stats["error_rate"] = (error_count / len(self.movie_entries)) * 100
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting movie statistics: {e}")
            return {}
    
    def _map_status(self, internal_status: str) -> str:
        """Map internal pipeline status to Notion status."""
        return self.stage_mappings.get(internal_status, internal_status)
    
    def _update_movie_entry_properties(self, movie_entry: NotionMovieEntry, data: Dict[str, Any]) -> None:
        """Update movie entry properties from additional data."""
        if "cleaned_title" in data:
            movie_entry.title = data["cleaned_title"]
        if "year" in data:
            movie_entry.year = data["year"]
        if "detected_quality" in data:
            movie_entry.quality = data["detected_quality"]
        if "resolution" in data:
            movie_entry.quality = data["resolution"]
        if "file_size_gb" in data:
            movie_entry.file_size = data["file_size_gb"]
        if "processing_time" in data:
            movie_entry.processing_time = data["processing_time"]
        if "error_message" in data:
            movie_entry.notes = data["error_message"]
        
        # Handle metadata updates
        if "metadata_details" in data:
            metadata = data["metadata_details"]
            if metadata.get("tmdb_id"):
                movie_entry.tmdb_id = metadata["tmdb_id"]
            if metadata.get("imdb_id"):
                movie_entry.imdb_id = metadata["imdb_id"]
            if metadata.get("runtime"):
                movie_entry.runtime = metadata["runtime"]
            if metadata.get("genres"):
                movie_entry.genres = metadata["genres"]
            if metadata.get("director"):
                movie_entry.director = metadata["director"]
            if metadata.get("cast"):
                movie_entry.cast = metadata["cast"][:5]  # Limit to top 5
            if metadata.get("overview"):
                movie_entry.overview = metadata["overview"]
    
    async def health_check(self) -> bool:
        """Perform health check on the integration."""
        if not self.initialized:
            return False
        
        try:
            # Check if Notion service is healthy
            if self.notion_service:
                return await self.notion_service.health_check()
            return False
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup the integration."""
        self.logger.info("Cleaning up Notion Pipeline Integration...")
        self.movie_entries.clear()
        self.initialized = False

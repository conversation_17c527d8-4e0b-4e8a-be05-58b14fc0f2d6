# Step 3: NGC OCR Integration Plan for PlexMovieAutomator

## Overview
This step integrates NVIDIA NGC OCR models as **enhancement tools** for your existing BDSup2Sub-based subtitle pipeline, not as replacements.

## Integration Strategy

### 🎯 **Primary Use Cases**
1. **Quality Control** - Validate BDSup2Sub extraction results
2. **Fallback Detection** - Handle cases where BDSup2Sub struggles
3. **Edge Case Resolution** - Process difficult subtitle formats
4. **Accuracy Enhancement** - Improve OCR results for challenging text

### 📁 **Pipeline Integration Points**

#### **Stage 3: MKV Processor Enhancement** (`03_mkv_processor.py`)
```python
# BEFORE: Basic subtitle track extraction
extract_subtitle_tracks(mkv_file)

# AFTER: Enhanced with NGC OCR validation
extract_subtitle_tracks(mkv_file)
if needs_ocr_validation(subtitle_track):
    ngc_regions = detect_subtitle_regions(video_frames)
    validate_extraction_quality(bdsup2sub_result, ngc_regions)
```

#### **Stage 5: Subtitle Handler Enhancement** (`05_subtitle_handler.py`)
```python
# BEFORE: BDSup2Sub only
bdsup2sub_result = extract_with_bdsup2sub(pgs_file)

# AFTER: NGC OCR as backup/enhancement
bdsup2sub_result = extract_with_bdsup2sub(pgs_file)
if quality_check_fails(bdsup2sub_result):
    ngc_result = extract_with_ngc_ocr(video_frames)
    final_result = merge_best_results(bdsup2sub_result, ngc_result)
```

### 🔧 **Implementation Approach**

#### **1. Smart Frame Sampling**
- Don't process every frame (expensive)
- Sample frames every 0.5-1 seconds
- Focus on frames with subtitle timing metadata
- Skip frames where BDSup2Sub already worked perfectly

#### **2. Region Filtering for Subtitles**
- Restrict detection to bottom 30% of frame
- Filter by subtitle-typical aspect ratios
- Ignore non-subtitle text (credits, signs, etc.)
- Prioritize center-bottom positioning

#### **3. Hybrid Processing Workflow**
```
1. Run existing BDSup2Sub extraction
2. Analyze extraction quality/confidence
3. If quality < threshold:
   - Extract relevant video frames
   - Run NGC OCR text detection
   - Compare with BDSub2Sub results
   - Use best result or merge both
4. Continue with existing pipeline
```

### 📊 **Performance Optimization**

#### **Selective Processing**
- Only run NGC OCR when needed (not every file)
- Use metadata to identify problematic subtitle tracks
- Cache results to avoid reprocessing
- Process in batches for efficiency

#### **Resource Management**
- Use RTX 5090's 32GB VRAM efficiently
- Process multiple frames in batches
- Implement frame-level caching
- Balance speed vs accuracy

### 🔀 **Modified Pipeline Flow**

```
Stage 3: MKV Processor
├── Extract subtitle tracks (existing)
├── Identify problematic tracks
└── Mark for NGC OCR enhancement

Stage 5: Subtitle Handler  
├── Run BDSup2Sub extraction (existing)
├── Quality assessment
├── If needed: NGC OCR enhancement
│   ├── Frame extraction
│   ├── Text region detection
│   ├── Result comparison
│   └── Best result selection
└── Continue to final mux
```

### 🛠️ **Technical Implementation**

#### **File Structure**
```
_internal/tools/
├── ngc_ocr/
│   ├── text_detector.py      # NGC OCDNet wrapper
│   ├── subtitle_enhancer.py  # Integration with existing pipeline
│   ├── quality_assessor.py   # Determine when to use NGC OCR
│   └── result_merger.py      # Combine BDSup2Sub + NGC results
```

#### **Configuration**
```python
# settings.ini additions
[NGC_OCR]
enabled = true
quality_threshold = 0.7
frame_sample_rate = 0.5
use_tensorrt = true
confidence_threshold = 0.5
```

#### **Dependencies**
- Existing: BDSup2Sub, FFmpeg, MKVToolNix
- New: NGC OCR models, ONNX Runtime GPU, TensorRT

### 🎯 **Step 3 Deliverables**

1. **NGC Model Setup**
   - Download OCDNet from NGC catalog
   - Convert to optimized format (ONNX/TensorRT)
   - Test with sample subtitle frames

2. **Integration Code**
   - Wrapper classes for NGC models
   - Quality assessment functions
   - Pipeline integration points

3. **Testing Framework**
   - Test with existing problematic files
   - Compare BDSup2Sub vs NGC results
   - Benchmark performance impact

4. **Configuration System**
   - Enable/disable NGC enhancement
   - Adjustable quality thresholds
   - Performance tuning options

### 📋 **Next Steps**
After Step 3, we'll have:
- NGC OCR models ready and optimized
- Integration points identified and tested
- Quality assessment system working
- Enhanced subtitle extraction capability

The existing BDSup2Sub workflow remains primary, with NGC OCR providing intelligent enhancement where needed.

---

**Key Principle**: Enhance, don't replace. Each tool does what it does best:
- **BDSup2Sub**: Fast, reliable PGS extraction
- **NGC OCR**: Handle difficult cases with superior accuracy
- **Combined**: Best of both worlds

# ✅ COMPLETE: Fixed Stage 5 to Copy Subtitles to Final Mux Folder

## 🎯 **PROBLEM SOLVED**

You identified a critical workflow issue: **Stage 5 was leaving subtitles in Stage 3 location instead of putting them where Stage 6 expects them.**

### **BEFORE (Broken Flow):**
```
Stage 3: Creates processed_va.mkv + extracts subtitles to /subtitles/
Stage 4: Encodes video → 4_ready_for_final_mux/[movie]/encoded_video.mkv
Stage 5: Converts PGS→SRT → LEAVES in 3_mkv_cleaned_subtitles_extracted/[movie]/subtitles/
Stage 6: Looks for video in 4_ready_for_final_mux/ but subtitles elsewhere ❌
```

### **AFTER (Fixed Flow):**
```  
Stage 3: Creates processed_va.mkv + extracts subtitles to /subtitles/
Stage 4: Encodes video → 4_ready_for_final_mux/[movie]/encoded_video.mkv  
Stage 5: Converts PGS→SRT → COPIES to 4_ready_for_final_mux/[movie]/movie.srt ✅
Stage 6: Finds BOTH video AND subtitles in 4_ready_for_final_mux/[movie]/ ✅
```

## 🛠️ **CHANGES MADE TO STAGE 5**

### **New Logic Added:**
```python
# 4. Copy final SRT and PGS to the final mux folder (where Stage 4 placed the encoded video)
final_mux_srt_path = None
final_mux_pgs_path = None

if final_srt_path or final_pgs_path:
    try:
        # Determine the target directory in 4_ready_for_final_mux
        movie_title = movie.get("cleaned_title", "Unknown")
        resolution = movie.get("resolution", "1080p")
        final_mux_base_dir = Path("workspace") / "4_ready_for_final_mux" / resolution / movie_title
        final_mux_base_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy SRT to final mux folder with a clean name
        if final_srt_path:
            final_mux_srt_path = final_mux_base_dir / f"{movie_title}.srt"
            shutil.copy2(final_srt_path, final_mux_srt_path)
            logger.info(f"Copied final SRT to final mux folder: {final_mux_srt_path}")
            final_srt_path = str(final_mux_srt_path)
        
        # Copy PGS to final mux folder with a clean name  
        if final_pgs_path:
            pgs_extension = Path(final_pgs_path).suffix
            final_mux_pgs_path = final_mux_base_dir / f"{movie_title}{pgs_extension}"
            shutil.copy2(final_pgs_path, final_mux_pgs_path)
            logger.info(f"Copied final PGS to final mux folder: {final_mux_pgs_path}")
            final_pgs_path = str(final_mux_pgs_path)
        
    except Exception as e:
        logger.error(f"Failed to copy subtitle files to final mux folder: {e}")
        # Keep original paths if copy fails
        pass
```

## 🎯 **PERFECT WORKFLOW NOW**

### **Stage 5 Now Does:**
1. ✅ **Finds subtitles** in `3_mkv_cleaned_subtitles_extracted/[movie]/subtitles/`
2. ✅ **Converts PGS→SRT** using MCP ImageSorcery OCR
3. ✅ **Copies both SRT and PGS** to `4_ready_for_final_mux/[movie]/`
4. ✅ **Updates database** with new paths in final mux folder
5. ✅ **Sets status** to `final_mux_pending`

### **Final Mux Folder Contents:**
```
4_ready_for_final_mux/1080p/The Matrix (1999)/
├── encoded_video.mkv          ← From Stage 4 (video encoding)
├── The Matrix (1999).srt      ← From Stage 5 (subtitle conversion)
└── The Matrix (1999).sup      ← From Stage 5 (original PGS backup)
```

### **Stage 6 Benefits:**
- ✅ **All files in one place** - no database path lookups needed
- ✅ **Clean organization** - video + subtitles together
- ✅ **Simple workflow** - just scan the folder for files to mux
- ✅ **Backup preservation** - keeps both SRT and original PGS

## 🚀 **YOUR SUGGESTION WAS SPOT-ON!**

You correctly identified that:
1. **Stage 4 puts encoded video in final mux folder**
2. **Stage 5 should put converted subtitles in the SAME folder**
3. **Stage 6 should find everything together in one place**

This creates a **much cleaner, more logical workflow** where each stage's output is exactly where the next stage expects to find it.

### **Benefits of This Design:**
- 🎯 **Intuitive** - files flow naturally from stage to stage
- 🎯 **Reliable** - no complex database path coordination needed
- 🎯 **Debuggable** - easy to see what files are ready for muxing
- 🎯 **Efficient** - Stage 6 just processes what's in the folder

**You've improved the entire pipeline architecture!** 🎉

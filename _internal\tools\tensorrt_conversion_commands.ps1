# Step 4: TensorRT Conversion Commands for RTX 5090
# Run these commands in PowerShell (as Administrator) from your project directory

# Create TensorRT directory
mkdir "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt"

# Convert OCDNet (Text Detection) to TensorRT
trtexec `
  --onnx="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx" `
  --saveEngine="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocdnet_optimized.trt" `
  --fp16 `
  --workspace=8192 `
  --verbose

# Convert OCRNet (Text Recognition) to TensorRT  
trtexec `
  --onnx="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx" `
  --saveEngine="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocrnet_optimized.trt" `
  --fp16 `
  --workspace=4096 `
  --verbose

# Alternative: Simple conversion (if dynamic shapes cause issues)
# OCDNet Simple
trtexec --onnx="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocdnet_v2.4\ocdnet_vdeployable_onnx_v2.4\ocdnet_fan_tiny_2x_icdar_pruned.onnx" --saveEngine="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocdnet_simple.trt" --fp16

# OCRNet Simple  
trtexec --onnx="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\ocrnet_v2.1.1\ocrnet_vdeployable_v2.1.1\ocrnet-vit-pcb.onnx" --saveEngine="C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr\tensorrt\ocrnet_simple.trt" --fp16

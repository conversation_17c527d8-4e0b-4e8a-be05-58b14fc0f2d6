#!/usr/bin/env python3
"""
Filesystem-First State Management System for PlexMovieAutomator.

This implements the new architecture where:
- Filesystem is the single source of truth for pipeline state
- Database stores only static metadata (TMDB ID, user preferences, etc.)
- Marker files track processing state
- All scripts use filesystem scanning to discover work

Key principles:
1. Filesystem scanning as state: Scripts discover movies by scanning directories
2. Marker files for progress: Hidden marker files indicate processing stages
3. Idempotent operations: Safe to retry after interruptions
4. Database for metadata only: No status or path information stored in DB
"""

import sqlite3
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timezone
import re
import threading
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class FilesystemFirstStateManager:
    """
    Filesystem-first state management using marker files and directory scanning.
    
    This class provides:
    - File-based discovery of movies by scanning workspace directories
    - Marker file management for tracking processing stages
    - Idempotent operations that can recover from interruptions
    - Auto-detection of interrupted work for resumption
    """
    
    # Standard marker files for tracking processing stages
    MARKERS = {
        'organized': '.organized',
        'mkv_processing': '.mkv_processing',
        'mkv_complete': '.mkv_complete',
        'subtitle_processing': '.subtitle_processing',
        'subtitle_complete': '.subtitle_complete',
        'final_mux_complete': '.final_mux_complete',
        'error': '.error'
    }
    
    def __init__(self, workspace_root: Path):
        self.workspace_root = Path(workspace_root)
        
        # Define workspace directories for each stage
        self.stage_directories = {
            'download_raw': self.workspace_root / "workspace" / "1_downloading" / "complete_raw",
            'organized': self.workspace_root / "workspace" / "2_downloaded_and_organized",
            'mkv_processed': self.workspace_root / "workspace" / "3_mkv_cleaned_subtitles_extracted",
            # Note: 4_subtitles_converting removed - Stage 5 works directly from stage 3 to stage 5
            'ready_for_mux': self.workspace_root / "workspace" / "4_ready_for_final_mux",
            'awaiting_poster': self.workspace_root / "workspace" / "5_awaiting_poster",
            'awaiting_qc': self.workspace_root / "workspace" / "6_awaiting_quality_check",
            'completed': self.workspace_root / "workspace" / "7_final_plex_ready",
            'issues': self.workspace_root / "workspace" / "issues_hold"
        }
        
        # Create directories if they don't exist
        for directory in self.stage_directories.values():
            directory.mkdir(parents=True, exist_ok=True)
    
    def discover_movies_by_stage(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Discover movies at each pipeline stage by scanning filesystem.
        Returns movies organized by their current stage.
        """
        movies = {
            'download_completed': [],
            'organized': [],
            'mkv_processing_pending': [],
            'mkv_processing_active': [],
            'mkv_processing_interrupted': [],
            'mkv_processing_complete': [],
            'subtitle_processing_pending': [],
            'subtitle_processing_active': [],
            'subtitle_processing_complete': [],
            'final_mux_pending': [],
            'final_mux_complete': [],
            'completed': [],
            'error': []
        }
        
        # 1. Scan for completed downloads (ready for organization)
        if self.stage_directories['download_raw'].exists():
            for item in self.stage_directories['download_raw'].iterdir():
                if item.is_dir():
                    movie_info = self._analyze_download_folder(item)
                    if movie_info:
                        movies['download_completed'].append(movie_info)
        
        # 2. Scan organized directory and its resolution subfolders
        if self.stage_directories['organized'].exists():
            for item in self.stage_directories['organized'].iterdir():
                if item.is_dir():
                    # Check if this is a resolution folder (e.g., "1080p", "4K", "SD_or_unknown")
                    if any(res in item.name.lower() for res in ['1080p', '4k', 'sd_or_unknown', '720p', '2160p']):
                        # This is a resolution folder, scan its contents
                        for movie_dir in item.iterdir():
                            if movie_dir.is_dir():
                                movie_info = self._analyze_movie_directory(movie_dir)
                                if movie_info:
                                    stage = self._determine_movie_stage(movie_dir)
                                    if stage in movies:
                                        movies[stage].append(movie_info)
                    else:
                        # This might be a movie folder directly in organized
                        movie_info = self._analyze_movie_directory(item)
                        if movie_info:
                            stage = self._determine_movie_stage(item)
                            if stage in movies:
                                movies[stage].append(movie_info)
        
        # 3. Scan other processing directories
        for stage_name, directory in self.stage_directories.items():
            if stage_name in ['organized', 'download_raw']:
                continue  # Already scanned above
                
            if directory.exists():
                for item in directory.iterdir():
                    if item.is_dir():
                        # Check if this is a resolution folder (e.g., "1080p", "4K", "SD_or_unknown")
                        if any(res in item.name.lower() for res in ['1080p', '4k', 'sd_or_unknown', '720p', '2160p']):
                            # This is a resolution folder, scan its contents
                            for movie_dir in item.iterdir():
                                if movie_dir.is_dir():
                                    movie_info = self._analyze_movie_directory(movie_dir)
                                    if movie_info:
                                        stage = self._determine_movie_stage(movie_dir)
                                        # Map directory-based stage to our stage names
                                        if stage_name == 'completed' and stage != 'error':
                                            movies['completed'].append(movie_info)
                                        elif stage == 'error':
                                            movies['error'].append(movie_info)
                                        elif stage in movies:
                                            movies[stage].append(movie_info)
                        else:
                            # This might be a movie folder directly in the stage directory
                            movie_info = self._analyze_movie_directory(item)
                            if movie_info:
                                stage = self._determine_movie_stage(item)
                                # Map directory-based stage to our stage names
                                if stage_name == 'completed' and stage != 'error':
                                    movies['completed'].append(movie_info)
                                elif stage == 'error':
                                    movies['error'].append(movie_info)
                                elif stage in movies:
                                    movies[stage].append(movie_info)
        
        return movies
    
    def _analyze_download_folder(self, folder_path: Path) -> Optional[Dict[str, Any]]:
        """Analyze a download folder to extract movie information."""
        try:
            movie_info = self._extract_movie_info_from_filename(folder_path.name)
            movie_info.update({
                'download_folder': str(folder_path),
                'stage': 'download_completed',
                'discovered_at': datetime.now().isoformat()
            })
            return movie_info
            
        except Exception as e:
            logger.warning(f"Error analyzing download folder {folder_path}: {e}")
            return None
    
    def _analyze_movie_directory(self, movie_dir: Path) -> Optional[Dict[str, Any]]:
        """Analyze a movie directory in any processing stage."""
        try:
            movie_info = self._extract_movie_info_from_filename(movie_dir.name)
            
            # Find main movie file
            movie_files = list(movie_dir.glob("*.mkv"))
            if movie_files:
                movie_info['main_movie_file'] = str(movie_files[0])
            
            # Check for processed content
            processed_dir = movie_dir / "_Processed_VideoAudio"
            if processed_dir.exists():
                movie_info['processed_content'] = str(processed_dir)
            
            movie_info.update({
                'movie_directory': str(movie_dir),
                'discovered_at': datetime.now().isoformat()
            })
            
            return movie_info
            
        except Exception as e:
            logger.warning(f"Error analyzing movie directory {movie_dir}: {e}")
            return None
    
    def determine_movie_stage(self, movie_dir: Path) -> str:
        """Public method to determine the current stage of a movie based on marker files and content."""
        return self._determine_movie_stage(movie_dir)
    
    def _determine_movie_stage(self, movie_dir: Path) -> str:
        """Determine the current stage of a movie based on marker files and content."""
        
        # Check for error marker first
        if (movie_dir / self.MARKERS['error']).exists():
            return 'error'
        
        # Check for completion markers in reverse pipeline order
        if (movie_dir / self.MARKERS['final_mux_complete']).exists():
            return 'final_mux_complete'
        
        if (movie_dir / self.MARKERS['subtitle_complete']).exists():
            return 'final_mux_pending'
        
        if (movie_dir / self.MARKERS['subtitle_processing']).exists():
            return 'subtitle_processing_active'
        
        if (movie_dir / self.MARKERS['mkv_complete']).exists():
            return 'subtitle_processing_pending'
        
        if (movie_dir / self.MARKERS['mkv_processing']).exists():
            return 'mkv_processing_interrupted'  # Was processing but stopped
        
        if (movie_dir / self.MARKERS['organized']).exists():
            return 'mkv_processing_pending'
        
        # Check based on content if no markers exist
        if (movie_dir / "_Processed_VideoAudio").exists():
            return 'mkv_processing_complete'  # Has processed content, ready for next step
        
        # Has main movie file but no processing done
        if list(movie_dir.glob("*.mkv")):
            return 'organized'
        
        return 'unknown'
    
    def _extract_movie_info_from_filename(self, filename: str) -> Dict[str, Any]:
        """Extract movie title and year from filename."""
        # Try to extract year in parentheses
        year_match = re.search(r'\((\d{4})\)', filename)
        year = int(year_match.group(1)) if year_match else None
        
        # Extract title (everything before the year)
        if year_match:
            title = filename[:year_match.start()].strip()
        else:
            title = filename
        
        # Clean up common artifacts
        title = re.sub(r'\[.*?\]', '', title).strip()  # Remove [tags]
        title = re.sub(r'\{.*?\}', '', title).strip()  # Remove {tags}
        title = re.sub(r'\s+', ' ', title).strip()     # Normalize whitespace
        
        return {
            'cleaned_title': title,
            'year': year,
            'original_filename': filename
        }
    
    def set_stage_marker(self, movie_dir: Path, stage: str, data: Optional[Dict[str, Any]] = None):
        """Set a stage marker file for a movie."""
        if stage not in self.MARKERS:
            logger.warning(f"Unknown stage marker: {stage}")
            return
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            # Write marker with optional data
            marker_content = {
                'timestamp': datetime.now().isoformat(),
                'stage': stage
            }
            if data:
                marker_content.update(data)
            
            with open(marker_file, 'w') as f:
                json.dump(marker_content, f, indent=2)
            
            logger.debug(f"Set marker {stage} for {movie_dir.name}")
            
        except Exception as e:
            logger.error(f"Failed to set marker {stage} for {movie_dir}: {e}")
    
    def clear_stage_marker(self, movie_dir: Path, stage: str):
        """Clear a stage marker file."""
        if stage not in self.MARKERS:
            logger.warning(f"Unknown stage marker: {stage}")
            return
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            if marker_file.exists():
                marker_file.unlink()
                logger.debug(f"Cleared marker {stage} for {movie_dir.name}")
        except Exception as e:
            logger.error(f"Failed to clear marker {stage} for {movie_dir}: {e}")
    
    def get_stage_marker_data(self, movie_dir: Path, stage: str) -> Optional[Dict[str, Any]]:
        """Get data from a stage marker file."""
        if stage not in self.MARKERS:
            return None
        
        marker_file = movie_dir / self.MARKERS[stage]
        
        try:
            if marker_file.exists():
                with open(marker_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to read marker {stage} for {movie_dir}: {e}")
        
        return None
    
    def is_stage_complete(self, movie_dir: Path, stage: str) -> bool:
        """Check if a stage is complete for a movie."""
        marker_file = movie_dir / self.MARKERS.get(stage, f'.{stage}')
        return marker_file.exists()
    
    def generate_movie_identifier(self, movie_info: Dict[str, Any]) -> str:
        """Generate a unique identifier for a movie."""
        title = movie_info.get('cleaned_title', 'Unknown')
        year = movie_info.get('year', 'Unknown')
        return f"{title} ({year})"
    
    def cleanup_stale_markers(self, movie_dir: Path):
        """Clean up stale or conflicting marker files."""
        try:
            # Remove processing markers if corresponding complete markers exist
            if self.is_stage_complete(movie_dir, 'mkv_complete'):
                self.clear_stage_marker(movie_dir, 'mkv_processing')

            if self.is_stage_complete(movie_dir, 'subtitle_complete'):
                self.clear_stage_marker(movie_dir, 'subtitle_processing')

        except Exception as e:
            logger.warning(f"Failed to cleanup stale markers for {movie_dir}: {e}")
    
    def find_movie_by_identifier(self, movie_identifier: str) -> Optional[Dict[str, Any]]:
        """Find a movie by its identifier across all stages."""
        all_movies = self.discover_movies_by_stage()
        
        for stage, movies in all_movies.items():
            for movie in movies:
                if self.generate_movie_identifier(movie) == movie_identifier:
                    movie['current_stage'] = stage
                    return movie
        
        return None


class MetadataOnlyDatabase:
    """
    Simplified database that stores only static movie metadata.
    
    No status, paths, or processing information - only TMDB IDs, 
    user preferences, and other non-discoverable metadata.
    """
    
    def __init__(self, workspace_root: Path, db_path: Optional[Path] = None):
        self.workspace_root = Path(workspace_root)
        self.db_path = db_path or (self.workspace_root / "_internal" / "data" / "movie_metadata.db")
        
        # Thread-local storage for database connections
        self._local = threading.local()
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database with metadata-only schema."""
        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        with self._get_connection() as conn:
            # Create metadata-only table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS movies (
                    movie_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    unique_id TEXT UNIQUE NOT NULL,  -- e.g. "Title (Year)"
                    title TEXT NOT NULL,
                    year INTEGER,
                    tmdb_id TEXT,
                    imdb_id TEXT,
                    audio_lang TEXT DEFAULT 'eng',
                    subtitle_lang TEXT DEFAULT 'eng',
                    keep_commentary BOOLEAN DEFAULT 0,
                    metadata_json TEXT,  -- Additional metadata as JSON
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Create indexes
            conn.execute("CREATE INDEX IF NOT EXISTS idx_movies_unique_id ON movies(unique_id);")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_movies_tmdb_id ON movies(tmdb_id);")
            
            logger.info(f"Initialized metadata database at: {self.db_path}")
    
    def _get_connection(self) -> sqlite3.Connection:
        """Get a thread-local database connection."""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(str(self.db_path))
            self._local.connection.row_factory = sqlite3.Row
            self._local.connection.execute("PRAGMA foreign_keys = ON")
        
        return self._local.connection
    
    @contextmanager
    def transaction(self):
        """Context manager for database transactions with automatic rollback on error."""
        conn = self._get_connection()
        try:
            conn.execute("BEGIN")
            yield conn
            conn.commit()
        except Exception:
            conn.rollback()
            raise
    
    def get_movie_metadata(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Get metadata for a movie by its unique identifier."""
        conn = self._get_connection()
        cursor = conn.execute("SELECT * FROM movies WHERE unique_id = ?", (unique_id,))
        row = cursor.fetchone()
        
        if row:
            movie = dict(row)
            # Parse JSON metadata
            if movie.get('metadata_json'):
                try:
                    movie['metadata'] = json.loads(movie['metadata_json'])
                except json.JSONDecodeError:
                    movie['metadata'] = {}
            else:
                movie['metadata'] = {}
            return movie
        
        return None
    
    def save_movie_metadata(self, unique_id: str, title: str, year: Optional[int] = None,
                           tmdb_id: Optional[str] = None, imdb_id: Optional[str] = None,
                           audio_lang: str = 'eng', subtitle_lang: str = 'eng',
                           keep_commentary: bool = False, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """Save or update movie metadata."""
        with self.transaction() as conn:
            # Check if movie exists
            cursor = conn.execute("SELECT movie_id FROM movies WHERE unique_id = ?", (unique_id,))
            exists = cursor.fetchone()
            
            metadata_json = json.dumps(metadata) if metadata else None
            
            if exists:
                # Update existing
                conn.execute("""
                    UPDATE movies SET 
                        title = ?, year = ?, tmdb_id = ?, imdb_id = ?,
                        audio_lang = ?, subtitle_lang = ?, keep_commentary = ?,
                        metadata_json = ?, last_updated = CURRENT_TIMESTAMP
                    WHERE unique_id = ?
                """, (title, year, tmdb_id, imdb_id, audio_lang, subtitle_lang, 
                     keep_commentary, metadata_json, unique_id))
                logger.debug(f"Updated metadata for {unique_id}")
            else:
                # Insert new
                conn.execute("""
                    INSERT INTO movies (unique_id, title, year, tmdb_id, imdb_id,
                                      audio_lang, subtitle_lang, keep_commentary, metadata_json)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (unique_id, title, year, tmdb_id, imdb_id, audio_lang, 
                     subtitle_lang, keep_commentary, metadata_json))
                logger.debug(f"Added metadata for {unique_id}")
            
            return True
    
    def get_all_movies(self) -> List[Dict[str, Any]]:
        """Get all movies from the metadata database."""
        conn = self._get_connection()
        cursor = conn.execute("SELECT * FROM movies ORDER BY title, year")
        rows = cursor.fetchall()
        
        movies = []
        for row in rows:
            movie = dict(row)
            # Parse JSON metadata
            if movie.get('metadata_json'):
                try:
                    movie['metadata'] = json.loads(movie['metadata_json'])
                except json.JSONDecodeError:
                    movie['metadata'] = {}
            else:
                movie['metadata'] = {}
            movies.append(movie)
        
        return movies
    
    def delete_movie_metadata(self, unique_id: str) -> bool:
        """Delete metadata for a movie."""
        with self.transaction() as conn:
            cursor = conn.execute("DELETE FROM movies WHERE unique_id = ?", (unique_id,))
            if cursor.rowcount > 0:
                logger.info(f"Deleted metadata for {unique_id}")
                return True
            return False
    
    def close(self):
        """Close database connections."""
        if hasattr(self._local, 'connection'):
            self._local.connection.close()
            delattr(self._local, 'connection')


def create_filesystem_first_manager(workspace_root: Optional[str] = None) -> FilesystemFirstStateManager:
    """Factory function to create a FilesystemFirstStateManager instance."""
    if workspace_root is None:
        workspace_root_path = Path.cwd()
    else:
        workspace_root_path = Path(workspace_root)
    
    return FilesystemFirstStateManager(workspace_root_path)


def create_metadata_database(workspace_root: Optional[str] = None, db_path: Optional[str] = None) -> MetadataOnlyDatabase:
    """Factory function to create a MetadataOnlyDatabase instance."""
    if workspace_root is None:
        workspace_root_path = Path.cwd()
    else:
        workspace_root_path = Path(workspace_root)
    
    if db_path:
        db_path_obj = Path(db_path)
    else:
        db_path_obj = None
    
    return MetadataOnlyDatabase(workspace_root_path, db_path_obj)

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/mcp_manager.py

Main MCP Manager that coordinates all MCP server integrations
and provides a unified interface for the pipeline orchestrator.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timezone
import time

from .config_manager import MCPConfigManager, MCPServerConfig
from .sequential_thinking import SequentialThinkingMCP
from .memory_manager import MemoryManagerMCP
from .github_integration import GitHubMCP
from .notion_database import NotionDatabaseMCP
from .firecrawl_integration import FirecrawlMCP
from .image_processor import ImageSorceryMCP
from .radarr_integration import RadarrMCP
from .sonarr_integration import SonarrMCP
from .download_monitor import DownloadMonitorMCP
from .notification_system import NotificationSystemMCP
from .error_handler import ErrorHandlerMCP

class MCPManager:
    """
    Central manager for all MCP server integrations.
    Handles initialization, connection management, and coordination between services.
    """
    
    def __init__(self, settings_dict: Dict, logger: logging.Logger):
        self.settings = settings_dict
        self.logger = logger
        
        # Initialize configuration manager
        self.config_manager = MCPConfigManager(
            config_path="config/mcp_config.json",
            settings_dict=settings_dict,
            logger=logger
        )
        
        # MCP service instances
        self.services: Dict[str, Any] = {}
        self.service_health: Dict[str, Dict] = {}
        
        # Connection management
        self.connection_pool = {}
        self.active_connections = 0
        self.max_connections = 10
        
        # Performance tracking
        self.service_metrics = {}
        self.last_health_check = None
        
        # Error handling
        self.error_handler = None
        
    async def initialize(self) -> bool:
        """Initialize the MCP manager and all enabled services."""
        try:
            self.logger.info("Initializing MCP Manager...")
            
            # Load configuration
            if not self.config_manager.load_config():
                self.logger.error("Failed to load MCP configuration")
                return False
            
            # Setup API keys from environment
            self.config_manager.setup_api_keys_from_env()
            
            # Validate configuration
            issues = self.config_manager.validate_config()
            if issues:
                self.logger.warning(f"Configuration issues found: {issues}")
            
            # Update max connections from config
            self.max_connections = self.config_manager.global_config.max_concurrent_connections
            
            # Initialize error handler first
            await self._initialize_error_handler()
            
            # Initialize enabled services
            enabled_servers = self.config_manager.get_enabled_servers()
            self.logger.info(f"Initializing {len(enabled_servers)} enabled MCP services...")
            
            for server_config in enabled_servers:
                success = await self._initialize_service(server_config)
                if not success:
                    self.logger.warning(f"Failed to initialize service: {server_config.name}")
            
            # Start health monitoring
            asyncio.create_task(self._health_monitor_loop())
            
            self.logger.info(f"MCP Manager initialized with {len(self.services)} active services")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MCP Manager: {e}")
            return False
    
    async def _initialize_error_handler(self):
        """Initialize the error handling service."""
        try:
            self.error_handler = ErrorHandlerMCP(
                config=None,  # Error handler uses default config
                logger=self.logger,
                mcp_manager=self
            )
            await self.error_handler.initialize()
            self.logger.info("Error handler initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize error handler: {e}")
    
    async def _initialize_service(self, config: MCPServerConfig) -> bool:
        """Initialize a single MCP service."""
        try:
            service_class = self._get_service_class(config.server_type)
            if not service_class:
                self.logger.error(f"Unknown service type: {config.server_type}")
                return False
            
            # Create service instance
            service = service_class(
                config=config,
                logger=self.logger,
                mcp_manager=self
            )
            
            # Initialize the service
            if await service.initialize():
                self.services[config.name] = service
                self.service_health[config.name] = {
                    "status": "healthy",
                    "last_check": datetime.now(timezone.utc).isoformat(),
                    "error_count": 0,
                    "last_error": None
                }
                self.service_metrics[config.name] = {
                    "requests": 0,
                    "successes": 0,
                    "failures": 0,
                    "avg_response_time": 0.0,
                    "total_response_time": 0.0
                }
                
                self.logger.info(f"Successfully initialized service: {config.name}")
                return True
            else:
                self.logger.error(f"Service initialization failed: {config.name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Exception initializing service {config.name}: {e}")
            return False
    
    def _get_service_class(self, server_type: str) -> Optional[type]:
        """Get the service class for a given server type."""
        service_classes = {
            "sequential_thinking": SequentialThinkingMCP,
            "memory": MemoryManagerMCP,
            "github": GitHubMCP,
            "notion": NotionDatabaseMCP,
            "firecrawl": FirecrawlMCP,
            "imagesorcery": ImageSorceryMCP,
            "radarr": RadarrMCP,
            "sonarr": SonarrMCP,
            "download_monitor": DownloadMonitorMCP,
            "notification_system": NotificationSystemMCP
        }
        return service_classes.get(server_type)
    
    async def call_service(self, service_name: str, method: str, 
                          *args, **kwargs) -> Optional[Any]:
        """
        Call a method on a specific MCP service with error handling and metrics.
        """
        if service_name not in self.services:
            self.logger.error(f"Service {service_name} not available")
            return None
        
        service = self.services[service_name]
        start_time = time.time()
        
        try:
            # Update metrics
            self.service_metrics[service_name]["requests"] += 1
            
            # Call the service method
            if hasattr(service, method):
                result = await getattr(service, method)(*args, **kwargs)
                
                # Update success metrics
                response_time = time.time() - start_time
                metrics = self.service_metrics[service_name]
                metrics["successes"] += 1
                metrics["total_response_time"] += response_time
                metrics["avg_response_time"] = metrics["total_response_time"] / metrics["requests"]
                
                return result
            else:
                self.logger.error(f"Method {method} not found on service {service_name}")
                return None
                
        except Exception as e:
            # Update failure metrics
            self.service_metrics[service_name]["failures"] += 1
            
            # Update health status
            self.service_health[service_name]["error_count"] += 1
            self.service_health[service_name]["last_error"] = str(e)
            
            # Log error
            self.logger.error(f"Service call failed - {service_name}.{method}: {e}")
            
            # Handle error through error handler
            if self.error_handler:
                await self.error_handler.handle_service_error(service_name, method, e)
            
            return None
    
    async def broadcast_to_services(self, method: str, service_types: List[str] = None,
                                   *args, **kwargs) -> Dict[str, Any]:
        """
        Broadcast a method call to multiple services.
        
        Args:
            method: Method name to call
            service_types: List of service types to call (None = all services)
            *args, **kwargs: Arguments to pass to the method
            
        Returns:
            Dict mapping service names to their results
        """
        results = {}
        
        # Determine which services to call
        target_services = []
        if service_types:
            for service_name, service in self.services.items():
                config = self.config_manager.get_server_config(service_name)
                if config and config.server_type in service_types:
                    target_services.append(service_name)
        else:
            target_services = list(self.services.keys())
        
        # Call services concurrently
        tasks = []
        for service_name in target_services:
            task = self.call_service(service_name, method, *args, **kwargs)
            tasks.append((service_name, task))
        
        # Wait for all results
        for service_name, task in tasks:
            try:
                result = await task
                results[service_name] = result
            except Exception as e:
                self.logger.error(f"Broadcast call failed for {service_name}: {e}")
                results[service_name] = None
        
        return results
    
    async def _health_monitor_loop(self):
        """Background task to monitor service health."""
        interval = self.config_manager.global_config.health_check_interval
        
        while True:
            try:
                await asyncio.sleep(interval)
                await self._perform_health_checks()
            except Exception as e:
                self.logger.error(f"Health monitor error: {e}")
    
    async def _perform_health_checks(self):
        """Perform health checks on all services."""
        self.last_health_check = datetime.now(timezone.utc).isoformat()
        
        for service_name, service in self.services.items():
            try:
                if hasattr(service, 'health_check'):
                    is_healthy = await service.health_check()
                    
                    self.service_health[service_name].update({
                        "status": "healthy" if is_healthy else "unhealthy",
                        "last_check": self.last_health_check
                    })
                    
                    if not is_healthy:
                        self.logger.warning(f"Service {service_name} failed health check")
                        
            except Exception as e:
                self.service_health[service_name].update({
                    "status": "error",
                    "last_check": self.last_health_check,
                    "last_error": str(e)
                })
                self.logger.error(f"Health check failed for {service_name}: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive status of all MCP services."""
        return {
            "manager_status": "active",
            "total_services": len(self.services),
            "active_connections": self.active_connections,
            "max_connections": self.max_connections,
            "last_health_check": self.last_health_check,
            "services": {
                name: {
                    "health": self.service_health.get(name, {}),
                    "metrics": self.service_metrics.get(name, {}),
                    "config": {
                        "type": self.config_manager.get_server_config(name).server_type,
                        "enabled": self.config_manager.get_server_config(name).enabled,
                        "priority": self.config_manager.get_server_config(name).priority
                    }
                }
                for name in self.services.keys()
            }
        }
    
    async def cleanup(self):
        """Cleanup all MCP services and connections."""
        self.logger.info("Cleaning up MCP Manager...")
        
        # Cleanup all services
        for service_name, service in self.services.items():
            try:
                if hasattr(service, 'cleanup'):
                    await service.cleanup()
                self.logger.info(f"Cleaned up service: {service_name}")
            except Exception as e:
                self.logger.error(f"Error cleaning up service {service_name}: {e}")
        
        # Clear service tracking
        self.services.clear()
        self.service_health.clear()
        self.service_metrics.clear()
        
        self.logger.info("MCP Manager cleanup completed")

2025-07-20 23:35:44,639 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:35:44,639 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:35:44,639 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:35:44,639 - ERROR - Error running Stage 02: No module named 'utils'
2025-07-20 23:35:44,639 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:35:44,639 - ERROR - One or more stages failed
2025-07-20 23:36:03,871 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:36:03,871 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:36:03,873 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:36:03,891 - ERROR - Failed to load settings
2025-07-20 23:36:03,891 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:36:03,891 - ERROR - One or more stages failed
2025-07-20 23:36:19,519 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:36:19,519 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:36:19,519 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:36:19,876 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-20 23:36:19,895 - INFO - Initialized SQLite database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\data\pipeline_state.db
2025-07-20 23:36:19,895 - INFO - Synchronizing database with filesystem...
2025-07-20 23:36:19,895 - INFO - Starting filesystem synchronization...
2025-07-20 23:36:19,896 - INFO - Correcting status for The Matrix: download_completed -> error_organizing_movie
2025-07-20 23:36:19,897 - INFO - Updated movie 5b895984: download_completed -> error_organizing_movie
2025-07-20 23:36:19,941 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 1, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-20 23:36:19,941 - INFO - Auto-corrected 1 movie statuses
2025-07-20 23:36:19,958 - INFO - Retrieved 1 movies from Radarr
2025-07-20 23:36:19,962 - INFO - Found 0 movies in download states to monitor
2025-07-20 23:36:19,963 - INFO - Found 0 movies in download states to monitor
2025-07-20 23:36:19,963 - INFO - No movies currently in download states
2025-07-20 23:36:19,969 - INFO - Retrieved 1 movies from Radarr
2025-07-20 23:36:19,974 - INFO -    Total movies to check: 0
2025-07-20 23:36:19,977 - INFO - Starting filesystem synchronization...
2025-07-20 23:36:19,978 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-20 23:36:19,978 - INFO - Pipeline state synchronized - corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-20 23:36:19,978 - INFO - ===== Finished Modern Radarr Download Monitoring =====
2025-07-20 23:36:19,981 - INFO - Stage 02 completed successfully
2025-07-20 23:36:19,981 - INFO - All stages completed successfully
2025-07-20 23:46:36,330 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:46:36,330 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:46:36,330 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:46:36,385 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-20 23:46:36,385 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:46:36,385 - ERROR - One or more stages failed
2025-07-20 23:56:06,037 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:56:06,037 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:56:06,037 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:56:06,094 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-20 23:56:06,094 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:56:06,094 - ERROR - One or more stages failed
2025-07-21 00:31:22,335 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 00:31:22,335 - INFO - Running Stage 02: Download and Organize
2025-07-21 00:31:22,335 - INFO - Starting Stage 02: Download and Organize
2025-07-21 00:31:22,456 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-21 00:31:22,457 - ERROR - Stage 02_download_and_organize failed
2025-07-21 00:31:22,457 - ERROR - One or more stages failed
2025-07-21 01:29:25,002 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 01:29:25,002 - INFO - Running Stage 02: Download and Organize
2025-07-21 01:29:25,002 - INFO - Starting Stage 02: Download and Organize
2025-07-21 01:29:25,055 - ERROR - Error running Stage 02: expected 'except' or 'finally' block (02_download_and_organize.py, line 763)
2025-07-21 01:29:25,055 - ERROR - Stage 02_download_and_organize failed
2025-07-21 01:29:25,055 - ERROR - One or more stages failed
2025-07-21 02:09:26,047 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 02:09:26,048 - INFO - Running Stage 02: Download and Organize
2025-07-21 02:09:26,048 - INFO - Starting Stage 02: Download and Organize
2025-07-21 02:09:26,749 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-21 02:09:26,749 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-21 02:09:34,262 - INFO - Initialized SQLite database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\_internal\data\pipeline_state.db
2025-07-21 02:09:34,263 - INFO - Synchronizing database with filesystem...
2025-07-21 02:09:34,263 - INFO - Starting filesystem synchronization...
2025-07-21 02:09:34,263 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 02:09:34,263 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-21 02:09:34,263 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-21 02:09:34,263 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-21 02:09:34,276 - INFO - Retrieved 1 movies from Radarr
2025-07-21 02:09:34,278 - INFO -      Active downloads in Radarr queue: 1
2025-07-21 02:09:34,278 - INFO - Found 0 movies in download states to monitor
2025-07-21 02:09:34,278 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-21 02:09:34,278 - INFO - Found 0 movies in download states to monitor
2025-07-21 02:09:34,278 - INFO - No movies currently in download states
2025-07-21 02:09:34,278 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-21 02:09:34,278 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-21 02:09:34,278 - INFO -      No completed movies found in filesystem
2025-07-21 02:09:34,279 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-21 02:09:34,279 - WARNING - Complete directory does not exist: workspace\1_downloading\complete_raw
2025-07-21 02:09:34,279 - INFO -      FILESYSTEM SCAN: Found 0 completed downloads with matched movies
2025-07-21 02:09:34,279 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
2025-07-21 02:09:34,290 - INFO -      Radarr API: Retrieved 1 movies for status sync
2025-07-21 02:09:34,290 - INFO - Starting filesystem synchronization...
2025-07-21 02:09:34,290 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 02:09:34,290 - INFO - Pipeline state synchronized - corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 02:09:34,290 - INFO - ===== Finished Modern Radarr Download Monitoring =====
2025-07-21 02:09:34,290 - INFO -     No new completed downloads found this run
2025-07-21 02:09:34,291 - INFO - Stage 02 completed successfully
2025-07-21 02:09:34,291 - INFO - All stages completed successfully
2025-07-21 04:24:06,685 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 04:24:06,691 - INFO - Running Stage 02: Download and Organize
2025-07-21 04:24:06,691 - INFO - Starting Stage 02: Download and Organize
2025-07-21 04:24:07,318 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-21 04:24:07,318 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-21 04:24:07,332 - INFO - Initialized SQLite database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\_internal\data\pipeline_state.db
2025-07-21 04:24:07,332 - INFO - Synchronizing database with filesystem...
2025-07-21 04:24:07,332 - INFO - Starting filesystem synchronization...
2025-07-21 04:24:07,332 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 04:24:07,332 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-21 04:24:07,332 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-21 04:24:07,332 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-21 04:24:07,339 - INFO - Retrieved 1 movies from Radarr
2025-07-21 04:24:07,340 - INFO -      Active downloads in Radarr queue: 1
2025-07-21 04:24:07,341 - INFO - Found 0 movies in download states to monitor
2025-07-21 04:24:07,341 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-21 04:24:07,342 - INFO - Found 0 movies in download states to monitor
2025-07-21 04:24:07,342 - INFO - No movies currently in download states
2025-07-21 04:24:07,342 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-21 04:24:07,342 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-21 04:24:07,342 - INFO -      No completed movies found in filesystem
2025-07-21 04:24:07,342 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-21 04:24:07,342 - WARNING - Complete directory does not exist: workspace\1_downloading\complete_raw
2025-07-21 04:24:07,342 - INFO -      FILESYSTEM SCAN: Found 0 completed downloads with matched movies
2025-07-21 04:24:07,342 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
2025-07-21 04:24:07,351 - INFO -      Radarr API: Retrieved 1 movies for status sync
2025-07-21 04:24:07,351 - INFO - Starting filesystem synchronization...
2025-07-21 04:24:07,351 - INFO - Filesystem sync complete. Corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 04:24:07,351 - INFO - Pipeline state synchronized - corrections: {'status_corrections': 0, 'missing_files': 0, 'orphaned_files': 0, 'new_discoveries': 0}
2025-07-21 04:24:07,351 - INFO - ===== Finished Modern Radarr Download Monitoring =====
2025-07-21 04:24:07,351 - INFO -     No new completed downloads found this run
2025-07-21 04:24:07,352 - INFO - Stage 02 completed successfully
2025-07-21 04:24:07,352 - INFO - All stages completed successfully
2025-07-22 06:12:46,912 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-22 06:12:46,912 - INFO - Running Stage 02: Download and Organize
2025-07-22 06:12:46,912 - INFO - Starting Stage 02: Download and Organize
2025-07-22 06:12:47,652 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-22 06:12:47,652 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-22 06:12:47,665 - INFO - Synchronizing database with filesystem...
2025-07-22 06:12:47,666 - ERROR - Error running Stage 02: 'status_corrections'
2025-07-22 06:12:47,666 - ERROR - Stage 02_download_and_organize failed
2025-07-22 06:12:47,666 - ERROR - One or more stages failed
2025-07-22 06:35:48,256 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-22 06:35:48,256 - INFO - Running Stage 02: Download and Organize
2025-07-22 06:35:48,256 - INFO - Starting Stage 02: Download and Organize
2025-07-22 06:35:48,916 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-22 06:35:48,916 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-22 06:35:48,941 - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\_internal\data\movie_metadata.db
2025-07-22 06:35:48,941 - INFO - Discovering movies by scanning filesystem...
2025-07-22 06:35:48,941 - INFO - Found 0 movies across 13 stages
2025-07-22 06:35:48,941 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-22 06:35:48,941 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-22 06:35:48,941 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-22 06:35:48,948 - INFO - Retrieved 1 movies from Radarr
2025-07-22 06:35:48,950 - INFO -      Active downloads in Radarr queue: 1
2025-07-22 06:35:48,950 - INFO - Found 0 movies in download states to monitor
2025-07-22 06:35:48,950 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-22 06:35:48,951 - INFO - Found 0 movies in download states to monitor
2025-07-22 06:35:48,951 - INFO - No movies currently in download states
2025-07-22 06:35:48,951 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-22 06:35:48,951 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-22 06:35:48,951 - INFO -      No completed movies found in filesystem
2025-07-22 06:35:48,951 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-22 06:35:48,951 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
2025-07-22 06:35:48,951 - INFO - Found 0 completed downloads with database matches
2025-07-22 06:35:48,951 - INFO -      FILESYSTEM SCAN: Found 0 completed downloads with matched movies
2025-07-22 06:35:48,951 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
2025-07-22 06:35:48,954 - INFO -      Radarr API: Retrieved 1 movies for status sync
2025-07-22 06:35:48,955 - INFO - Pipeline state refreshed - found 0 movies
2025-07-22 06:35:48,955 - INFO - ===== Finished Modern Radarr Download Monitoring =====
2025-07-22 06:35:48,955 - INFO -     No new completed downloads found this run
2025-07-22 06:35:48,956 - INFO - Stage 02 completed successfully
2025-07-22 06:35:48,956 - INFO - All stages completed successfully
2025-07-22 14:53:49,350 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-22 14:53:49,350 - INFO - Running Stage 02: Download and Organize
2025-07-22 14:53:49,350 - INFO - Starting Stage 02: Download and Organize
2025-07-22 14:53:50,099 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-22 14:53:50,099 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-22 14:53:50,101 - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\_internal\data\movie_metadata.db
2025-07-22 14:53:50,101 - INFO - Discovering movies by scanning filesystem...
2025-07-22 14:53:50,101 - INFO - Found 0 movies across 13 stages
2025-07-22 14:53:50,101 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-22 14:53:50,101 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-22 14:53:50,101 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-22 14:53:50,108 - INFO - Retrieved 1 movies from Radarr
2025-07-22 14:53:50,110 - INFO -      Active downloads in Radarr queue: 1
2025-07-22 14:53:50,110 - INFO - Found 0 movies in download states to monitor
2025-07-22 14:53:50,110 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-22 14:53:50,111 - INFO - Found 0 movies in download states to monitor
2025-07-22 14:53:50,111 - INFO - No movies currently in download states
2025-07-22 14:53:50,111 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-22 14:53:50,111 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-22 14:53:50,111 - INFO -      No completed movies found in filesystem
2025-07-22 14:53:50,111 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-22 14:53:50,111 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
2025-07-22 14:53:50,111 - INFO - Found 0 completed downloads with database matches
2025-07-22 14:53:50,111 - INFO -      FILESYSTEM SCAN: Found 0 completed downloads with matched movies
2025-07-22 14:53:50,111 - INFO -      ORGANIZATION SUMMARY: 0 movies organized successfully
2025-07-22 14:53:50,118 - INFO -      Radarr API: Retrieved 1 movies for status sync
2025-07-22 14:53:50,119 - INFO - Pipeline state refreshed - found 0 movies
2025-07-22 14:53:50,119 - INFO - ===== Finished Modern Radarr Download Monitoring =====
2025-07-22 14:53:50,119 - INFO -     No new completed downloads found this run
2025-07-22 14:53:50,119 - INFO - Stage 02 completed successfully
2025-07-22 14:53:50,119 - INFO - All stages completed successfully
2025-07-23 00:15:10,548 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-23 00:15:10,548 - INFO - Running Stage 02: Download and Organize
2025-07-23 00:15:10,548 - INFO - Starting Stage 02: Download and Organize
2025-07-23 00:15:10,626 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-23 00:15:10,626 - INFO - Running Stage 02: Download and Organize
2025-07-23 00:15:10,626 - INFO - Starting Stage 02: Download and Organize
2025-07-23 00:15:11,192 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-23 00:15:11,192 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-23 00:15:11,193 - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\data\movie_metadata.db
2025-07-23 00:15:11,193 - INFO - Discovering movies by scanning filesystem...
2025-07-23 00:15:11,195 - INFO - Found 3 movies across 13 stages
2025-07-23 00:15:11,195 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,195 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-23 00:15:11,195 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-23 00:15:11,204 - INFO - Retrieved 1 movies from Radarr
2025-07-23 00:15:11,205 - INFO -      Active downloads in Radarr queue: 1
2025-07-23 00:15:11,207 - INFO - Found 0 movies in download states to monitor
2025-07-23 00:15:11,207 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-23 00:15:11,208 - INFO - Found 0 movies in download states to monitor
2025-07-23 00:15:11,208 - INFO - No movies currently in download states
2025-07-23 00:15:11,208 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-23 00:15:11,208 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,208 - INFO -      Found completed movie: Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON (28.95 GB)
2025-07-23 00:15:11,208 - INFO -      Found completed movie: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON (27.05 GB)
2025-07-23 00:15:11,208 - INFO -      FILESYSTEM DETECTION: Found 2 completed movies ready for organization
2025-07-23 00:15:11,208 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-23 00:15:11,208 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,210 - INFO -      Trying to match: Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:11,210 - INFO -      Against 3 tracked movies
2025-07-23 00:15:11,257 - INFO -      Trying to match: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:11,257 - INFO -      Against 3 tracked movies
2025-07-23 00:15:11,259 - INFO -        Source: filesystem_fallback (no prior tracking required)
2025-07-23 00:15:11,259 - INFO - Found 2 completed downloads with database matches
2025-07-23 00:15:11,259 - INFO -      FILESYSTEM SCAN: Found 2 completed downloads with matched movies
2025-07-23 00:15:11,259 - ERROR - Critical error in download monitoring: 'unique_id'
Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 782, in monitor_radarr_downloads
    movie_id = movie["unique_id"]
               ~~~~~^^^^^^^^^^^^^
KeyError: 'unique_id'
2025-07-23 00:15:11,260 - ERROR - Stage 02 failed
2025-07-23 00:15:11,260 - ERROR - Stage 02_download_and_organize failed
2025-07-23 00:15:11,260 - ERROR - One or more stages failed

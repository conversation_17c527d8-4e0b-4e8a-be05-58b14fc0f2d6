#!/usr/bin/env python3
"""
PlexMovieAutomator/src/utils/metadata_apis.py

Functions for interacting with metadata APIs like TMDb and OMDb.
"""
import logging
import requests
import time
from pathlib import Path
from utils.common_helpers import get_setting, extract_year_from_title # extract_year_from_title used internally

# Consider using a more robust library like 'tmdbsimple' or 'themoviedb'
# but direct requests are fine for controlled use.
# from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

logger = logging.getLogger(__name__) # Assumes logger is set up by calling script

TMDB_API_BASE_URL = "https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL = "https://image.tmdb.org/t/p/" # Add size and path, e.g., w500/poster.jpg

# Basic retry decorator (can be made more specific with tenacity)
def retry_request(max_tries=3, delay_seconds=2, backoff_factor=2):
    def decorator(func):
        def wrapper(*args, **kwargs):
            tries = 0
            while tries < max_tries:
                try:
                    return func(*args, **kwargs)
                except requests.exceptions.RequestException as e:
                    tries += 1
                    logger.warning(f"Request failed (attempt {tries}/{max_tries}): {e}. Retrying in {delay_seconds}s...")
                    if tries >= max_tries:
                        logger.error(f"Max retries reached for {func.__name__}. Last error: {e}")
                        raise # Or return a specific error indicator
                    time.sleep(delay_seconds)
                    delay_seconds *= backoff_factor
        return wrapper
    return decorator

@retry_request()
def _make_tmdb_api_request(endpoint: str, api_key: str, params: dict = None):
    """Makes a GET request to TMDb API, handles common errors and retries."""
    if not api_key:
        logger.error("TMDb API key not provided for TMDb request.")
        return None # Or raise ValueError

    full_url = f"{TMDB_API_BASE_URL}/{endpoint.strip('/')}"
    request_params = {"api_key": api_key}
    if params:
        request_params.update(params)

    logger.debug(f"Requesting TMDb: {full_url} with params: {request_params}")
    response = requests.get(full_url, params=request_params, timeout=15)
    response.raise_for_status() # Will raise HTTPError for 4xx/5xx
    return response.json()

def search_movie_tmdb(title_query: str, year: int = None, api_key: str = None, settings_dict=None) -> list[dict]:
    """Searches TMDb for movies."""
    if not api_key:
        api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict)
    if not api_key:
        logger.error("TMDb API Key is missing for search_movie_tmdb.")
        return []

    params = {"query": title_query, "language": "en-US", "page": 1}
    if year:
        params["year"] = year # TMDb search also supports primary_release_year

    try:
        data = _make_tmdb_api_request("search/movie", api_key, params)
        results = data.get("results", []) if data else []
        logger.info(f"TMDb search for '{title_query}' (Year: {year}) found {len(results)} results.")
        return results
    except requests.exceptions.HTTPError as e:
        logger.error(f"TMDb search HTTP error for '{title_query}': {e.response.status_code} - {e.response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"TMDb search request error for '{title_query}': {e}")
    except Exception as e: # Catch any other unexpected error during processing
        logger.error(f"Unexpected error in search_movie_tmdb for '{title_query}': {e}")
    return []


def get_movie_details_tmdb(tmdb_id: str | int, api_key: str = None, settings_dict=None) -> dict | None:
    """Fetches detailed movie information from TMDb including external IDs."""
    if not tmdb_id: return None
    if not api_key:
        api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict)
    if not api_key:
        logger.error("TMDb API Key is missing for get_movie_details_tmdb.")
        return None

    endpoint = f"movie/{tmdb_id}"
    params = {"language": "en-US", "append_to_response": "external_ids,release_dates,credits,keywords"}
    
    try:
        details = _make_tmdb_api_request(endpoint, api_key, params)
        if details:
            logger.info(f"Fetched details for TMDb ID {tmdb_id}: {details.get('title')}")
        return details
    except requests.exceptions.HTTPError as e:
        logger.error(f"TMDb details HTTP error for ID {tmdb_id}: {e.response.status_code} - {e.response.text}")
    except requests.exceptions.RequestException as e:
        logger.error(f"TMDb details request error for ID {tmdb_id}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error in get_movie_details_tmdb for ID {tmdb_id}: {e}")
    return None


def fetch_movie_metadata_for_intake(raw_title_from_user: str, settings_dict: dict) -> dict:
    """
    Orchestrates metadata fetching: cleans title, searches TMDb, gets details.
    Returns a standardized dictionary for the state manager.
    """
    logger_instance = logging.getLogger(__name__) # Use local logger
    
    tmdb_api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings_dict, required=True)
    if not tmdb_api_key: # Should be caught by required=True, but defensive check
        return {"success": False, "error": "TMDb API Key not configured."}

    # 1. Clean raw title and extract year if present
    # Using the helper from common_helpers (assuming it's imported or defined here)
    title_to_search, year_from_title = extract_year_from_title(raw_title_from_user)
    logger_instance.debug(f"Parsed intake: Title='{title_to_search}', Year='{year_from_title}' from '{raw_title_from_user}'")

    # 2. Search TMDb
    search_results = search_movie_tmdb(title_to_search, year_from_title, tmdb_api_key, settings_dict)

    if not search_results:
        # If search with year failed, try without year (if year was originally parsed)
        if year_from_title:
            logger_instance.info(f"No results for '{title_to_search} ({year_from_title})'. Retrying search without year.")
            search_results = search_movie_tmdb(title_to_search, None, tmdb_api_key, settings_dict)
        if not search_results:
            logger_instance.warning(f"No TMDb search results found for '{raw_title_from_user}'.")
            return {"success": False, "error": f"No TMDb results found for '{raw_title_from_user}'"}

    # 3. Select best match (simple: first result, could be more complex)
    # More advanced: filter by year match if year_from_title, then by exact title match, then popularity.
    # For now, let's take the first result if year matches, or first overall.
    best_match = None
    if year_from_title:
        for res in search_results:
            release_date = res.get("release_date", "")
            if release_date and release_date.startswith(str(year_from_title)):
                best_match = res
                break
    if not best_match: # Fallback to first result if no year match or no year provided
        best_match = search_results[0]
    
    tmdb_id_found = best_match.get("id")
    if not tmdb_id_found:
        logger_instance.error(f"No TMDb ID found in best search result for '{raw_title_from_user}'.")
        return {"success": False, "error": "TMDb ID missing in search result."}

    logger_instance.info(f"Selected TMDb match for '{raw_title_from_user}': ID={tmdb_id_found}, Title='{best_match.get('title')}'")

    # 4. Get Full Details from TMDb
    full_details = get_movie_details_tmdb(tmdb_id_found, tmdb_api_key, settings_dict)
    if not full_details:
        logger_instance.error(f"Failed to fetch full details for TMDb ID {tmdb_id_found}.")
        return {"success": False, "error": f"Failed to get details for TMDb ID {tmdb_id_found}."}

    # 5. Structure and Return Data
    title = full_details.get("title")
    release_date_str = full_details.get("release_date", "")
    year = int(release_date_str[:4]) if release_date_str and release_date_str[:4].isdigit() else None
    
    if not title or not year: # Critical fields
        logger_instance.error(f"Essential metadata (title/year) missing from TMDb details for ID {tmdb_id_found}.")
        return {"success": False, "error": "Title or year missing in TMDb details."}

    # Construct poster URL
    poster_path_segment = full_details.get("poster_path")
    full_poster_url = None
    if poster_path_segment:
         # Poster size can be a setting, e.g., w500
        poster_size = get_setting("TMDb", "poster_default_size", default="w500", settings_dict=settings_dict)
        image_base = get_setting("TMDb", "image_base_url", default="https://image.tmdb.org/t/p/", settings_dict=settings_dict)
        full_poster_url = f"{image_base.strip('/')}/{poster_size.strip('/')}{poster_path_segment}"


    return {
        "success": True,
        "cleaned_title": title,
        "year": year,
        "tmdb_id": str(tmdb_id_found), # Ensure string for consistency
        "imdb_id": full_details.get("external_ids", {}).get("imdb_id"),
        "genres": [g["name"] for g in full_details.get("genres", [])],
        "overview": full_details.get("overview"),
        "poster_url_tmdb": full_poster_url,
        "metadata_source": "TMDb",
        "error": None
    }

def download_image_from_url(image_url: str, save_path_str: str | Path, logger_instance: logging.Logger = None) -> bool:
    """Downloads an image from a URL and saves it."""
    current_logger = logger_instance if logger_instance else logging.getLogger(__name__)
    if not image_url:
        current_logger.warning("No image URL provided for download.")
        return False
    
    save_path = Path(save_path_str)
    ensure_dir_exists(str(save_path.parent), logger_instance=current_logger)

    try:
        response = requests.get(image_url, stream=True, timeout=20)
        response.raise_for_status()
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        current_logger.info(f"Image downloaded successfully to {save_path}")
        return True
    except requests.exceptions.RequestException as e:
        current_logger.error(f"Failed to download image from {image_url}: {e}")
        return False
    except IOError as e:
        current_logger.error(f"Failed to save image to {save_path}: {e}")
        return False
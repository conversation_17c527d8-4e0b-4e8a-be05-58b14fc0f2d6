cupy-cuda12x
triton==3.1.0; (platform_system != "Windows" and python_version <= "3.8")
triton==3.2.0; (platform_system != "Windows" and python_version >= "3.9")
torch
--extra-index-url https://pypi.ngc.nvidia.com
polygraphy
colored
numpy==1.23.5; (platform_system != "Windows" and python_version <= "3.10")
numpy==1.26.4; (platform_system != "Windows" and python_version >= "3.11")
pyyaml==6.0.1
requests==2.32.2
tqdm==4.66.4

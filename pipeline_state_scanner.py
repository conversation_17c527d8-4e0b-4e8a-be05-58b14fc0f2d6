#!/usr/bin/env python3
"""
Pipeline State Scanner & Fixer - Filesystem-First Architecture
==============================================================

This script scans the entire movie processing pipeline and automatically creates
the appropriate marker files based on what content is actually present in each folder.

This is particularly useful when:
- Adding manually processed movies to the pipeline
- Recovering from interrupted processing
- Migrating existing movie collections into the pipeline
- Testing and development work

Features:
- Scans all pipeline stage directories
- Analyzes actual file content to determine processing state
- Creates appropriate marker files automatically
- Provides detailed reports of what was found and fixed
- Safe dry-run mode to preview changes
- Backup of existing markers before making changes

Author: GitHub Copilot
Date: July 2025
"""

import os
import sys
import json
import logging
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Add the _internal directory to the Python path for imports
sys.path.append(str(Path(__file__).parent / "_internal"))

try:
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    print("Error: Could not import filesystem-first state management components.")
    print("Please ensure the filesystem_first_state_manager.py is available.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/pipeline_scanner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class MovieAnalysis:
    """Analysis results for a movie directory"""
    movie_directory: Path
    title: str
    year: str
    resolution: str
    current_stage: str
    detected_stage: str
    files_found: List[str]
    existing_markers: List[str]
    recommended_markers: List[str]
    needs_fixing: bool
    issues: List[str]


class PipelineStateScanner:
    """Scans and fixes movie pipeline state markers"""
    
    def __init__(self, workspace_path: Optional[str] = None):
        """
        Initialize the pipeline scanner
        
        Args:
            workspace_path: Base workspace directory path
        """
        self.workspace_path = Path(workspace_path) if workspace_path else Path.cwd()
        
        # Initialize filesystem-first state manager
        self.fs_manager = FilesystemFirstStateManager(self.workspace_path)
        
        # Initialize metadata database
        self.metadata_db = MetadataOnlyDatabase(self.workspace_path)
        
        logger.info(f"Pipeline scanner initialized for workspace: {self.workspace_path}")

    def scan_all_pipeline_folders(self) -> Dict[str, List[MovieAnalysis]]:
        """
        Scan all pipeline directories and analyze movie states
        
        Returns:
            Dictionary mapping stage names to lists of movie analyses
        """
        print("🔍 Scanning entire movie pipeline...")
        print(f"Workspace: {self.workspace_path}")
        print(f"{'='*60}")
        
        all_analyses = {}
        stage_directories = self.fs_manager.stage_directories
        
        total_movies = 0
        movies_needing_fixes = 0
        
        for stage_name, stage_path in stage_directories.items():
            print(f"\n📁 Scanning stage: {stage_name}")
            print(f"   Path: {stage_path}")
            
            if not stage_path.exists():
                print(f"   ⚠️  Directory does not exist - creating it")
                stage_path.mkdir(parents=True, exist_ok=True)
                all_analyses[stage_name] = []
                continue
            
            stage_analyses = self.scan_stage_directory(stage_name, stage_path)
            all_analyses[stage_name] = stage_analyses
            
            stage_total = len(stage_analyses)
            stage_needing_fixes = sum(1 for analysis in stage_analyses if analysis.needs_fixing)
            
            total_movies += stage_total
            movies_needing_fixes += stage_needing_fixes
            
            print(f"   Found: {stage_total} movies")
            if stage_needing_fixes > 0:
                print(f"   ⚠️  Need fixes: {stage_needing_fixes}")
            else:
                print(f"   ✅ All good")
        
        print(f"\n{'='*60}")
        print(f"📊 Scan Summary:")
        print(f"   Total movies found: {total_movies}")
        print(f"   Movies needing fixes: {movies_needing_fixes}")
        print(f"   Stages scanned: {len(stage_directories)}")
        
        return all_analyses

    def scan_stage_directory(self, stage_name: str, stage_path: Path) -> List[MovieAnalysis]:
        """
        Scan a specific stage directory for movies
        
        Args:
            stage_name: Name of the processing stage
            stage_path: Path to the stage directory
            
        Returns:
            List of movie analyses for this stage
        """
        analyses = []
        
        # Look for resolution subdirectories (1080p, 4k, etc.)
        resolution_dirs = [d for d in stage_path.iterdir() if d.is_dir() and d.name.lower() in ['1080p', '4k', '720p', '2160p']]
        
        if resolution_dirs:
            # Stage has resolution subdirectories
            for res_dir in resolution_dirs:
                movie_dirs = [d for d in res_dir.iterdir() if d.is_dir()]
                for movie_dir in movie_dirs:
                    analysis = self.analyze_movie_directory(movie_dir, stage_name, res_dir.name)
                    if analysis:
                        analyses.append(analysis)
        else:
            # Stage may have direct movie directories
            movie_dirs = [d for d in stage_path.iterdir() if d.is_dir()]
            for movie_dir in movie_dirs:
                analysis = self.analyze_movie_directory(movie_dir, stage_name, "unknown")
                if analysis:
                    analyses.append(analysis)
        
        return analyses

    def analyze_movie_directory(self, movie_dir: Path, stage_name: str, resolution: str) -> Optional[MovieAnalysis]:
        """
        Analyze a single movie directory to determine its state
        
        Args:
            movie_dir: Path to movie directory
            stage_name: Current stage name
            resolution: Resolution (1080p, 4k, etc.)
            
        Returns:
            MovieAnalysis object or None if not a valid movie directory
        """
        try:
            # Extract title and year from directory name
            dir_name = movie_dir.name
            title, year = self.extract_title_year(dir_name)
            
            if not title:
                return None  # Skip non-movie directories
            
            # Find what files are present
            files_found = []
            video_files = list(movie_dir.glob("*.mkv")) + list(movie_dir.glob("*.mp4")) + list(movie_dir.glob("*.avi"))
            subtitle_files = list(movie_dir.glob("*.srt")) + list(movie_dir.glob("*.sup"))
            audio_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'audio' in d.name.lower()]
            subtitle_dirs = [d for d in movie_dir.iterdir() if d.is_dir() and 'subtitle' in d.name.lower()]
            
            for vf in video_files:
                files_found.append(f"video: {vf.name}")
            for sf in subtitle_files:
                files_found.append(f"subtitle: {sf.name}")
            for ad in audio_dirs:
                files_found.append(f"audio_dir: {ad.name}")
            for sd in subtitle_dirs:
                files_found.append(f"subtitle_dir: {sd.name}")
            
            # Check existing markers
            existing_markers = []
            marker_files = ['.mkv_processing', '.mkv_complete', '.subtitle_processing', '.subtitle_complete', '.error']
            for marker in marker_files:
                if (movie_dir / marker).exists():
                    existing_markers.append(marker)
            
            # Detect what stage this movie should be in based on content
            detected_stage, recommended_markers, issues = self.detect_movie_stage(
                movie_dir, video_files, subtitle_files, audio_dirs, subtitle_dirs
            )
            
            # Determine if fixes are needed
            needs_fixing = (
                len(recommended_markers) > 0 and 
                set(recommended_markers) != set(existing_markers)
            ) or len(issues) > 0
            
            return MovieAnalysis(
                movie_directory=movie_dir,
                title=title,
                year=year,
                resolution=resolution,
                current_stage=stage_name,
                detected_stage=detected_stage,
                files_found=files_found,
                existing_markers=existing_markers,
                recommended_markers=recommended_markers,
                needs_fixing=needs_fixing,
                issues=issues
            )
            
        except Exception as e:
            logger.warning(f"Error analyzing {movie_dir}: {e}")
            return None

    def extract_title_year(self, dir_name: str) -> Tuple[str, str]:
        """Extract movie title and year from directory name"""
        import re
        
        # Common patterns: "Movie Title (2023)" or "Movie Title 2023"
        patterns = [
            r'^(.+?)\s*\((\d{4})\).*$',  # Title (Year)
            r'^(.+?)\s+(\d{4}).*$',      # Title Year
        ]
        
        for pattern in patterns:
            match = re.match(pattern, dir_name)
            if match:
                title = match.group(1).strip()
                year = match.group(2)
                return title, year
        
        # If no year found, treat whole name as title
        if any(word in dir_name.lower() for word in ['movie', 'film', 'dvd', 'bluray']):
            return dir_name, "unknown"
        
        return "", ""  # Not a movie directory

    def detect_movie_stage(self, movie_dir: Path, video_files: List[Path], 
                          subtitle_files: List[Path], audio_dirs: List[Path], 
                          subtitle_dirs: List[Path]) -> Tuple[str, List[str], List[str]]:
        """
        Detect what stage a movie should be in based on its content
        
        Returns:
            Tuple of (detected_stage, recommended_markers, issues)
        """
        recommended_markers = []
        issues = []
        
        # Analyze video files
        processed_video = any('processed' in vf.name.lower() for vf in video_files)
        raw_video = any('processed' not in vf.name.lower() for vf in video_files)
        
        # Analyze subtitle content
        has_srt_subtitles = len([sf for sf in subtitle_files if sf.suffix.lower() == '.srt']) > 0
        has_sup_subtitles = len([sf for sf in subtitle_files if sf.suffix.lower() == '.sup']) > 0
        has_subtitle_dirs = len(subtitle_dirs) > 0
        
        # Analyze audio content
        has_audio_dirs = len(audio_dirs) > 0
        
        # Determine stage based on content analysis
        if processed_video and has_subtitle_dirs and has_audio_dirs:
            # Fully processed - ready for encoding
            detected_stage = "mkv_processing_complete"
            recommended_markers = ['.mkv_complete']
            
        elif processed_video and (has_srt_subtitles or has_subtitle_dirs):
            # Video and subtitles processed
            detected_stage = "mkv_processing_complete" 
            recommended_markers = ['.mkv_complete']
            
        elif processed_video:
            # Video processed but may need subtitle work
            if has_sup_subtitles and not has_srt_subtitles:
                detected_stage = "subtitle_processing_needed"
                recommended_markers = ['.subtitle_processing']
                issues.append("Has .sup files that may need conversion to .srt")
            else:
                detected_stage = "mkv_processing_complete"
                recommended_markers = ['.mkv_complete']
                
        elif raw_video and has_audio_dirs:
            # Raw video with extracted audio - in MKV processing
            detected_stage = "mkv_processing_in_progress"
            recommended_markers = ['.mkv_processing']
            
        elif raw_video and (has_srt_subtitles or has_sup_subtitles):
            # Raw video with subtitles - ready for MKV processing
            detected_stage = "ready_for_mkv_processing"
            # No markers needed - this is the input state
            
        elif raw_video:
            # Just raw video - early stage
            detected_stage = "downloaded_and_organized"
            # No markers needed - this is an early input state
            
        else:
            detected_stage = "unknown"
            issues.append("Could not determine processing stage from content")
        
        return detected_stage, recommended_markers, issues

    def generate_detailed_report(self, all_analyses: Dict[str, List[MovieAnalysis]]) -> str:
        """Generate a detailed report of the scan results"""
        
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("MOVIE PIPELINE STATE ANALYSIS REPORT")
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("=" * 80)
        
        total_movies = sum(len(analyses) for analyses in all_analyses.values())
        total_needing_fixes = sum(sum(1 for analysis in analyses if analysis.needs_fixing) 
                                 for analyses in all_analyses.values())
        
        report_lines.append(f"\nOVERVIEW:")
        report_lines.append(f"  Total movies found: {total_movies}")
        report_lines.append(f"  Movies needing fixes: {total_needing_fixes}")
        report_lines.append(f"  Stages scanned: {len(all_analyses)}")
        
        # Stage-by-stage breakdown
        for stage_name, analyses in all_analyses.items():
            if not analyses:
                continue
                
            report_lines.append(f"\n{'-' * 60}")
            report_lines.append(f"STAGE: {stage_name.upper()}")
            report_lines.append(f"{'-' * 60}")
            
            needing_fixes = [a for a in analyses if a.needs_fixing]
            
            report_lines.append(f"Movies in stage: {len(analyses)}")
            if needing_fixes:
                report_lines.append(f"Needing fixes: {len(needing_fixes)}")
                
                for analysis in needing_fixes:
                    report_lines.append(f"\n  📁 {analysis.title} ({analysis.year}) [{analysis.resolution}]")
                    report_lines.append(f"     Directory: {analysis.movie_directory.name}")
                    report_lines.append(f"     Current stage: {analysis.current_stage}")
                    report_lines.append(f"     Detected stage: {analysis.detected_stage}")
                    
                    if analysis.files_found:
                        report_lines.append(f"     Files found:")
                        for file_info in analysis.files_found[:5]:  # Limit to first 5
                            report_lines.append(f"       • {file_info}")
                        if len(analysis.files_found) > 5:
                            report_lines.append(f"       • ... and {len(analysis.files_found) - 5} more")
                    
                    if analysis.existing_markers:
                        report_lines.append(f"     Existing markers: {', '.join(analysis.existing_markers)}")
                    else:
                        report_lines.append(f"     Existing markers: None")
                    
                    if analysis.recommended_markers:
                        report_lines.append(f"     Recommended markers: {', '.join(analysis.recommended_markers)}")
                    
                    if analysis.issues:
                        report_lines.append(f"     Issues:")
                        for issue in analysis.issues:
                            report_lines.append(f"       ⚠️  {issue}")
            else:
                report_lines.append("All movies in this stage have correct markers. ✅")
        
        return "\n".join(report_lines)

    def apply_fixes(self, all_analyses: Dict[str, List[MovieAnalysis]], dry_run: bool = True) -> Dict[str, Any]:
        """
        Apply the recommended marker fixes
        
        Args:
            all_analyses: Analysis results from scan
            dry_run: If True, only show what would be done
            
        Returns:
            Dictionary with fix results
        """
        if dry_run:
            print(f"\n🔍 DRY RUN MODE - Showing what would be fixed:")
        else:
            print(f"\n🔧 APPLYING FIXES...")
        
        print(f"{'='*60}")
        
        fixes_applied = 0
        errors_encountered = 0
        fix_details = []
        
        for stage_name, analyses in all_analyses.items():
            needing_fixes = [a for a in analyses if a.needs_fixing]
            
            if not needing_fixes:
                continue
                
            print(f"\n📁 Stage: {stage_name}")
            
            for analysis in needing_fixes:
                print(f"\n  🎬 {analysis.title} ({analysis.year})")
                
                try:
                    # Backup existing markers if not dry run
                    if not dry_run and analysis.existing_markers:
                        self.backup_existing_markers(analysis.movie_directory, analysis.existing_markers)
                    
                    # Remove conflicting markers
                    markers_to_remove = set(analysis.existing_markers) - set(analysis.recommended_markers)
                    for marker in markers_to_remove:
                        if dry_run:
                            print(f"     Would remove: {marker}")
                        else:
                            marker_file = analysis.movie_directory / marker
                            if marker_file.exists():
                                marker_file.unlink()
                                print(f"     ❌ Removed: {marker}")
                    
                    # Add recommended markers
                    for marker in analysis.recommended_markers:
                        if marker not in analysis.existing_markers:
                            if dry_run:
                                print(f"     Would add: {marker}")
                            else:
                                self.create_marker_file(analysis.movie_directory, marker, {
                                    'created_by': 'pipeline_scanner',
                                    'timestamp': datetime.now().isoformat(),
                                    'detected_stage': analysis.detected_stage,
                                    'auto_generated': True
                                })
                                print(f"     ✅ Added: {marker}")
                    
                    # Update metadata if needed
                    if not dry_run:
                        self.update_movie_metadata(analysis)
                    
                    fixes_applied += 1
                    fix_details.append({
                        'movie': f"{analysis.title} ({analysis.year})",
                        'directory': str(analysis.movie_directory),
                        'markers_added': [m for m in analysis.recommended_markers if m not in analysis.existing_markers],
                        'markers_removed': list(markers_to_remove),
                        'detected_stage': analysis.detected_stage
                    })
                    
                except Exception as e:
                    logger.error(f"Error fixing {analysis.title}: {e}")
                    print(f"     ❌ Error: {e}")
                    errors_encountered += 1
        
        action_word = "Would fix" if dry_run else "Fixed"
        print(f"\n{'='*60}")
        print(f"📊 SUMMARY:")
        print(f"   {action_word}: {fixes_applied} movies")
        if errors_encountered > 0:
            print(f"   Errors: {errors_encountered}")
        
        return {
            'fixes_applied': fixes_applied,
            'errors_encountered': errors_encountered,
            'fix_details': fix_details,
            'dry_run': dry_run
        }

    def backup_existing_markers(self, movie_dir: Path, markers: List[str]):
        """Create backup of existing marker files"""
        backup_dir = movie_dir / ".marker_backup"
        backup_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        for marker in markers:
            marker_file = movie_dir / marker
            if marker_file.exists():
                backup_file = backup_dir / f"{marker}.{timestamp}.bak"
                shutil.copy2(marker_file, backup_file)

    def create_marker_file(self, movie_dir: Path, marker: str, data: Dict[str, Any]):
        """Create a marker file with metadata"""
        marker_file = movie_dir / marker
        
        # Create marker with metadata
        with open(marker_file, 'w') as f:
            json.dump(data, f, indent=2)

    def update_movie_metadata(self, analysis: MovieAnalysis):
        """Update movie metadata in the database"""
        try:
            unique_id = f"{analysis.title}_{analysis.year}_{analysis.resolution}"
            
            metadata = {
                'resolution': analysis.resolution,
                'detected_stage': analysis.detected_stage,
                'files_found': analysis.files_found,
                'scanner_updated': datetime.now().isoformat(),
                'auto_detected': True
            }
            
            self.metadata_db.save_movie_metadata(
                unique_id=unique_id,
                title=analysis.title,
                year=int(analysis.year) if analysis.year.isdigit() else None,
                metadata=metadata
            )
            
        except Exception as e:
            logger.warning(f"Could not update metadata for {analysis.title}: {e}")

    def prompt_yes_no(self, question: str, default: bool = True) -> bool:
        """Prompt user for yes/no response"""
        default_text = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_text}]: ").upper().strip()
        
        if response == "":
            return default
        elif response in ["Y", "YES"]:
            return True
        elif response in ["N", "NO"]:
            return False
        else:
            print("Please answer yes or no.")
            return self.prompt_yes_no(question, default)


def main():
    """Main entry point for the pipeline scanner"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Movie Pipeline State Scanner & Fixer")
    parser.add_argument("--workspace", help="Workspace base directory", default=".")
    parser.add_argument("--scan-only", action="store_true", help="Only scan and report, don't fix anything")
    parser.add_argument("--auto-fix", action="store_true", help="Automatically apply all recommended fixes")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be fixed without making changes")
    parser.add_argument("--save-report", help="Save detailed report to file")
    
    args = parser.parse_args()
    
    try:
        # Initialize scanner
        scanner = PipelineStateScanner(workspace_path=args.workspace)
        
        # Scan all pipeline folders
        all_analyses = scanner.scan_all_pipeline_folders()
        
        # Generate detailed report
        report = scanner.generate_detailed_report(all_analyses)
        print(f"\n{report}")
        
        # Save report if requested
        if args.save_report:
            with open(args.save_report, 'w') as f:
                f.write(report)
            print(f"\n📄 Report saved to: {args.save_report}")
        
        # Check if any fixes are needed
        total_needing_fixes = sum(sum(1 for analysis in analyses if analysis.needs_fixing) 
                                 for analyses in all_analyses.values())
        
        if total_needing_fixes == 0:
            print(f"\n✅ All movies have correct markers! No fixes needed.")
            return
        
        if args.scan_only:
            print(f"\n📊 Scan complete. Use --dry-run to see what would be fixed.")
            return
        
        # Apply fixes
        if args.auto_fix:
            fix_results = scanner.apply_fixes(all_analyses, dry_run=False)
        elif args.dry_run:
            fix_results = scanner.apply_fixes(all_analyses, dry_run=True)
        else:
            # Interactive mode
            if scanner.prompt_yes_no(f"\nApply fixes to {total_needing_fixes} movies?", default=True):
                fix_results = scanner.apply_fixes(all_analyses, dry_run=False)
            else:
                print("No fixes applied.")
                return
        
        print(f"\n✅ Pipeline state scanning complete!")
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

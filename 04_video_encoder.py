#!/usr/bin/env python3
"""
HandBrake Video Re-encoding Script for Media Pipeline - Filesystem-First Architecture
=====================================================================================

This script automates video re-encoding using HandBrakeCLI as part of a media processing pipeline.
It uses filesystem-first state management with marker files as the single source of truth.

Features:
- Filesystem-based movie discovery and state management
- Marker files for tracking processing stages (.mkv_processing, .mkv_complete)
- Resolution-based encoding presets (1080p/4K)
- Dynamic bitrate calculation with quality floors
- Interactive user confirmation and parameter override
- Idempotent operations with auto-recovery from interruptions
- Metadata-only database for static information
- Audio passthrough preservation

Author: GitHub Copilot
Date: July 2025
"""

import os
import sys
import json
import logging
import subprocess
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# Add the _internal directory to the Python path for imports
sys.path.append(str(Path(__file__).parent / "_internal"))

try:
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    print("Error: Could not import filesystem-first state management components.")
    print("Please ensure the filesystem_first_state_manager.py is available.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('_internal/logs/video_encoder.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class EncodingParameters:
    """Container for video encoding parameters"""
    preset: str = "slow"
    target_bitrate_kbps: int = 9000
    encoder_profile: str = "main"
    encoder_level: str = "5.1"
    turbo_first_pass: bool = True
    resolution: str = "1080p"
    handbrake_preset: str = "H.265 MKV 1080p30"


@dataclass
class MovieInfo:
    """Container for movie information discovered from filesystem"""
    unique_id: str
    title: str
    year: str
    resolution: str
    movie_directory: Path
    main_movie_file: Optional[Path]
    processed_content: Optional[Path]
    current_stage: str
    duration_seconds: float = 7200.0  # Default 2 hours
    file_size_bytes: int = 0


class FilesystemFirstVideoEncoder:
    """Main class for handling video encoding operations using filesystem-first architecture"""
    
    def __init__(self, handbrake_path: Optional[str] = None, workspace_path: Optional[str] = None):
        """
        Initialize the video encoder with filesystem-first state management
        
        Args:
            handbrake_path: Path to HandBrakeCLI.exe
            workspace_path: Base workspace directory path
        """
        self.workspace_path = Path(workspace_path) if workspace_path else Path.cwd()
        
        # Initialize filesystem-first state manager
        self.fs_manager = FilesystemFirstStateManager(self.workspace_path)
        
        # Initialize metadata-only database
        self.metadata_db = MetadataOnlyDatabase(self.workspace_path)
        
        # Set HandBrakeCLI path
        if handbrake_path:
            self.handbrake_path = Path(handbrake_path)
        else:
            # Default to the tools directory
            self.handbrake_path = Path(__file__).parent / "_internal" / "tools" / "HandBrakeCLI.exe"
        
        # Verify HandBrakeCLI exists
        if not self.handbrake_path.exists():
            raise FileNotFoundError(f"HandBrakeCLI not found at: {self.handbrake_path}")
        
        # Define minimum bitrate floors (in kbps)
        self.min_bitrate_1080p = 9000  # 9 Mbps
        self.min_bitrate_4k = 20000    # 20 Mbps
        self.default_4k_bitrate = 20000  # 20 Mbps default for 4K
        
        # Compression ratio for high-bitrate sources
        self.compression_ratio = 0.5  # 50% of original
        
        logger.info(f"Filesystem-first video encoder initialized")
        logger.info(f"HandBrakeCLI path: {self.handbrake_path}")
        logger.info(f"Workspace: {self.workspace_path}")

    def discover_movies_for_encoding(self) -> List[MovieInfo]:
        """
        Discover movies ready for video encoding using filesystem scanning
        
        Returns:
            List of MovieInfo objects ready for encoding
        """
        logger.info("Discovering movies ready for video encoding...")
        
        # Get all movies by stage from filesystem
        all_movies = self.fs_manager.discover_movies_by_stage()
        
        # Find movies ready for encoding
        encoding_candidates = []
        
        # 1. Movies with completed MKV processing (ready for encoding)
        # These are movies with .mkv_complete marker (detected as 'subtitle_processing_pending')
        mkv_complete_movies = all_movies.get('subtitle_processing_pending', [])
        for movie_data in mkv_complete_movies:
            movie_info = self._convert_to_movie_info(movie_data)
            if movie_info:
                encoding_candidates.append(movie_info)
        
        # 2. Movies with interrupted encoding (can be resumed)
        interrupted_movies = []
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    # Check for interrupted encoding (.mkv_processing without .mkv_complete)
                    if (movie_dir / '.mkv_processing').exists() and not (movie_dir / '.mkv_complete').exists():
                        movie_info = self._convert_to_movie_info(movie_data)
                        if movie_info:
                            movie_info.current_stage = 'mkv_processing_interrupted'
                            interrupted_movies.append(movie_info)
        
        if interrupted_movies:
            logger.warning(f"Found {len(interrupted_movies)} movies with interrupted encoding")
            print(f"\n⚠️  Found {len(interrupted_movies)} movies with interrupted encoding:")
            for movie in interrupted_movies:
                print(f"   • {movie.title} ({movie.year})")
            
            if self.prompt_yes_no("Reset interrupted movies for retry?", default=True):
                for movie in interrupted_movies:
                    # Clear processing marker to allow retry
                    self.fs_manager.clear_stage_marker(movie.movie_directory, 'mkv_processing')
                    encoding_candidates.append(movie)
                logger.info(f"Reset {len(interrupted_movies)} interrupted movies for retry")
        
        logger.info(f"Found {len(encoding_candidates)} movies ready for encoding")
        return encoding_candidates

    def _convert_to_movie_info(self, movie_data: Dict[str, Any]) -> Optional[MovieInfo]:
        """Convert filesystem discovery data to MovieInfo object"""
        try:
            movie_dir = Path(movie_data.get('movie_directory', ''))
            if not movie_dir.exists():
                return None
            
            # Generate unique ID
            unique_id = self.fs_manager.generate_movie_identifier(movie_data)
            
            # Find main movie file
            main_movie_file = None
            movie_files = list(movie_dir.glob("*.mkv"))
            if movie_files:
                # Prefer processed files if they exist
                processed_files = [f for f in movie_files if 'processed' in f.name.lower()]
                main_movie_file = processed_files[0] if processed_files else movie_files[0]
            
            # Check for processed content directory
            processed_content = None
            processed_dir = movie_dir / "_Processed_VideoAudio"
            if processed_dir.exists():
                processed_content = processed_dir
            
            # Get file size and estimate duration
            file_size_bytes = 0
            duration_seconds = 7200.0  # Default 2 hours
            
            if main_movie_file and main_movie_file.exists():
                file_size_bytes = main_movie_file.stat().st_size
                # Try to get actual duration using ffprobe if available, otherwise use MediaInfo logic
                actual_duration = self._get_actual_duration(main_movie_file)
                if actual_duration:
                    duration_seconds = actual_duration
                else:
                    # Improved duration estimate based on typical movie bitrates
                    # For processed files, assume higher bitrate (15-25 Mbps range)
                    if 'processed' in main_movie_file.name.lower():
                        # Processed files typically have higher bitrates
                        estimated_bitrate_mbps = 20  # Assume 20 Mbps for processed files
                    else:
                        # Raw files might be lower
                        estimated_bitrate_mbps = 15  # Assume 15 Mbps for raw files
                    
                    if file_size_bytes > 0:
                        duration_seconds = max((file_size_bytes * 8) / (estimated_bitrate_mbps * 1000 * 1000), 600)  # Min 10 minutes
            
            # Determine resolution from directory structure or metadata
            resolution = self._determine_resolution(movie_dir, movie_data)
            
            return MovieInfo(
                unique_id=unique_id,
                title=movie_data.get('cleaned_title', 'Unknown'),
                year=str(movie_data.get('year', 'Unknown')),
                resolution=resolution,
                movie_directory=movie_dir,
                main_movie_file=main_movie_file,
                processed_content=processed_content,
                current_stage=movie_data.get('current_stage', 'subtitle_processing_pending'),  # Use filesystem stage
                duration_seconds=duration_seconds,
                file_size_bytes=file_size_bytes
            )
            
        except Exception as e:
            logger.error(f"Error converting movie data: {e}")
            return None

    def _determine_resolution(self, movie_dir: Path, movie_data: Dict[str, Any]) -> str:
        """Determine movie resolution from directory structure or metadata"""
        # Check if movie is in a resolution-specific directory
        path_str = str(movie_dir).lower()
        if '4k' in path_str or '2160p' in path_str:
            return '4K'
        elif '1080p' in path_str:
            return '1080p'
        elif '720p' in path_str:
            return '720p'
        
        # Try to get from metadata database
        unique_id = self.fs_manager.generate_movie_identifier(movie_data)
        metadata = self.metadata_db.get_movie_metadata(unique_id)
        if metadata and metadata.get('metadata', {}).get('resolution'):
            return metadata['metadata']['resolution']
        
        # Default assumption
        return '1080p'

    def _get_actual_duration(self, video_file: Path) -> Optional[float]:
        """
        Try to get actual video duration using ffprobe
        
        Args:
            video_file: Path to video file
            
        Returns:
            Duration in seconds, or None if unable to determine
        """
        try:
            import subprocess
            
            # Try ffprobe first
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-select_streams", "v:0",
                "-show_entries", "format=duration",
                "-of", "csv=p=0",
                str(video_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration = float(result.stdout.strip())
                logger.info(f"Got actual duration from ffprobe: {duration:.1f} seconds ({duration/60:.1f} minutes)")
                return duration
                
        except Exception as e:
            logger.debug(f"Could not get duration with ffprobe: {e}")
        
        # Try alternative method with MediaInfo if available
        try:
            cmd = [
                "mediainfo",
                "--Output=General;%Duration%",
                str(video_file)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
            if result.returncode == 0 and result.stdout.strip():
                duration_ms = float(result.stdout.strip())
                duration_sec = duration_ms / 1000
                logger.info(f"Got actual duration from MediaInfo: {duration_sec:.1f} seconds ({duration_sec/60:.1f} minutes)")
                return duration_sec
                
        except Exception as e:
            logger.debug(f"Could not get duration with MediaInfo: {e}")
        
        return None

    def calculate_target_bitrate(self, movie: MovieInfo) -> int:
        """
        Calculate target bitrate based on source file and resolution
        
        Args:
            movie: MovieInfo object containing file information
            
        Returns:
            Target bitrate in kbps
        """
        try:
            # Get file size if not available
            if movie.file_size_bytes == 0 and movie.main_movie_file and movie.main_movie_file.exists():
                movie.file_size_bytes = movie.main_movie_file.stat().st_size
            
            if movie.file_size_bytes == 0 or movie.duration_seconds == 0:
                logger.warning(f"Missing file size or duration for {movie.title}, using defaults")
                return self.min_bitrate_4k if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower() else self.min_bitrate_1080p
            
            # Calculate original average bitrate (in kbps)
            original_bitrate_kbps = (movie.file_size_bytes * 8) / movie.duration_seconds / 1000
            
            logger.info(f"Original bitrate for {movie.title}: {original_bitrate_kbps:.1f} kbps ({original_bitrate_kbps/1000:.1f} Mbps)")
            
            # Determine target based on resolution
            if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower():
                # 4K: Default to 20 Mbps
                target_kbps = self.default_4k_bitrate
                logger.info(f"4K content: using default {target_kbps/1000} Mbps")
            else:
                # 1080p or lower: Use compression ratio with minimum floor
                if original_bitrate_kbps < self.min_bitrate_1080p:
                    target_kbps = self.min_bitrate_1080p
                    logger.info(f"Original below minimum, using floor: {target_kbps/1000} Mbps")
                else:
                    # Apply compression ratio but respect minimum
                    compressed_bitrate = int(original_bitrate_kbps * self.compression_ratio)
                    target_kbps = max(compressed_bitrate, self.min_bitrate_1080p)
                    logger.info(f"Compressed to {self.compression_ratio*100}% of original: {target_kbps/1000:.1f} Mbps")
            
            return target_kbps
            
        except Exception as e:
            logger.error(f"Error calculating bitrate for {movie.title}: {e}")
            # Return safe defaults
            return self.min_bitrate_4k if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower() else self.min_bitrate_1080p

    def get_encoding_parameters(self, movie: MovieInfo) -> EncodingParameters:
        """
        Get encoding parameters based on movie resolution and characteristics
        
        Args:
            movie: MovieInfo object
            
        Returns:
            EncodingParameters object with appropriate settings
        """
        params = EncodingParameters()
        
        # Determine resolution and preset
        if "4k" in movie.resolution.lower() or "2160p" in movie.resolution.lower():
            params.resolution = "4K"
            params.handbrake_preset = "H.265 MKV 2160p60 4K"
        else:
            params.resolution = "1080p"
            params.handbrake_preset = "H.265 MKV 1080p30"
        
        # Calculate target bitrate
        params.target_bitrate_kbps = self.calculate_target_bitrate(movie)
        
        # Check for user preferences in metadata database
        metadata = self.metadata_db.get_movie_metadata(movie.unique_id)
        if metadata and metadata.get('metadata', {}):
            user_metadata = metadata['metadata']
            # Apply any user-specified encoding preferences
            if 'encoding_preset' in user_metadata:
                params.preset = user_metadata['encoding_preset']
            if 'target_bitrate_kbps' in user_metadata:
                params.target_bitrate_kbps = user_metadata['target_bitrate_kbps']
        
        return params

    def prompt_user_confirmation(self, movie: MovieInfo, params: EncodingParameters) -> Tuple[bool, EncodingParameters]:
        """
        Prompt user for confirmation and allow parameter overrides
        
        Args:
            movie: MovieInfo object
            params: Initial encoding parameters
            
        Returns:
            Tuple of (should_proceed, updated_parameters)
        """
        print(f"\n{'='*60}")
        print(f"Ready to encode: {movie.title} ({movie.year}) [{params.resolution}]")
        print(f"{'='*60}")
        if movie.main_movie_file:
            print(f"Input file: {movie.main_movie_file.name}")
        print(f"File size: {movie.file_size_bytes / (1024**3):.1f} GB")
        print(f"Duration: {movie.duration_seconds / 3600:.1f} hours")
        print(f"Current stage: {movie.current_stage}")
        print(f"\nProposed encoding settings:")
        print(f"  • Video codec: H.265 (x265), preset: {params.preset}")
        print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
        print(f"  • Encoder profile: {params.encoder_profile}, level: {params.encoder_level}")
        print(f"  • Two-pass encoding: {'Yes' if not params.turbo_first_pass else 'Yes (turbo first pass)'}")
        print(f"  • Audio: pass-through (source audio preserved)")
        print(f"  • HandBrake preset: {params.handbrake_preset}")
        
        while True:
            choice = input(f"\nProceed with these settings? (Y)es/(E)dit/(S)kip/(Q)uit: ").upper().strip()
            
            if choice in ['Y', 'YES', '']:
                return True, params
            elif choice in ['S', 'SKIP']:
                return False, params
            elif choice in ['Q', 'QUIT']:
                sys.exit(0)
            elif choice in ['E', 'EDIT']:
                params = self.prompt_parameter_overrides(params)
                # Show updated settings
                print(f"\nUpdated settings:")
                print(f"  • Preset: {params.preset}")
                print(f"  • Target bitrate: {params.target_bitrate_kbps} kbps ({params.target_bitrate_kbps/1000:.1f} Mbps)")
                print(f"  • Profile/Level: {params.encoder_profile}@{params.encoder_level}")
                print(f"  • Turbo first pass: {'Yes' if params.turbo_first_pass else 'No'}")
                return True, params
            else:
                print("Invalid choice. Please enter Y, E, S, or Q.")

    def prompt_parameter_overrides(self, params: EncodingParameters) -> EncodingParameters:
        """
        Allow user to override encoding parameters
        
        Args:
            params: Current parameters
            
        Returns:
            Updated parameters
        """
        print(f"\nParameter Override Menu:")
        print(f"Current settings shown in [brackets]")
        
        # Turbo first pass
        turbo = input(f"Use turbo first pass? [{'Y' if params.turbo_first_pass else 'N'}]: ").upper().strip()
        if turbo in ['N', 'NO']:
            params.turbo_first_pass = False
        elif turbo in ['Y', 'YES']:
            params.turbo_first_pass = True
        
        # Target bitrate
        bitrate_input = input(f"Target bitrate in kbps [{params.target_bitrate_kbps}]: ").strip()
        if bitrate_input:
            try:
                new_bitrate = int(bitrate_input)
                if new_bitrate > 0:
                    params.target_bitrate_kbps = new_bitrate
                else:
                    print("Invalid bitrate, keeping current value")
            except ValueError:
                print("Invalid bitrate format, keeping current value")
        
        # Encoder preset
        preset_input = input(f"Encoder speed preset [{params.preset}] (ultrafast/fast/medium/slow/slower/veryslow): ").strip().lower()
        valid_presets = ['ultrafast', 'fast', 'medium', 'slow', 'slower', 'veryslow']
        if preset_input in valid_presets:
            params.preset = preset_input
        elif preset_input:
            print(f"Invalid preset '{preset_input}', keeping current value")
        
        # Encoder level
        level_input = input(f"Encoder level [{params.encoder_level}]: ").strip()
        if level_input:
            params.encoder_level = level_input
        
        # Encoder profile
        profile_input = input(f"Encoder profile [{params.encoder_profile}] (main/main10): ").strip().lower()
        if profile_input in ['main', 'main10']:
            params.encoder_profile = profile_input
        elif profile_input:
            print(f"Invalid profile '{profile_input}', keeping current value")
        
        return params

    def build_handbrake_command(self, movie: MovieInfo, params: EncodingParameters, output_path: Path) -> List[str]:
        """
        Build HandBrakeCLI command with specified parameters
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters
            output_path: Output file path
            
        Returns:
            Command as list of strings
        """
        if not movie.main_movie_file:
            raise ValueError(f"No input file found for {movie.title}")
        
        cmd = [
            str(self.handbrake_path),
            "-i", str(movie.main_movie_file),
            "-o", str(output_path),
            
            # Use preset as base
            "-Z", params.handbrake_preset,
            
            # Override video settings
            "-e", "x265",
            "--encoder-preset", params.preset,
            "--encoder-profile", params.encoder_profile,
            "--encoder-level", params.encoder_level,
            
            # Set target bitrate with multi-pass encoding
            "-b", str(params.target_bitrate_kbps),
            "--multi-pass",
        ]
        
        # Add turbo first pass if enabled
        if params.turbo_first_pass:
            cmd.extend(["-T"])
        
        # Audio handling - preserve original audio
        cmd.extend([
            "--audio", "1",  # Use first audio track
            "-E", "copy",    # Copy audio without re-encoding
            "--audio-copy-mask", "ac3,eac3,truehd,dts,dtshd",
            "--audio-fallback", "ac3"  # Fallback to AC3 if copy fails
        ])
        
        # Additional quality settings
        cmd.extend([
            "--verbose", "1"  # Some output for progress monitoring
        ])
        
        return cmd

    def get_output_path(self, movie: MovieInfo, params: EncodingParameters) -> Path:
        """
        Determine output file path in the ready for mux directory
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters
            
        Returns:
            Output file path
        """
        # Use the ready_for_mux directory from filesystem manager
        base_dir = self.fs_manager.stage_directories['ready_for_mux']
        resolution_dir = base_dir / params.resolution.lower()
        movie_dir = resolution_dir / f"{movie.title} ({movie.year})"
        
        # Create directories if they don't exist
        movie_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate output filename
        safe_title = "".join(c for c in movie.title if c.isalnum() or c in (' ', '-', '_')).strip()
        output_filename = f"{safe_title} ({movie.year}).mkv"
        
        return movie_dir / output_filename

    def run_handbrake_encoding(self, cmd: List[str], movie: MovieInfo) -> Tuple[bool, str]:
        """
        Execute HandBrakeCLI encoding command
        
        Args:
            cmd: Command to execute
            movie: MovieInfo object for logging
            
        Returns:
            Tuple of (success, error_message)
        """
        try:
            logger.info(f"Starting encoding for {movie.title}")
            
            # Show the command for debugging
            cmd_str = ' '.join(f'"{arg}"' if ' ' in arg else arg for arg in cmd)
            logger.info(f"HandBrake command: {cmd_str}")
            print(f"Running HandBrake command:")
            print(f"  {cmd_str}")
            
            start_time = time.time()
            
            # Run HandBrakeCLI
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                errors='ignore',
                bufsize=1
            )
            
            output_lines = []
            
            # Monitor output for progress and errors
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    output_lines.append(output.strip())
                    # Show encoding progress (HandBrake outputs percentage)
                    if "%" in output and "fps" in output:
                        print(f"\r{output.strip()}", end="", flush=True)
            
            print()  # New line after progress output
            
            # Get return code
            return_code = process.poll()
            encoding_time = time.time() - start_time
            
            if return_code == 0:
                logger.info(f"Encoding completed successfully for {movie.title} in {encoding_time/60:.1f} minutes")
                return True, ""
            else:
                error_msg = f"HandBrakeCLI failed with return code {return_code}"
                
                # Get last few lines of output for error context
                error_context = "\n".join(output_lines[-10:]) if output_lines else "No output captured"
                full_error = f"{error_msg}\nLast output:\n{error_context}"
                
                # Show the error output for debugging
                print(f"\nHandBrake Error Output:")
                print(f"Return code: {return_code}")
                if output_lines:
                    print("Last 10 lines of output:")
                    for line in output_lines[-10:]:
                        print(f"  {line}")
                else:
                    print("No output captured")
                
                logger.error(f"Encoding failed for {movie.title}: {full_error}")
                return False, full_error
                
        except Exception as e:
            error_msg = f"Exception during encoding: {str(e)}"
            logger.error(f"Encoding error for {movie.title}: {error_msg}")
            return False, error_msg

    def verify_output_file(self, output_path: Path, expected_duration: float) -> bool:
        """
        Verify that the output file was created and is valid
        
        Args:
            output_path: Path to output file
            expected_duration: Expected duration in seconds
            
        Returns:
            True if file appears valid
        """
        try:
            if not output_path.exists():
                logger.error(f"Output file not created: {output_path}")
                return False
            
            file_size = output_path.stat().st_size
            
            # Check if file is suspiciously small (less than 1MB)
            if file_size < 1024 * 1024:
                logger.error(f"Output file suspiciously small: {file_size} bytes")
                return False
            
            # Calculate expected minimum size based on duration and minimum bitrate
            # Using a very conservative estimate
            min_expected_size = (expected_duration * 1000 * 1000) / 8  # 1 Mbps minimum
            
            if file_size < min_expected_size:
                logger.warning(f"Output file smaller than expected: {file_size} vs {min_expected_size} bytes")
                # Don't fail for this, just warn
            
            logger.info(f"Output file verification passed: {file_size / (1024**2):.1f} MB")
            return True
            
        except Exception as e:
            logger.error(f"Error verifying output file: {e}")
            return False

    def set_encoding_markers(self, movie: MovieInfo, stage: str, data: Optional[Dict[str, Any]] = None):
        """
        Set marker files for encoding stages using filesystem-first state management
        
        Args:
            movie: MovieInfo object
            stage: Stage marker to set ('mkv_processing', 'mkv_complete', 'error')
            data: Optional data to store with marker
        """
        try:
            self.fs_manager.set_stage_marker(movie.movie_directory, stage, data)
            logger.debug(f"Set {stage} marker for {movie.title}")
        except Exception as e:
            logger.error(f"Failed to set {stage} marker for {movie.title}: {e}")

    def clear_encoding_markers(self, movie: MovieInfo, stage: str):
        """
        Clear marker files for encoding stages
        
        Args:
            movie: MovieInfo object
            stage: Stage marker to clear
        """
        try:
            self.fs_manager.clear_stage_marker(movie.movie_directory, stage)
            logger.debug(f"Cleared {stage} marker for {movie.title}")
        except Exception as e:
            logger.error(f"Failed to clear {stage} marker for {movie.title}: {e}")

    def set_encoding_markers_in_dir(self, target_dir: Path, stage: str, data: dict = None):
        """
        Set marker files in a specific directory (for output directories)
        
        Args:
            target_dir: Directory to place marker in
            stage: Stage marker to set
            data: Optional data to store with marker
        """
        try:
            self.fs_manager.set_stage_marker(target_dir, stage, data)
            logger.debug(f"Set {stage} marker in {target_dir}")
        except Exception as e:
            logger.error(f"Failed to set {stage} marker in {target_dir}: {e}")

    def save_encoding_metadata(self, movie: MovieInfo, params: EncodingParameters, output_path: Path):
        """
        Save encoding metadata to the metadata-only database
        
        Args:
            movie: MovieInfo object
            params: Encoding parameters used
            output_path: Path to encoded output file
        """
        try:
            # Prepare metadata
            encoding_metadata = {
                'encoding_preset': params.preset,
                'target_bitrate_kbps': params.target_bitrate_kbps,
                'encoder_profile': params.encoder_profile,
                'encoder_level': params.encoder_level,
                'turbo_first_pass': params.turbo_first_pass,
                'handbrake_preset': params.handbrake_preset,
                'encoded_file_path': str(output_path),
                'encoding_timestamp': datetime.now().isoformat(),
                'resolution': movie.resolution
            }
            
            # Get existing metadata if any
            existing_metadata = self.metadata_db.get_movie_metadata(movie.unique_id)
            if existing_metadata:
                # Update existing metadata
                current_metadata = existing_metadata.get('metadata', {})
                current_metadata.update(encoding_metadata)
                
                self.metadata_db.save_movie_metadata(
                    unique_id=movie.unique_id,
                    title=movie.title,
                    year=int(movie.year) if movie.year.isdigit() else None,
                    metadata=current_metadata
                )
            else:
                # Create new metadata entry
                self.metadata_db.save_movie_metadata(
                    unique_id=movie.unique_id,
                    title=movie.title,
                    year=int(movie.year) if movie.year.isdigit() else None,
                    metadata=encoding_metadata
                )
            
            logger.info(f"Saved encoding metadata for {movie.title}")
            
        except Exception as e:
            logger.error(f"Failed to save encoding metadata for {movie.title}: {e}")

    def process_movie(self, movie: MovieInfo) -> bool:
        """
        Process a single movie through the encoding pipeline using filesystem-first approach
        
        Args:
            movie: MovieInfo object to process
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Processing movie: {movie.title} ({movie.year})")
            
            # FILESYSTEM-FIRST: Don't set processing markers in source directory
            # The .mkv_complete marker in folder 3 is sufficient for tracking
            
            # Get encoding parameters
            params = self.get_encoding_parameters(movie)
            
            # Get user confirmation and potential overrides
            should_proceed, params = self.prompt_user_confirmation(movie, params)
            
            if not should_proceed:
                logger.info(f"Skipping {movie.title} at user request")
                # Clear processing marker since we're not proceeding
                self.clear_encoding_markers(movie, 'mkv_processing')
                return False
            
            # Determine output path
            output_path = self.get_output_path(movie, params)
            logger.info(f"Output path: {output_path}")
            
            # Build HandBrake command
            cmd = self.build_handbrake_command(movie, params, output_path)
            
            # Run encoding
            success, error_msg = self.run_handbrake_encoding(cmd, movie)
            
            if success:
                # Verify output file
                if self.verify_output_file(output_path, movie.duration_seconds):
                    # FILESYSTEM-FIRST: Set completion marker in OUTPUT directory
                    output_movie_dir = output_path.parent  # Destination directory
                    self.set_encoding_markers_in_dir(output_movie_dir, 'encoding_complete', {
                        'completion_time': datetime.now().isoformat(),
                        'output_path': str(output_path),
                        'encoding_parameters': {
                            'preset': params.preset,
                            'bitrate_kbps': params.target_bitrate_kbps,
                            'profile': params.encoder_profile,
                            'level': params.encoder_level
                        }
                    })
                    
                    # Clear old markers from SOURCE directory
                    self.clear_encoding_markers(movie, 'mkv_processing')
                    self.clear_encoding_markers(movie, 'mkv_complete')  # Clear any old markers
                    
                    # Save metadata
                    self.save_encoding_metadata(movie, params, output_path)
                    
                    logger.info(f"Successfully processed {movie.title}")
                    print(f"✓ Encoding complete for {movie.title}")
                    print(f"  Output: {output_path}")
                    return True
                else:
                    # Verification failed - set error marker
                    error_msg = "Output file verification failed"
                    self.set_encoding_markers(movie, 'error', {
                        'error_time': datetime.now().isoformat(),
                        'error_message': error_msg,
                        'stage': 'video_encoding_verification'
                    })
                    self.clear_encoding_markers(movie, 'mkv_processing')
                    logger.error(f"Verification failed for {movie.title}")
                    return False
            else:
                # Encoding failed - set error marker
                self.set_encoding_markers(movie, 'error', {
                    'error_time': datetime.now().isoformat(),
                    'error_message': error_msg,
                    'stage': 'video_encoding_handbrake'
                })
                self.clear_encoding_markers(movie, 'mkv_processing')
                logger.error(f"Encoding failed for {movie.title}: {error_msg}")
                
                # Ask user if they want to retry with different settings
                if self.prompt_yes_no("Encoding failed. Try again with different settings?", default=False):
                    # Clear error marker to allow retry
                    self.clear_encoding_markers(movie, 'error')
                
                return False
                
        except Exception as e:
            error_msg = f"Unexpected error processing {movie.title}: {str(e)}"
            logger.error(error_msg)
            
            # Set error marker
            self.set_encoding_markers(movie, 'error', {
                'error_time': datetime.now().isoformat(),
                'error_message': error_msg,
                'stage': 'video_encoding_exception'
            })
            self.clear_encoding_markers(movie, 'mkv_processing')
            return False

    def prompt_yes_no(self, question: str, default: bool = True) -> bool:
        """
        Prompt user for yes/no response
        
        Args:
            question: Question to ask
            default: Default response if user just presses Enter
            
        Returns:
            True for yes, False for no
        """
        default_text = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_text}]: ").upper().strip()
        
        if response == "":
            return default
        elif response in ["Y", "YES"]:
            return True
        elif response in ["N", "NO"]:
            return False
        else:
            print("Please answer yes or no.")
            return self.prompt_yes_no(question, default)

    def run_batch_processing(self, auto_confirm: bool = False):
        """
        Run batch processing of all movies ready for encoding using filesystem-first discovery
        
        Args:
            auto_confirm: If True, skip user confirmation prompts
        """
        logger.info("Starting filesystem-first video encoding batch processing")
        
        # Get movies to process using filesystem discovery
        movies = self.discover_movies_for_encoding()
        
        if not movies:
            print("No movies found ready for encoding.")
            logger.info("No movies ready for encoding")
            return
        
        print(f"\nFound {len(movies)} movies ready for encoding:")
        for i, movie in enumerate(movies, 1):
            print(f"  {i}. {movie.title} ({movie.year}) - {movie.resolution} [{movie.current_stage}]")
        
        if not auto_confirm:
            if not self.prompt_yes_no(f"\nProcess all {len(movies)} movies?", default=True):
                print("Batch processing cancelled.")
                return
        
        # Process each movie
        successful = 0
        failed = 0
        skipped = 0
        
        for i, movie in enumerate(movies, 1):
            print(f"\n{'='*60}")
            print(f"Processing movie {i}/{len(movies)}")
            print(f"{'='*60}")
            
            try:
                if auto_confirm:
                    # Skip user confirmation, use default parameters
                    params = self.get_encoding_parameters(movie)
                    output_path = self.get_output_path(movie, params)
                    
                    # Set processing marker
                    self.set_encoding_markers(movie, 'mkv_processing', {
                        'start_time': datetime.now().isoformat(),
                        'auto_confirm': True
                    })
                    
                    # Build and run command
                    cmd = self.build_handbrake_command(movie, params, output_path)
                    success, error_msg = self.run_handbrake_encoding(cmd, movie)
                    
                    if success and self.verify_output_file(output_path, movie.duration_seconds):
                        # Set completion marker and save metadata
                        self.set_encoding_markers(movie, 'mkv_complete', {
                            'completion_time': datetime.now().isoformat(),
                            'output_path': str(output_path)
                        })
                        self.clear_encoding_markers(movie, 'mkv_processing')
                        self.save_encoding_metadata(movie, params, output_path)
                        successful += 1
                        logger.info(f"Auto-processed {movie.title} successfully")
                    else:
                        # Set error marker
                        self.set_encoding_markers(movie, 'error', {
                            'error_time': datetime.now().isoformat(),
                            'error_message': error_msg or "Verification failed",
                            'auto_confirm': True
                        })
                        self.clear_encoding_markers(movie, 'mkv_processing')
                        failed += 1
                        logger.error(f"Auto-processing failed for {movie.title}")
                else:
                    # Interactive processing
                    result = self.process_movie(movie)
                    if result:
                        successful += 1
                    else:
                        # Check if it was skipped (no error marker) or failed (has error marker)
                        if (movie.movie_directory / '.error').exists():
                            failed += 1
                        else:
                            skipped += 1
                        
            except KeyboardInterrupt:
                print("\nBatch processing interrupted by user.")
                logger.info("Batch processing interrupted")
                break
            except Exception as e:
                logger.error(f"Unexpected error processing {movie.title}: {e}")
                # Set error marker for unexpected errors
                self.set_encoding_markers(movie, 'error', {
                    'error_time': datetime.now().isoformat(),
                    'error_message': f"Unexpected error: {str(e)}",
                    'stage': 'batch_processing'
                })
                failed += 1
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"Batch Processing Complete")
        print(f"{'='*60}")
        print(f"Successful: {successful}")
        print(f"Failed: {failed}")
        if not auto_confirm:
            print(f"Skipped: {skipped}")
        print(f"Total: {len(movies)}")
        
        logger.info(f"Batch processing complete: {successful} successful, {failed} failed, {skipped} skipped")

    def cleanup_stale_markers(self):
        """
        Clean up stale or conflicting marker files across all movies
        """
        logger.info("Cleaning up stale encoding markers...")
        
        all_movies = self.fs_manager.discover_movies_by_stage()
        cleaned_count = 0
        
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    try:
                        self.fs_manager.cleanup_stale_markers(movie_dir)
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to cleanup markers for {movie_dir.name}: {e}")
        
        logger.info(f"Cleaned up markers for {cleaned_count} movies")

    def show_encoding_status(self):
        """
        Show current encoding status of all movies using filesystem-first discovery
        """
        print(f"\n{'='*60}")
        print(f"Video Encoding Status Dashboard")
        print(f"{'='*60}")
        
        all_movies = self.fs_manager.discover_movies_by_stage()
        
        # Count movies by encoding stage
        ready_for_encoding = len(all_movies.get('mkv_processing_complete', []))
        currently_encoding = 0
        completed_encoding = 0
        failed_encoding = 0
        
        # Count interrupted/error states
        for stage_name, movies in all_movies.items():
            for movie_data in movies:
                movie_dir = Path(movie_data.get('movie_directory', ''))
                if movie_dir.exists():
                    if (movie_dir / '.mkv_processing').exists() and not (movie_dir / '.mkv_complete').exists():
                        currently_encoding += 1
                    elif (movie_dir / '.mkv_complete').exists():
                        completed_encoding += 1
                    elif (movie_dir / '.error').exists():
                        # Check if error is encoding related
                        error_data = self.fs_manager.get_stage_marker_data(movie_dir, 'error')
                        if error_data and 'video_encoding' in error_data.get('stage', ''):
                            failed_encoding += 1
        
        print(f"Ready for encoding: {ready_for_encoding}")
        print(f"Currently encoding: {currently_encoding}")
        print(f"Completed encoding: {completed_encoding}")
        print(f"Failed encoding: {failed_encoding}")
        
        # Show details for movies needing attention
        if currently_encoding > 0:
            print(f"\n📹 Movies currently encoding:")
            for stage_name, movies in all_movies.items():
                for movie_data in movies:
                    movie_dir = Path(movie_data.get('movie_directory', ''))
                    if movie_dir.exists() and (movie_dir / '.mkv_processing').exists():
                        title = movie_data.get('cleaned_title', 'Unknown')
                        year = movie_data.get('year', 'Unknown')
                        print(f"   • {title} ({year})")
        
        if failed_encoding > 0:
            print(f"\n❌ Movies with encoding errors:")
            for stage_name, movies in all_movies.items():
                for movie_data in movies:
                    movie_dir = Path(movie_data.get('movie_directory', ''))
                    if movie_dir.exists() and (movie_dir / '.error').exists():
                        error_data = self.fs_manager.get_stage_marker_data(movie_dir, 'error')
                        if error_data and 'video_encoding' in error_data.get('stage', ''):
                            title = movie_data.get('cleaned_title', 'Unknown')
                            year = movie_data.get('year', 'Unknown')
                            error_msg = error_data.get('error_message', 'Unknown error')
                            print(f"   • {title} ({year}): {error_msg}")


def main():
    """Main entry point for the filesystem-first video encoder script"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Filesystem-First HandBrake Video Re-encoding Script")
    parser.add_argument("--handbrake-path", help="Path to HandBrakeCLI.exe")
    parser.add_argument("--workspace", help="Workspace base directory", default=".")
    parser.add_argument("--auto-confirm", action="store_true", help="Auto-confirm all encoding with default parameters")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without actually encoding")
    parser.add_argument("--status", action="store_true", help="Show encoding status dashboard")
    parser.add_argument("--cleanup", action="store_true", help="Clean up stale marker files")
    
    args = parser.parse_args()
    
    try:
        # Initialize filesystem-first encoder
        encoder = FilesystemFirstVideoEncoder(
            handbrake_path=args.handbrake_path,
            workspace_path=args.workspace
        )
        
        if args.cleanup:
            print("Cleaning up stale marker files...")
            encoder.cleanup_stale_markers()
            print("Cleanup complete.")
            return
        
        if args.status:
            encoder.show_encoding_status()
            return
        
        if args.dry_run:
            print("DRY RUN MODE - No actual encoding will be performed")
            movies = encoder.discover_movies_for_encoding()
            if movies:
                print(f"\nWould process {len(movies)} movies:")
                for movie in movies:
                    params = encoder.get_encoding_parameters(movie)
                    output_path = encoder.get_output_path(movie, params)
                    print(f"  • {movie.title} ({movie.year}) - {params.resolution}")
                    print(f"    Target: {params.target_bitrate_kbps/1000:.1f} Mbps")
                    print(f"    Output: {output_path}")
                    print(f"    Current stage: {movie.current_stage}")
            else:
                print("No movies found ready for encoding.")
        else:
            # Run batch processing with filesystem-first architecture
            encoder.run_batch_processing(auto_confirm=args.auto_confirm)
        
    except FileNotFoundError as e:
        print(f"Error: {e}")
        print("Please ensure HandBrakeCLI.exe is available in the tools directory or specify --handbrake-path")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"An unexpected error occurred: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

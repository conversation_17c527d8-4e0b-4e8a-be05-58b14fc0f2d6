{"encoding_settings": {"min_bitrate_1080p_kbps": 9000, "min_bitrate_4k_kbps": 20000, "default_4k_bitrate_kbps": 20000, "compression_ratio": 0.5, "default_preset": "slow", "default_profile": "main", "default_level": "5.1", "turbo_first_pass": true}, "handbrake_presets": {"1080p": "H.265 MKV 1080p30", "4k": "H.265 MKV 2160p60 4K"}, "audio_settings": {"copy_mask": "ac3,eac3,truehd,dts,dtshd", "fallback_codec": "ac3"}, "file_paths": {"handbrake_cli": "_internal/tools/HandBrakeCLI.exe", "output_base": "workspace/4_ready_for_final_mux", "database": "_internal/data/pipeline_state.db", "log_file": "_internal/logs/video_encoder.log"}, "status_mapping": {"input_status": "video_encoding_pending", "active_status": "video_encoding_active", "success_status": "final_mux_pending", "error_status": "error_video_encoding"}}
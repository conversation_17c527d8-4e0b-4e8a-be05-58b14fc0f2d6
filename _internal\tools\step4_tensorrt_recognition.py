#!/usr/bin/env python3
"""
Step 4: Text Recognition with TensorRT Optimization
Reading Subtitle Text using NGC OCRNet with maximum performance
"""

import os
import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict
from dataclasses import dataclass
from pathlib import Path
import logging

@dataclass
class RecognizedText:
    """Represents recognized text from a subtitle region"""
    text: str
    confidence: float
    bbox: Tuple[int, int, int, int]  # x, y, width, height
    timestamp: float

class TensorRT_ModelConverter:
    """
    Convert ONNX models to optimized TensorRT engines for RTX 5090
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Set up TensorRT environment
        tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
        current_path = os.environ.get('PATH', '')
        if tensorrt_lib_path not in current_path:
            os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"
        
        try:
            import tensorrt as trt
            self.trt = trt
            self.logger.info("TensorRT imported successfully")
        except ImportError as e:
            self.logger.error(f"TensorRT import failed: {e}")
            raise
    
    def convert_ocdnet_to_tensorrt(self, onnx_path: str, engine_path: str, 
                                  max_batch_size: int = 8) -> bool:
        """
        Convert OCDNet ONNX model to TensorRT engine for text detection
        
        Args:
            onnx_path: Path to ONNX model
            engine_path: Output path for TensorRT engine
            max_batch_size: Maximum batch size for inference
            
        Returns:
            True if conversion successful
        """
        try:
            # Create TensorRT logger and builder
            logger = self.trt.Logger(self.trt.Logger.WARNING)
            builder = self.trt.Builder(logger)
            
            # Create network
            network = builder.create_network(1 << int(self.trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            parser = self.trt.OnnxParser(network, logger)
            
            # Parse ONNX model
            with open(onnx_path, 'rb') as model_file:
                if not parser.parse(model_file.read()):
                    self.logger.error("Failed to parse ONNX model")
                    return False
            
            # Get actual input shape from ONNX model
            input_tensor = network.get_input(0)
            input_shape = input_tensor.shape
            self.logger.info(f"ONNX model input shape: {input_shape}")
            
            # Extract dimensions (assuming format: [batch, channels, height, width])
            if len(input_shape) >= 4:
                _, channels, height, width = input_shape[-4:]
                self.logger.info(f"Model expects: channels={channels}, height={height}, width={width}")
            else:
                # Fallback to common OCDNet dimensions
                channels, height, width = 3, 736, 1280
                self.logger.warning(f"Could not parse input shape, using fallback: {channels}x{height}x{width}")
            
            # Configure builder
            config = builder.create_builder_config()
            
            # Set memory pool size (use RTX 5090's massive VRAM)
            config.set_memory_pool_limit(self.trt.MemoryPoolType.WORKSPACE, 8 << 30)  # 8GB workspace
            
            # Enable FP16 precision for RTX 5090
            if builder.platform_has_fast_fp16:
                config.set_flag(self.trt.BuilderFlag.FP16)
                self.logger.info("Enabled FP16 precision for OCDNet")
            
            # Set optimization profiles for dynamic shapes using actual model dimensions
            profile = builder.create_optimization_profile()
            
            # Use actual model dimensions for the profile
            input_name = network.get_input(0).name
            profile.set_shape(input_name, 
                            (1, channels, height, width),                    # min: single image
                            (max_batch_size//2, channels, height, width),    # opt: half batch
                            (max_batch_size, channels, height, width))       # max: full batch
            
            config.add_optimization_profile(profile)
            
            # Build engine
            self.logger.info(f"Building TensorRT engine for OCDNet (this may take several minutes)...")
            serialized_engine = builder.build_serialized_network(network, config)
            
            if serialized_engine is None:
                self.logger.error("Failed to build TensorRT engine")
                return False
            
            # Save engine
            with open(engine_path, 'wb') as f:
                f.write(serialized_engine)
            
            self.logger.info(f"OCDNet TensorRT engine saved: {engine_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"OCDNet conversion failed: {e}")
            return False
    
    def convert_ocrnet_to_tensorrt(self, onnx_path: str, engine_path: str,
                                  max_batch_size: int = 32) -> bool:
        """
        Convert OCRNet ONNX model to TensorRT engine for text recognition
        
        Args:
            onnx_path: Path to ONNX model
            engine_path: Output path for TensorRT engine
            max_batch_size: Maximum batch size for text patches
            
        Returns:
            True if conversion successful
        """
        try:
            # Create TensorRT logger and builder
            logger = self.trt.Logger(self.trt.Logger.WARNING)
            builder = self.trt.Builder(logger)
            
            # Create network
            network = builder.create_network(1 << int(self.trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
            parser = self.trt.OnnxParser(network, logger)
            
            # Parse ONNX model
            with open(onnx_path, 'rb') as model_file:
                if not parser.parse(model_file.read()):
                    self.logger.error("Failed to parse OCRNet ONNX model")
                    return False
            
            # Get actual input shape from ONNX model
            input_tensor = network.get_input(0)
            input_shape = input_tensor.shape
            self.logger.info(f"OCRNet ONNX model input shape: {input_shape}")
            
            # Extract dimensions (assuming format: [batch, channels, height, width])
            if len(input_shape) >= 4:
                _, channels, height, width = input_shape[-4:]
                self.logger.info(f"OCRNet expects: channels={channels}, height={height}, width={width}")
            else:
                # Fallback to common OCRNet dimensions
                channels, height, width = 1, 64, 200
                self.logger.warning(f"Could not parse OCRNet input shape, using fallback: {channels}x{height}x{width}")
            
            # Configure builder
            config = builder.create_builder_config()
            
            # Set memory pool size
            config.set_memory_pool_limit(self.trt.MemoryPoolType.WORKSPACE, 4 << 30)  # 4GB workspace
            
            # Enable FP16 precision for RTX 5090
            if builder.platform_has_fast_fp16:
                config.set_flag(self.trt.BuilderFlag.FP16)
                self.logger.info("Enabled FP16 precision for OCRNet")
            
            # Set optimization profiles using actual model dimensions
            profile = builder.create_optimization_profile()
            
            # Use actual model dimensions for the profile
            input_name = network.get_input(0).name
            profile.set_shape(input_name,
                            (1, channels, height, width),                    # min: single text patch
                            (max_batch_size//2, channels, height, width),    # opt: half batch
                            (max_batch_size, channels, height, width))       # max: full batch
            
            config.add_optimization_profile(profile)
            
            # Build engine
            self.logger.info(f"Building TensorRT engine for OCRNet (this may take several minutes)...")
            serialized_engine = builder.build_serialized_network(network, config)
            
            if serialized_engine is None:
                self.logger.error("Failed to build OCRNet TensorRT engine")
                return False
            
            # Save engine
            with open(engine_path, 'wb') as f:
                f.write(serialized_engine)
            
            self.logger.info(f"OCRNet TensorRT engine saved: {engine_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"OCRNet conversion failed: {e}")
            return False

class TensorRT_SubtitleRecognizer:
    """
    High-performance subtitle text recognition using TensorRT optimized models
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Model paths
        self.models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
        self.tensorrt_dir = self.models_dir / "tensorrt"
        self.tensorrt_dir.mkdir(exist_ok=True)
        
        # TensorRT engines
        self.ocdnet_engine_path = self.tensorrt_dir / "ocdnet_optimized.trt"
        self.ocrnet_engine_path = self.tensorrt_dir / "ocrnet_optimized.trt"
        
        # Model parameters
        self.ocrnet_input_size = (64, 200)  # height=64, max_width=200 for FAN model
        self.max_text_length = 25  # NGC OCRNet training constraint
        self.batch_size = 16  # Optimal batch size for recognition
        
        # Character mapping for OCRNet output
        self.char_map = self._create_character_mapping()
        
        # TensorRT runtime components
        self.trt_logger = None
        self.trt_runtime = None
        self.ocdnet_context = None
        self.ocrnet_context = None
        
        self._initialize_tensorrt()
    
    def _initialize_tensorrt(self):
        """Initialize TensorRT runtime and load engines"""
        try:
            # Set up TensorRT environment
            tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
            current_path = os.environ.get('PATH', '')
            if tensorrt_lib_path not in current_path:
                os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"
            
            import tensorrt as trt
            
            # Create logger and runtime
            self.trt_logger = trt.Logger(trt.Logger.WARNING)
            self.trt_runtime = trt.Runtime(self.trt_logger)
            
            # Load or create TensorRT engines
            self._ensure_tensorrt_engines()
            
            self.logger.info("TensorRT subtitle recognizer initialized")
            
        except Exception as e:
            self.logger.error(f"TensorRT initialization failed: {e}")
            raise
    
    def _ensure_tensorrt_engines(self):
        """Ensure TensorRT engines exist, convert from ONNX if needed"""
        converter = TensorRT_ModelConverter()
        
        # Convert OCDNet if needed
        if not self.ocdnet_engine_path.exists():
            onnx_path = self.models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
            if onnx_path.exists():
                self.logger.info("Converting OCDNet to TensorRT...")
                converter.convert_ocdnet_to_tensorrt(str(onnx_path), str(self.ocdnet_engine_path))
            else:
                raise FileNotFoundError(f"OCDNet ONNX model not found: {onnx_path}")
        
        # Convert OCRNet if needed
        if not self.ocrnet_engine_path.exists():
            onnx_path = self.models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
            if onnx_path.exists():
                self.logger.info("Converting OCRNet to TensorRT...")
                converter.convert_ocrnet_to_tensorrt(str(onnx_path), str(self.ocrnet_engine_path))
            else:
                raise FileNotFoundError(f"OCRNet ONNX model not found: {onnx_path}")
        
        # Load engines
        self._load_tensorrt_engines()
    
    def _load_tensorrt_engines(self):
        """Load TensorRT engines into contexts"""
        try:
            # Load OCDNet engine
            with open(self.ocdnet_engine_path, 'rb') as f:
                ocdnet_engine_data = f.read()
            ocdnet_engine = self.trt_runtime.deserialize_cuda_engine(ocdnet_engine_data)
            self.ocdnet_context = ocdnet_engine.create_execution_context()
            
            # Load OCRNet engine
            with open(self.ocrnet_engine_path, 'rb') as f:
                ocrnet_engine_data = f.read()
            ocrnet_engine = self.trt_runtime.deserialize_cuda_engine(ocrnet_engine_data)
            self.ocrnet_context = ocrnet_engine.create_execution_context()
            
            self.logger.info("TensorRT engines loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to load TensorRT engines: {e}")
            raise
    
    def _create_character_mapping(self) -> Dict[int, str]:
        """Create character mapping for OCRNet output decoding"""
        # NGC OCRNet character set (alphanumeric + limited punctuation)
        chars = list("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz")
        
        # Add common punctuation that might be in training data
        punctuation = list(" .,!?'-\"")
        chars.extend(punctuation)
        
        # Create mapping
        char_map = {i: char for i, char in enumerate(chars)}
        char_map[len(chars)] = ''  # CTC blank token
        
        return char_map
    
    def preprocess_text_crops(self, crops: List[np.ndarray]) -> np.ndarray:
        """
        Preprocess text region crops for OCRNet recognition
        
        Args:
            crops: List of cropped text regions (BGR images)
            
        Returns:
            Batch tensor ready for OCRNet inference [batch, 1, 64, 200]
        """
        batch_data = []
        target_height, target_width = self.ocrnet_input_size
        
        for crop in crops:
            # Convert to grayscale
            if len(crop.shape) == 3:
                gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
            else:
                gray = crop
            
            # Resize maintaining aspect ratio
            h, w = gray.shape
            
            # Calculate scaling to fit target height
            scale = target_height / h
            new_width = int(w * scale)
            
            # Resize
            if new_width <= target_width:
                # Image fits, resize and pad
                resized = cv2.resize(gray, (new_width, target_height))
                
                # Pad to target width
                padded = np.zeros((target_height, target_width), dtype=np.uint8)
                padded[:, :new_width] = resized
                
            else:
                # Image too wide, resize to target width (may distort aspect ratio)
                padded = cv2.resize(gray, (target_width, target_height))
            
            # Normalize to [0, 1]
            normalized = padded.astype(np.float32) / 255.0
            
            # Add channel dimension
            normalized = np.expand_dims(normalized, axis=0)  # [1, H, W]
            
            batch_data.append(normalized)
        
        # Stack into batch
        if batch_data:
            batch_tensor = np.stack(batch_data, axis=0)  # [batch, 1, H, W]
            return batch_tensor
        else:
            return np.empty((0, 1, target_height, target_width), dtype=np.float32)
    
    def recognize_text_batch(self, text_crops: List[np.ndarray]) -> List[str]:
        """
        Recognize text from multiple crops using batched TensorRT inference
        
        Args:
            text_crops: List of cropped text regions
            
        Returns:
            List of recognized text strings
        """
        if not text_crops:
            return []
        
        try:
            # Preprocess crops
            batch_input = self.preprocess_text_crops(text_crops)
            
            if batch_input.shape[0] == 0:
                return []
            
            # Run TensorRT inference
            results = self._run_ocrnet_inference(batch_input)
            
            # Decode results
            recognized_texts = []
            for result in results:
                text = self._decode_ocrnet_output(result)
                recognized_texts.append(text)
            
            return recognized_texts
            
        except Exception as e:
            self.logger.error(f"Text recognition failed: {e}")
            return [""] * len(text_crops)
    
    def _run_ocrnet_inference(self, batch_input: np.ndarray) -> List[np.ndarray]:
        """Run OCRNet inference using TensorRT"""
        try:
            import pycuda.driver as cuda
            import pycuda.autoinit
            
            # Allocate GPU memory
            input_size = batch_input.nbytes
            output_size = batch_input.shape[0] * 26 * len(self.char_map) * 4  # Estimated output size
            
            input_gpu = cuda.mem_alloc(input_size)
            output_gpu = cuda.mem_alloc(output_size)
            
            # Copy input to GPU
            cuda.memcpy_htod(input_gpu, batch_input)
            
            # Set input shape for dynamic batching
            self.ocrnet_context.set_input_shape(0, batch_input.shape)
            
            # Run inference
            self.ocrnet_context.execute_v2([int(input_gpu), int(output_gpu)])
            
            # Copy output back to CPU
            output = np.empty((batch_input.shape[0], 26, len(self.char_map)), dtype=np.float32)
            cuda.memcpy_dtoh(output, output_gpu)
            
            # Split batch results
            results = [output[i] for i in range(batch_input.shape[0])]
            return results
            
        except Exception as e:
            self.logger.error(f"TensorRT inference failed: {e}")
            return [np.array([])] * batch_input.shape[0]
    
    def _decode_ocrnet_output(self, output: np.ndarray) -> str:
        """
        Decode OCRNet CTC output to text string
        
        Args:
            output: Model output [sequence_length, num_classes]
            
        Returns:
            Decoded text string
        """
        try:
            # Get most likely character at each time step
            predicted_chars = np.argmax(output, axis=1)
            
            # CTC decoding: remove consecutive duplicates and blank tokens
            decoded_chars = []
            prev_char = -1
            
            for char_idx in predicted_chars:
                if char_idx != prev_char and char_idx in self.char_map:
                    char = self.char_map[char_idx]
                    if char != '':  # Skip blank tokens
                        decoded_chars.append(char)
                prev_char = char_idx
            
            return ''.join(decoded_chars)
            
        except Exception as e:
            self.logger.error(f"Text decoding failed: {e}")
            return ""

def convert_models_to_tensorrt():
    """
    Convenience function to convert NGC ONNX models to TensorRT engines
    """
    print("🔄 Converting NGC ONNX models to TensorRT engines...")
    print("This will optimize models for your RTX 5090")
    print()
    
    try:
        recognizer = TensorRT_SubtitleRecognizer()
        print("✅ TensorRT engines created and ready!")
        print(f"   OCDNet: {recognizer.ocdnet_engine_path}")
        print(f"   OCRNet: {recognizer.ocrnet_engine_path}")
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")

if __name__ == "__main__":
    print("Step 4: TensorRT Text Recognition")
    print("Converting ONNX models to optimized TensorRT engines...")
    convert_models_to_tensorrt()

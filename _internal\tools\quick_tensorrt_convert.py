#!/usr/bin/env python3
"""
Quick TensorRT conversion for NGC OCR models
Lightweight approach focusing on basic optimization
"""
import os
import subprocess
import sys
from pathlib import Path

def main():
    print("Quick TensorRT Conversion for NGC OCR")
    print("=" * 50)
    
    # Paths
    workspace = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator")
    models_dir = workspace / "_internal" / "models" / "ngc_ocr"
    trt_bin = workspace / "_internal" / "tools" / "TensorRT" / "bin" / "trtexec.exe"
    trt_lib = workspace / "_internal" / "tools" / "TensorRT" / "lib"
    
    # Add TensorRT to PATH
    current_path = os.environ.get('PATH', '')
    if str(trt_lib) not in current_path:
        os.environ['PATH'] = f"{current_path};{trt_lib}"
        print(f"Added TensorRT lib to PATH: {trt_lib}")
    
    # Model paths
    ocdnet_onnx = models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    ocrnet_onnx = models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    
    tensorrt_dir = models_dir / "tensorrt"
    tensorrt_dir.mkdir(exist_ok=True)
    
    ocdnet_trt = tensorrt_dir / "ocdnet_v2.4.trt"
    ocrnet_trt = tensorrt_dir / "ocrnet_v2.1.1.trt"
    
    print(f"\nConverting models with reduced optimization for speed...")
    
    # Convert OCDNet (lighter settings)
    print(f"\n1. Converting OCDNet...")
    ocdnet_cmd = [
        str(trt_bin),
        f"--onnx={ocdnet_onnx}",
        f"--saveEngine={ocdnet_trt}",
        "--fp16",
        "--memPoolSize=workspace:2048M",  # Reduced memory
        "--builderOptimizationLevel=3",   # Reduced from 5
        "--skipInference"                 # Skip inference testing to save time
    ]
    
    try:
        result = subprocess.run(ocdnet_cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        if result.returncode == 0:
            print(f"✅ OCDNet conversion successful: {ocdnet_trt.stat().st_size / (1024*1024):.1f} MB")
        else:
            print(f"❌ OCDNet conversion failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ OCDNet conversion timed out - using ONNX fallback")
    except Exception as e:
        print(f"❌ OCDNet conversion error: {e}")
        return False
    
    # Convert OCRNet (lighter settings)
    print(f"\n2. Converting OCRNet...")
    ocrnet_cmd = [
        str(trt_bin),
        f"--onnx={ocrnet_onnx}",
        f"--saveEngine={ocrnet_trt}",
        "--fp16",
        "--memPoolSize=workspace:2048M",  # Reduced memory
        "--builderOptimizationLevel=3",   # Reduced from 5
        "--skipInference"                 # Skip inference testing to save time
    ]
    
    try:
        result = subprocess.run(ocrnet_cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        if result.returncode == 0:
            print(f"✅ OCRNet conversion successful: {ocrnet_trt.stat().st_size / (1024*1024):.1f} MB")
        else:
            print(f"❌ OCRNet conversion failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ OCRNet conversion timed out - using ONNX fallback")
    except Exception as e:
        print(f"❌ OCRNet conversion error: {e}")
        return False
    
    print(f"\n🚀 TensorRT conversion complete!")
    print(f"NGC OCR is ready for maximum performance on RTX 5090")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n⚡ Fallback: ONNX models will be used (still good performance)")
    input("\nPress Enter to continue...")

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/config_manager.py

Configuration management for MCP server integrations.
Handles MCP server connections, API keys, service preferences, and connection pooling.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timezone

@dataclass
class MCPServerConfig:
    """Configuration for a single MCP server."""
    name: str
    server_type: str  # e.g., "sequential_thinking", "memory", "github", etc.
    enabled: bool = True
    connection_url: str = ""
    api_key: str = ""
    api_secret: str = ""
    additional_config: Dict[str, Any] = None
    max_retries: int = 3
    timeout_seconds: int = 30
    rate_limit_per_minute: int = 60
    priority: int = 1  # Lower numbers = higher priority
    
    def __post_init__(self):
        if self.additional_config is None:
            self.additional_config = {}

@dataclass
class MCPGlobalConfig:
    """Global MCP configuration settings."""
    enabled: bool = True
    max_concurrent_connections: int = 10
    default_timeout: int = 30
    retry_backoff_factor: float = 2.0
    health_check_interval: int = 300  # seconds
    log_level: str = "INFO"
    cache_enabled: bool = True
    cache_ttl_seconds: int = 3600
    
class MCPConfigManager:
    """
    Manages MCP server configurations, API keys, and connection settings.
    """
    
    def __init__(self, config_path: str = "config/mcp_config.json", 
                 settings_dict: Dict = None, logger: logging.Logger = None):
        self.config_path = Path(config_path)
        self.settings_dict = settings_dict or {}
        self.logger = logger or logging.getLogger(__name__)
        
        self.global_config = MCPGlobalConfig()
        self.server_configs: Dict[str, MCPServerConfig] = {}
        self.connection_pool = {}
        
        # Default MCP server configurations
        self._initialize_default_configs()
    
    def _initialize_default_configs(self):
        """Initialize default configurations for available MCP servers."""
        default_servers = [
            {
                "name": "sequential_thinking",
                "server_type": "sequential_thinking",
                "enabled": True,
                "priority": 1,
                "additional_config": {
                    "max_steps": 50,
                    "step_timeout": 60,
                    "auto_breakdown": True
                }
            },
            {
                "name": "memory_manager", 
                "server_type": "memory",
                "enabled": True,
                "priority": 2,
                "additional_config": {
                    "max_memories": 1000,
                    "memory_ttl_days": 30,
                    "auto_cleanup": True
                }
            },
            {
                "name": "github_integration",
                "server_type": "github",
                "enabled": False,  # Requires API key
                "priority": 3,
                "additional_config": {
                    "repository": "your-username/plex-movie-automator",
                    "auto_create_issues": True,
                    "issue_labels": ["bug", "automation", "pipeline"]
                }
            },
            {
                "name": "notion_database",
                "server_type": "notion", 
                "enabled": False,  # Requires API key
                "priority": 4,
                "additional_config": {
                    "database_id": "",
                    "auto_sync": True,
                    "sync_interval": 300
                }
            },
            {
                "name": "firecrawl_scraper",
                "server_type": "firecrawl",
                "enabled": False,  # Requires API key
                "priority": 5,
                "additional_config": {
                    "max_pages": 10,
                    "respect_robots": True,
                    "delay_ms": 1000
                }
            },
            {
                "name": "image_processor",
                "server_type": "imagesorcery",
                "enabled": True,
                "priority": 6,
                "additional_config": {
                    "max_image_size": "2048x2048",
                    "quality": 85,
                    "formats": ["jpg", "png", "webp"]
                }
            }
        ]
        
        for server_config in default_servers:
            config = MCPServerConfig(**server_config)
            self.server_configs[config.name] = config
    
    def load_config(self) -> bool:
        """Load MCP configuration from file."""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r') as f:
                    config_data = json.load(f)
                
                # Load global config
                if "global" in config_data:
                    global_data = config_data["global"]
                    self.global_config = MCPGlobalConfig(**global_data)
                
                # Load server configs
                if "servers" in config_data:
                    for server_name, server_data in config_data["servers"].items():
                        self.server_configs[server_name] = MCPServerConfig(**server_data)
                
                self.logger.info(f"Loaded MCP configuration from {self.config_path}")
                return True
            else:
                self.logger.info("No MCP config file found, using defaults")
                self.save_config()  # Create default config file
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to load MCP configuration: {e}")
            return False
    
    def save_config(self) -> bool:
        """Save current MCP configuration to file."""
        try:
            # Ensure config directory exists
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            config_data = {
                "global": asdict(self.global_config),
                "servers": {name: asdict(config) for name, config in self.server_configs.items()},
                "last_updated": datetime.now(timezone.utc).isoformat(),
                "version": "1.0.0"
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.logger.info(f"Saved MCP configuration to {self.config_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save MCP configuration: {e}")
            return False
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """Get configuration for a specific MCP server."""
        return self.server_configs.get(server_name)
    
    def get_enabled_servers(self) -> List[MCPServerConfig]:
        """Get list of enabled MCP servers sorted by priority."""
        enabled_servers = [config for config in self.server_configs.values() if config.enabled]
        return sorted(enabled_servers, key=lambda x: x.priority)
    
    def update_server_config(self, server_name: str, updates: Dict[str, Any]) -> bool:
        """Update configuration for a specific server."""
        if server_name not in self.server_configs:
            self.logger.error(f"Server {server_name} not found in configuration")
            return False
        
        try:
            config = self.server_configs[server_name]
            for key, value in updates.items():
                if hasattr(config, key):
                    setattr(config, key, value)
                else:
                    config.additional_config[key] = value
            
            self.logger.info(f"Updated configuration for server {server_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update server config for {server_name}: {e}")
            return False
    
    def add_server_config(self, config: MCPServerConfig) -> bool:
        """Add a new server configuration."""
        try:
            self.server_configs[config.name] = config
            self.logger.info(f"Added configuration for server {config.name}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to add server config: {e}")
            return False
    
    def remove_server_config(self, server_name: str) -> bool:
        """Remove a server configuration."""
        if server_name in self.server_configs:
            del self.server_configs[server_name]
            self.logger.info(f"Removed configuration for server {server_name}")
            return True
        return False
    
    def validate_config(self) -> List[str]:
        """Validate the current configuration and return any issues."""
        issues = []
        
        # Validate global config
        if self.global_config.max_concurrent_connections <= 0:
            issues.append("max_concurrent_connections must be positive")
        
        if self.global_config.default_timeout <= 0:
            issues.append("default_timeout must be positive")
        
        # Validate server configs
        for name, config in self.server_configs.items():
            if not config.name:
                issues.append(f"Server {name} missing name")
            
            if not config.server_type:
                issues.append(f"Server {name} missing server_type")
            
            if config.enabled and config.server_type in ["github", "notion", "firecrawl"]:
                if not config.api_key:
                    issues.append(f"Server {name} enabled but missing API key")
        
        return issues
    
    def get_api_key_from_env(self, server_name: str, env_var_name: str = None) -> Optional[str]:
        """Get API key from environment variable."""
        if env_var_name is None:
            env_var_name = f"MCP_{server_name.upper()}_API_KEY"
        
        api_key = os.getenv(env_var_name)
        if api_key:
            self.logger.info(f"Loaded API key for {server_name} from environment")
            return api_key
        
        return None
    
    def setup_api_keys_from_env(self):
        """Setup API keys from environment variables for all servers."""
        env_mappings = {
            "github_integration": "GITHUB_TOKEN",
            "notion_database": "NOTION_API_KEY", 
            "firecrawl_scraper": "FIRECRAWL_API_KEY"
        }
        
        for server_name, env_var in env_mappings.items():
            if server_name in self.server_configs:
                api_key = os.getenv(env_var)
                if api_key:
                    self.server_configs[server_name].api_key = api_key
                    self.server_configs[server_name].enabled = True
                    self.logger.info(f"Configured API key for {server_name}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration."""
        enabled_count = len(self.get_enabled_servers())
        total_count = len(self.server_configs)
        
        return {
            "global_enabled": self.global_config.enabled,
            "total_servers": total_count,
            "enabled_servers": enabled_count,
            "disabled_servers": total_count - enabled_count,
            "server_list": [
                {
                    "name": config.name,
                    "type": config.server_type,
                    "enabled": config.enabled,
                    "priority": config.priority
                }
                for config in sorted(self.server_configs.values(), key=lambda x: x.priority)
            ],
            "validation_issues": self.validate_config()
        }

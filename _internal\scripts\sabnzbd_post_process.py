#!/usr/bin/env python3
"""
PlexMovieAutomator/scripts/sabnzbd_post_process.py

SABnzbd Post-Processing Bridge Script
=====================================

This script is called by SABnzbd after each download completes.
It triggers the PlexMovieAutomator pipeline to process the new download.

SABnzbd passes these parameters:
1. %1 = Final folder of the job (complete path)
2. %2 = Original NZB name
3. %3 = Clean version of the job name (no path)
4. %4 = Indexer's report number (if supported)
5. %5 = User-defined category
6. %6 = Group that the NZB was posted in
7. %7 = Status of post processing (0 = OK, 1 = failed verification, 2 = failed unpack, 3 = 1+2)

Usage by SABnzbd:
python "C:\\Users\\<USER>\\Videos\\PlexMovieAutomator\\scripts\\sabnzbd_post_process.py" "%1" "%2" "%3" "%4" "%5" "%6" "%7"
"""

import os
import sys
import subprocess
import logging
import json
from pathlib import Path
from datetime import datetime

# Add the project root to Python path
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

def setup_logging():
    """Setup logging for the post-process script."""
    log_dir = PROJECT_ROOT / "logs"
    log_dir.mkdir(exist_ok=True)
    
    log_file = log_dir / "sabnzbd_post_process.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def trigger_pipeline_stage_02(logger):
    """
    Trigger the download and organize stage of the pipeline.
    This will process any new completed downloads.
    """
    try:
        # Path to the main orchestrator
        orchestrator_path = PROJECT_ROOT / "src" / "main_pipeline_orchestrator.py"
        
        if not orchestrator_path.exists():
            logger.error(f"Orchestrator not found at: {orchestrator_path}")
            return False
        
        # Run only stage 02 (download and organize) to process the new download
        cmd = [
            sys.executable,
            str(orchestrator_path),
            "--run-mode", "once",
            "--stages", "02_download_and_organize"
        ]
        
        logger.info(f"Triggering pipeline stage 02: {' '.join(cmd)}")
        
        # Run the pipeline stage
        result = subprocess.run(
            cmd,
            cwd=str(PROJECT_ROOT),
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )
        
        if result.returncode == 0:
            logger.info("Pipeline stage 02 completed successfully")
            logger.info(f"Pipeline output: {result.stdout}")
            return True
        else:
            logger.error(f"Pipeline stage 02 failed with return code: {result.returncode}")
            logger.error(f"Pipeline error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("Pipeline stage 02 timed out after 5 minutes")
        return False
    except Exception as e:
        logger.error(f"Error triggering pipeline: {e}")
        return False

def log_sabnzbd_parameters(logger, args):
    """Log the parameters passed by SABnzbd for debugging."""
    param_names = [
        "final_folder",
        "original_nzb_name", 
        "clean_job_name",
        "indexer_report_number",
        "category",
        "group",
        "post_process_status"
    ]
    
    logger.info("=== SABnzbd Post-Process Parameters ===")
    for i, (name, value) in enumerate(zip(param_names, args[1:]), 1):
        logger.info(f"  %{i} ({name}): {value}")
    logger.info("=" * 40)

def main():
    """Main post-processing function."""
    logger = setup_logging()
    
    logger.info("=" * 60)
    logger.info("SABnzbd Post-Process Bridge Script Started")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")
    logger.info("=" * 60)
    
    # Log all parameters for debugging
    log_sabnzbd_parameters(logger, sys.argv)
    
    # Check if we have the minimum required parameters
    if len(sys.argv) < 8:
        logger.error(f"Expected 7 parameters from SABnzbd, got {len(sys.argv) - 1}")
        logger.error("Usage: sabnzbd_post_process.py <final_folder> <original_nzb> <clean_name> <report_num> <category> <group> <status>")
        return 1
    
    # Extract key parameters
    final_folder = sys.argv[1]
    original_nzb_name = sys.argv[2]
    clean_job_name = sys.argv[3]
    post_process_status = sys.argv[7]
    
    # Check if SABnzbd post-processing was successful
    if post_process_status != "0":
        logger.warning(f"SABnzbd post-processing failed with status: {post_process_status}")
        logger.warning("Skipping pipeline trigger due to failed post-processing")
        return 1
    
    logger.info(f"Download completed successfully: {clean_job_name}")
    logger.info(f"Final folder: {final_folder}")
    
    # Trigger the pipeline to process the new download
    success = trigger_pipeline_stage_02(logger)
    
    if success:
        logger.info("Post-processing completed successfully")
        logger.info("Pipeline has been triggered to process the new download")
        return 0
    else:
        logger.error("Post-processing failed")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

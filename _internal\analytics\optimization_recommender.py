#!/usr/bin/env python3
"""
PlexMovieAutomator/src/analytics/optimization_recommender.py

Intelligent Optimization Recommendation System
Implements intelligent optimization recommendations based on performance patterns, resource usage, and historical data analysis.
"""

import asyncio
import json
import logging
import math
import statistics
from collections import defaultdict, Counter
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, NamedTuple
from dataclasses import dataclass, field
import numpy as np

@dataclass
class OptimizationRecommendation:
    """Represents an optimization recommendation."""
    id: str
    category: str  # "performance", "resource", "quality", "reliability"
    priority: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    impact_score: float  # 0.0 to 100.0
    effort_score: float  # 0.0 to 100.0 (higher = more effort)
    roi_score: float  # Return on investment (impact/effort)
    affected_stages: List[str]
    affected_metrics: List[str]
    implementation_steps: List[str]
    expected_improvement: Dict[str, float]
    prerequisites: List[str]
    risks: List[str]
    monitoring_metrics: List[str]
    created_time: datetime
    confidence: float  # 0.0 to 1.0
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "category": self.category,
            "priority": self.priority,
            "title": self.title,
            "description": self.description,
            "impact_score": self.impact_score,
            "effort_score": self.effort_score,
            "roi_score": self.roi_score,
            "affected_stages": self.affected_stages,
            "affected_metrics": self.affected_metrics,
            "implementation_steps": self.implementation_steps,
            "expected_improvement": self.expected_improvement,
            "prerequisites": self.prerequisites,
            "risks": self.risks,
            "monitoring_metrics": self.monitoring_metrics,
            "created_time": self.created_time.isoformat(),
            "confidence": self.confidence
        }

@dataclass
class PerformancePattern:
    """Represents a detected performance pattern."""
    pattern_type: str  # "bottleneck", "inefficiency", "resource_waste", "quality_issue"
    frequency: float  # How often this pattern occurs
    severity: float  # Impact severity (0.0 to 1.0)
    affected_components: List[str]
    pattern_data: Dict[str, Any]
    first_detected: datetime
    last_detected: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "pattern_type": self.pattern_type,
            "frequency": self.frequency,
            "severity": self.severity,
            "affected_components": self.affected_components,
            "pattern_data": self.pattern_data,
            "first_detected": self.first_detected.isoformat(),
            "last_detected": self.last_detected.isoformat()
        }

class OptimizationRecommender:
    """
    Intelligent Optimization Recommendation System for Plex Movie Automator.
    Analyzes performance patterns and generates actionable optimization recommendations.
    """
    
    def __init__(self, trend_analyzer, memory_service, notion_service, logger: logging.Logger):
        self.trend_analyzer = trend_analyzer
        self.memory_service = memory_service
        self.notion_service = notion_service
        self.logger = logger
        self.recommendations: Dict[str, OptimizationRecommendation] = {}
        self.patterns: Dict[str, PerformancePattern] = {}
        self.analysis_history: List[Dict[str, Any]] = []
        self.initialized = False
        
        # Recommendation templates
        self.recommendation_templates = {
            "processing_time_optimization": {
                "category": "performance",
                "title": "Processing Time Optimization",
                "base_impact": 70.0,
                "base_effort": 40.0,
                "implementation_steps": [
                    "Analyze current processing bottlenecks",
                    "Implement parallel processing where possible",
                    "Optimize resource allocation",
                    "Monitor performance improvements"
                ]
            },
            "resource_utilization": {
                "category": "resource",
                "title": "Resource Utilization Optimization",
                "base_impact": 60.0,
                "base_effort": 30.0,
                "implementation_steps": [
                    "Monitor current resource usage patterns",
                    "Implement dynamic resource allocation",
                    "Add resource usage alerts",
                    "Optimize concurrent processing limits"
                ]
            },
            "quality_improvement": {
                "category": "quality",
                "title": "Quality Improvement",
                "base_impact": 80.0,
                "base_effort": 50.0,
                "implementation_steps": [
                    "Analyze quality metrics and failure patterns",
                    "Implement quality validation checks",
                    "Add fallback mechanisms",
                    "Monitor quality improvements"
                ]
            },
            "reliability_enhancement": {
                "category": "reliability",
                "title": "Reliability Enhancement",
                "base_impact": 85.0,
                "base_effort": 60.0,
                "implementation_steps": [
                    "Identify failure points and patterns",
                    "Implement retry mechanisms",
                    "Add health checks and monitoring",
                    "Create failover procedures"
                ]
            }
        }
        
        # Stage-specific optimization knowledge
        self.stage_optimizations = {
            "stage01_intake": {
                "common_issues": ["slow_metadata_lookup", "api_failures", "network_timeouts"],
                "optimizations": {
                    "caching": {"impact": 60, "effort": 30},
                    "parallel_requests": {"impact": 70, "effort": 40},
                    "api_failover": {"impact": 80, "effort": 50}
                }
            },
            "stage02_download": {
                "common_issues": ["slow_downloads", "queue_buildup", "bandwidth_limits"],
                "optimizations": {
                    "bandwidth_management": {"impact": 65, "effort": 35},
                    "queue_optimization": {"impact": 75, "effort": 45},
                    "parallel_downloads": {"impact": 80, "effort": 55}
                }
            },
            "stage03_mkv": {
                "common_issues": ["high_cpu_usage", "memory_leaks", "processing_delays"],
                "optimizations": {
                    "gpu_acceleration": {"impact": 90, "effort": 70},
                    "memory_optimization": {"impact": 70, "effort": 40},
                    "processing_queue": {"impact": 60, "effort": 30}
                }
            },
            "stage04_subtitle": {
                "common_issues": ["ocr_failures", "quality_issues", "processing_time"],
                "optimizations": {
                    "ocr_service_selection": {"impact": 75, "effort": 35},
                    "quality_preprocessing": {"impact": 80, "effort": 50},
                    "result_caching": {"impact": 65, "effort": 25}
                }
            },
            "stage04b_mux": {
                "common_issues": ["muxing_failures", "file_size_issues", "compatibility"],
                "optimizations": {
                    "parameter_optimization": {"impact": 70, "effort": 40},
                    "format_validation": {"impact": 75, "effort": 35},
                    "compression_tuning": {"impact": 65, "effort": 45}
                }
            },
            "stage05_qc": {
                "common_issues": ["poster_download_failures", "file_validation", "cleanup_issues"],
                "optimizations": {
                    "poster_source_optimization": {"impact": 60, "effort": 30},
                    "validation_enhancement": {"impact": 70, "effort": 40},
                    "cleanup_automation": {"impact": 55, "effort": 25}
                }
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize the optimization recommender."""
        try:
            self.logger.info("Initializing Optimization Recommender...")
            
            if not self.trend_analyzer:
                self.logger.error("Trend analyzer not available")
                return False
            
            # Load historical recommendations
            await self._load_historical_recommendations()
            
            # Load performance patterns
            await self._load_performance_patterns()
            
            self.initialized = True
            self.logger.info("Optimization Recommender initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Optimization Recommender: {e}")
            return False
    
    async def _load_historical_recommendations(self) -> None:
        """Load historical recommendations from memory."""
        try:
            if not self.memory_service:
                return
            
            recommendation_memories = await self.memory_service.search_memories(
                category="optimization_recommendations"
            )
            
            for memory in recommendation_memories:
                try:
                    rec_data = memory["value"]
                    recommendation = OptimizationRecommendation(
                        id=rec_data["id"],
                        category=rec_data["category"],
                        priority=rec_data["priority"],
                        title=rec_data["title"],
                        description=rec_data["description"],
                        impact_score=rec_data["impact_score"],
                        effort_score=rec_data["effort_score"],
                        roi_score=rec_data["roi_score"],
                        affected_stages=rec_data["affected_stages"],
                        affected_metrics=rec_data["affected_metrics"],
                        implementation_steps=rec_data["implementation_steps"],
                        expected_improvement=rec_data["expected_improvement"],
                        prerequisites=rec_data["prerequisites"],
                        risks=rec_data["risks"],
                        monitoring_metrics=rec_data["monitoring_metrics"],
                        created_time=datetime.fromisoformat(rec_data["created_time"]),
                        confidence=rec_data["confidence"]
                    )
                    self.recommendations[recommendation.id] = recommendation
                except Exception as e:
                    self.logger.error(f"Error loading recommendation: {e}")
            
            self.logger.info(f"Loaded {len(self.recommendations)} historical recommendations")
            
        except Exception as e:
            self.logger.error(f"Error loading historical recommendations: {e}")
    
    async def _load_performance_patterns(self) -> None:
        """Load performance patterns from memory."""
        try:
            if not self.memory_service:
                return
            
            pattern_memories = await self.memory_service.search_memories(
                category="performance_patterns"
            )
            
            for memory in pattern_memories:
                try:
                    pattern_data = memory["value"]
                    pattern = PerformancePattern(
                        pattern_type=pattern_data["pattern_type"],
                        frequency=pattern_data["frequency"],
                        severity=pattern_data["severity"],
                        affected_components=pattern_data["affected_components"],
                        pattern_data=pattern_data["pattern_data"],
                        first_detected=datetime.fromisoformat(pattern_data["first_detected"]),
                        last_detected=datetime.fromisoformat(pattern_data["last_detected"])
                    )
                    self.patterns[memory["key"]] = pattern
                except Exception as e:
                    self.logger.error(f"Error loading pattern: {e}")
            
            self.logger.info(f"Loaded {len(self.patterns)} performance patterns")
            
        except Exception as e:
            self.logger.error(f"Error loading performance patterns: {e}")
    
    async def analyze_and_recommend(self) -> List[OptimizationRecommendation]:
        """Analyze current performance and generate optimization recommendations."""
        try:
            if not self.initialized:
                return []
            
            self.logger.info("Analyzing performance for optimization recommendations...")
            
            # Get current performance data
            trend_analyses = await self.trend_analyzer.get_all_trend_analyses()
            bottlenecks = await self.trend_analyzer.get_active_bottlenecks()
            performance_summary = await self.trend_analyzer.generate_performance_summary()
            
            # Detect performance patterns
            await self._detect_performance_patterns(trend_analyses, bottlenecks)
            
            # Generate recommendations
            new_recommendations = []
            
            # Bottleneck-based recommendations
            for bottleneck in bottlenecks:
                recommendations = await self._generate_bottleneck_recommendations(bottleneck)
                new_recommendations.extend(recommendations)
            
            # Pattern-based recommendations
            for pattern in self.patterns.values():
                recommendations = await self._generate_pattern_based_recommendations(pattern)
                new_recommendations.extend(recommendations)
            
            # Trend-based recommendations
            for metric_name, analysis in trend_analyses.items():
                recommendations = await self._generate_trend_recommendations(metric_name, analysis)
                new_recommendations.extend(recommendations)
            
            # Performance-based recommendations
            recommendations = await self._generate_performance_recommendations(performance_summary)
            new_recommendations.extend(recommendations)
            
            # Deduplicate and prioritize recommendations
            unique_recommendations = self._deduplicate_recommendations(new_recommendations)
            prioritized_recommendations = self._prioritize_recommendations(unique_recommendations)
            
            # Store new recommendations
            for recommendation in prioritized_recommendations:
                self.recommendations[recommendation.id] = recommendation
                await self._store_recommendation(recommendation)
            
            self.logger.info(f"Generated {len(prioritized_recommendations)} optimization recommendations")
            return prioritized_recommendations
            
        except Exception as e:
            self.logger.error(f"Error in analyze_and_recommend: {e}")
            return []
    
    async def _detect_performance_patterns(self, trend_analyses: Dict, bottlenecks: List) -> None:
        """Detect performance patterns from trend analyses and bottlenecks."""
        try:
            # Detect bottleneck patterns
            bottleneck_stages = Counter([b.stage for b in bottlenecks])
            for stage, count in bottleneck_stages.items():
                if count >= 2:  # Multiple bottlenecks in same stage
                    pattern_id = f"recurring_bottleneck_{stage}"
                    pattern = PerformancePattern(
                        pattern_type="bottleneck",
                        frequency=count / len(bottlenecks) if bottlenecks else 0,
                        severity=max([b.impact_score for b in bottlenecks if b.stage == stage]) / 100.0,
                        affected_components=[stage],
                        pattern_data={"bottleneck_count": count, "stage": stage},
                        first_detected=min([b.detection_time for b in bottlenecks if b.stage == stage]),
                        last_detected=max([b.detection_time for b in bottlenecks if b.stage == stage])
                    )
                    self.patterns[pattern_id] = pattern
                    await self._store_pattern(pattern_id, pattern)
            
            # Detect trend patterns
            declining_metrics = []
            for metric_name, analysis in trend_analyses.items():
                if analysis.trend_direction == "decreasing":
                    # Check if this is a "good" metric that should be increasing
                    if any(good_metric in metric_name for good_metric in ["success_rate", "throughput"]):
                        declining_metrics.append(metric_name)
                elif analysis.trend_direction == "increasing":
                    # Check if this is a "bad" metric that should be decreasing
                    if any(bad_metric in metric_name for bad_metric in ["processing_time", "error_rate"]):
                        declining_metrics.append(metric_name)
            
            if declining_metrics:
                pattern_id = "performance_degradation"
                pattern = PerformancePattern(
                    pattern_type="inefficiency",
                    frequency=len(declining_metrics) / len(trend_analyses),
                    severity=0.7,  # Moderate severity
                    affected_components=list(set([m.split("_")[0] for m in declining_metrics])),
                    pattern_data={"declining_metrics": declining_metrics},
                    first_detected=datetime.now(timezone.utc) - timedelta(days=7),
                    last_detected=datetime.now(timezone.utc)
                )
                self.patterns[pattern_id] = pattern
                await self._store_pattern(pattern_id, pattern)
            
        except Exception as e:
            self.logger.error(f"Error detecting performance patterns: {e}")
    
    async def _generate_bottleneck_recommendations(self, bottleneck) -> List[OptimizationRecommendation]:
        """Generate recommendations based on a specific bottleneck."""
        recommendations = []
        
        try:
            stage = bottleneck.stage
            stage_config = self.stage_optimizations.get(stage, {})
            optimizations = stage_config.get("optimizations", {})
            
            for opt_name, opt_config in optimizations.items():
                rec_id = f"bottleneck_{stage}_{opt_name}_{int(datetime.now().timestamp())}"
                
                # Calculate impact and effort based on bottleneck severity
                severity_multiplier = {
                    "critical": 1.5,
                    "high": 1.2,
                    "medium": 1.0,
                    "low": 0.8
                }.get(bottleneck.severity, 1.0)
                
                impact_score = min(opt_config["impact"] * severity_multiplier, 100.0)
                effort_score = opt_config["effort"]
                roi_score = impact_score / effort_score if effort_score > 0 else 0
                
                recommendation = OptimizationRecommendation(
                    id=rec_id,
                    category="performance",
                    priority=bottleneck.severity,
                    title=f"Optimize {opt_name.replace('_', ' ').title()} for {stage}",
                    description=f"Address {bottleneck.description} by implementing {opt_name.replace('_', ' ')} optimization",
                    impact_score=impact_score,
                    effort_score=effort_score,
                    roi_score=roi_score,
                    affected_stages=[stage],
                    affected_metrics=bottleneck.affected_metrics,
                    implementation_steps=self._generate_implementation_steps(stage, opt_name),
                    expected_improvement={
                        "processing_time": -20.0 if "time" in opt_name else -10.0,
                        "success_rate": 15.0 if "reliability" in opt_name else 5.0,
                        "resource_usage": -25.0 if "resource" in opt_name else -5.0
                    },
                    prerequisites=self._get_prerequisites(stage, opt_name),
                    risks=self._get_risks(stage, opt_name),
                    monitoring_metrics=[f"{stage}_processing_time", f"{stage}_success_rate"],
                    created_time=datetime.now(timezone.utc),
                    confidence=0.8
                )
                
                recommendations.append(recommendation)
                
        except Exception as e:
            self.logger.error(f"Error generating bottleneck recommendations: {e}")
        
        return recommendations

    async def _generate_pattern_based_recommendations(self, pattern) -> List[OptimizationRecommendation]:
        """Generate recommendations based on a performance pattern."""
        recommendations = []

        try:
            pattern_type = pattern.pattern_type
            severity = pattern.severity
            affected_components = pattern.affected_components

            for component in affected_components:
                rec_id = f"pattern_{pattern_type}_{component}_{int(datetime.now().timestamp())}"

                # Calculate impact and effort based on pattern severity
                impact_score = min(severity * 100, 100.0)
                effort_score = 40.0  # Default effort
                roi_score = impact_score / effort_score if effort_score > 0 else 0

                priority = "high" if severity > 0.7 else "medium" if severity > 0.4 else "low"

                recommendation = OptimizationRecommendation(
                    id=rec_id,
                    category="performance",
                    priority=priority,
                    title=f"Address {pattern_type} pattern in {component}",
                    description=f"Optimize {component} to address detected {pattern_type} pattern",
                    impact_score=impact_score,
                    effort_score=effort_score,
                    roi_score=roi_score,
                    affected_stages=[component],
                    affected_metrics=[f"{component}_performance"],
                    implementation_steps=self._generate_implementation_steps(component, pattern_type),
                    expected_improvement={
                        "performance": 20.0,
                        "efficiency": 15.0
                    },
                    prerequisites=[f"Analyze {component} performance patterns"],
                    risks=["Implementation complexity"],
                    monitoring_metrics=[f"{component}_performance", f"{component}_efficiency"],
                    created_time=datetime.now(timezone.utc),
                    confidence=0.7
                )

                recommendations.append(recommendation)

        except Exception as e:
            self.logger.error(f"Error generating pattern-based recommendations: {e}")

        return recommendations

    async def _generate_trend_recommendations(self, metric_name: str, analysis) -> List[OptimizationRecommendation]:
        """Generate recommendations based on trend analysis."""
        recommendations = []

        try:
            if analysis.trend_direction == "stable":
                return recommendations  # No recommendations for stable trends

            stage = metric_name.split("_")[0] if "_" in metric_name else "unknown"
            metric_type = metric_name.split("_", 1)[1] if "_" in metric_name else metric_name

            rec_id = f"trend_{metric_name}_{int(datetime.now().timestamp())}"

            # Determine if this is a concerning trend
            concerning = False
            if analysis.trend_direction == "increasing" and metric_type in ["processing_time", "error_rate"]:
                concerning = True
            elif analysis.trend_direction == "decreasing" and metric_type in ["success_rate", "throughput"]:
                concerning = True

            if concerning:
                impact_score = min(analysis.trend_strength * 80 + 20, 100.0)
                effort_score = 35.0
                roi_score = impact_score / effort_score

                priority = "high" if analysis.trend_strength > 0.7 else "medium"

                recommendation = OptimizationRecommendation(
                    id=rec_id,
                    category="performance",
                    priority=priority,
                    title=f"Address {analysis.trend_direction} trend in {metric_type}",
                    description=f"Optimize {stage} to address {analysis.trend_direction} trend in {metric_type}",
                    impact_score=impact_score,
                    effort_score=effort_score,
                    roi_score=roi_score,
                    affected_stages=[stage],
                    affected_metrics=[metric_name],
                    implementation_steps=self._generate_implementation_steps(stage, f"trend_{metric_type}"),
                    expected_improvement={
                        metric_type: -15.0 if analysis.trend_direction == "increasing" else 15.0
                    },
                    prerequisites=[f"Analyze {metric_type} trend patterns"],
                    risks=["Trend may be temporary"],
                    monitoring_metrics=[metric_name],
                    created_time=datetime.now(timezone.utc),
                    confidence=analysis.r_squared
                )

                recommendations.append(recommendation)

        except Exception as e:
            self.logger.error(f"Error generating trend recommendations: {e}")

        return recommendations

    async def _generate_performance_recommendations(self, performance_summary: Dict) -> List[OptimizationRecommendation]:
        """Generate recommendations based on overall performance summary."""
        recommendations = []

        try:
            health_score = performance_summary.get("health_score", 100.0)

            if health_score < 80.0:
                rec_id = f"health_{int(datetime.now().timestamp())}"

                impact_score = 100.0 - health_score
                effort_score = 50.0
                roi_score = impact_score / effort_score

                priority = "critical" if health_score < 50 else "high" if health_score < 70 else "medium"

                recommendation = OptimizationRecommendation(
                    id=rec_id,
                    category="reliability",
                    priority=priority,
                    title="Improve Overall System Health",
                    description=f"System health score is {health_score:.1f}/100. Comprehensive optimization needed.",
                    impact_score=impact_score,
                    effort_score=effort_score,
                    roi_score=roi_score,
                    affected_stages=["all"],
                    affected_metrics=["health_score"],
                    implementation_steps=[
                        "Analyze all active bottlenecks",
                        "Prioritize critical issues",
                        "Implement systematic improvements",
                        "Monitor health score improvements"
                    ],
                    expected_improvement={
                        "health_score": 20.0,
                        "overall_performance": 15.0
                    },
                    prerequisites=["Complete system analysis"],
                    risks=["System-wide changes may cause temporary instability"],
                    monitoring_metrics=["health_score", "bottleneck_count"],
                    created_time=datetime.now(timezone.utc),
                    confidence=0.9
                )

                recommendations.append(recommendation)

        except Exception as e:
            self.logger.error(f"Error generating performance recommendations: {e}")

        return recommendations

    def _generate_implementation_steps(self, stage: str, optimization: str) -> List[str]:
        """Generate implementation steps for a specific optimization."""
        base_steps = {
            "caching": [
                "Implement caching layer for frequently accessed data",
                "Configure cache expiration policies",
                "Add cache hit/miss monitoring",
                "Test cache performance impact"
            ],
            "parallel_processing": [
                "Identify parallelizable operations",
                "Implement thread/process pool",
                "Add synchronization mechanisms",
                "Monitor resource usage and performance"
            ],
            "resource_optimization": [
                "Profile current resource usage",
                "Implement resource pooling",
                "Add resource usage monitoring",
                "Optimize resource allocation algorithms"
            ],
            "quality_improvement": [
                "Analyze current quality metrics",
                "Implement quality validation checks",
                "Add quality monitoring and alerts",
                "Create quality improvement feedback loop"
            ]
        }
        
        return base_steps.get(optimization, [
            f"Analyze current {stage} performance",
            f"Implement {optimization} optimization",
            f"Test and validate improvements",
            f"Monitor {optimization} effectiveness"
        ])
    
    def _get_prerequisites(self, stage: str, optimization: str) -> List[str]:
        """Get prerequisites for implementing an optimization."""
        prerequisites = {
            "gpu_acceleration": ["GPU hardware available", "GPU drivers installed"],
            "parallel_processing": ["Multi-core CPU available", "Thread-safe code"],
            "caching": ["Sufficient memory available", "Cache storage configured"],
            "bandwidth_management": ["Network monitoring tools", "QoS configuration"]
        }
        
        return prerequisites.get(optimization, ["Performance baseline established"])
    
    def _get_risks(self, stage: str, optimization: str) -> List[str]:
        """Get risks associated with implementing an optimization."""
        risks = {
            "parallel_processing": ["Increased complexity", "Potential race conditions"],
            "caching": ["Memory usage increase", "Cache invalidation issues"],
            "gpu_acceleration": ["Hardware dependency", "Driver compatibility issues"],
            "resource_optimization": ["Temporary performance degradation during implementation"]
        }
        
        return risks.get(optimization, ["Implementation complexity", "Temporary system instability"])
    
    def _deduplicate_recommendations(self, recommendations: List[OptimizationRecommendation]) -> List[OptimizationRecommendation]:
        """Remove duplicate recommendations based on similarity."""
        unique_recommendations = []
        seen_combinations = set()
        
        for rec in recommendations:
            # Create a signature based on category, affected stages, and title similarity
            signature = (rec.category, tuple(sorted(rec.affected_stages)), rec.title.lower())
            
            if signature not in seen_combinations:
                seen_combinations.add(signature)
                unique_recommendations.append(rec)
        
        return unique_recommendations
    
    def _prioritize_recommendations(self, recommendations: List[OptimizationRecommendation]) -> List[OptimizationRecommendation]:
        """Prioritize recommendations based on ROI, impact, and urgency."""
        # Sort by ROI score (descending), then by impact score (descending)
        return sorted(recommendations, key=lambda r: (r.roi_score, r.impact_score), reverse=True)
    
    async def _store_recommendation(self, recommendation: OptimizationRecommendation) -> None:
        """Store recommendation in memory for persistence."""
        try:
            if self.memory_service:
                await self.memory_service.store_memory(
                    category="optimization_recommendations",
                    key=recommendation.id,
                    value=recommendation.to_dict(),
                    tags=["optimization", recommendation.category, recommendation.priority]
                )
        except Exception as e:
            self.logger.error(f"Error storing recommendation: {e}")
    
    async def _store_pattern(self, pattern_id: str, pattern: PerformancePattern) -> None:
        """Store performance pattern in memory for persistence."""
        try:
            if self.memory_service:
                await self.memory_service.store_memory(
                    category="performance_patterns",
                    key=pattern_id,
                    value=pattern.to_dict(),
                    tags=["pattern", pattern.pattern_type]
                )
        except Exception as e:
            self.logger.error(f"Error storing pattern: {e}")
    
    async def get_recommendations(self, category: str = None, 
                                 priority: str = None, 
                                 limit: int = None) -> List[OptimizationRecommendation]:
        """Get optimization recommendations with optional filtering."""
        recommendations = list(self.recommendations.values())
        
        if category:
            recommendations = [r for r in recommendations if r.category == category]
        
        if priority:
            recommendations = [r for r in recommendations if r.priority == priority]
        
        # Sort by ROI score
        recommendations.sort(key=lambda r: r.roi_score, reverse=True)
        
        if limit:
            recommendations = recommendations[:limit]
        
        return recommendations
    
    async def get_recommendation_by_id(self, recommendation_id: str) -> Optional[OptimizationRecommendation]:
        """Get a specific recommendation by ID."""
        return self.recommendations.get(recommendation_id)
    
    async def mark_recommendation_implemented(self, recommendation_id: str) -> bool:
        """Mark a recommendation as implemented."""
        try:
            recommendation = self.recommendations.get(recommendation_id)
            if not recommendation:
                return False
            
            # Store implementation record
            if self.memory_service:
                await self.memory_service.store_memory(
                    category="implemented_optimizations",
                    key=recommendation_id,
                    value={
                        "recommendation": recommendation.to_dict(),
                        "implemented_time": datetime.now(timezone.utc).isoformat(),
                        "status": "implemented"
                    },
                    tags=["implemented", recommendation.category]
                )
            
            # Remove from active recommendations
            del self.recommendations[recommendation_id]
            
            self.logger.info(f"Marked recommendation as implemented: {recommendation.title}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error marking recommendation as implemented: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Perform health check on the recommender."""
        return (self.initialized and 
                self.trend_analyzer is not None and
                self.memory_service is not None)
    
    async def cleanup(self) -> None:
        """Cleanup the recommender."""
        self.logger.info("Cleaning up Optimization Recommender...")
        self.recommendations.clear()
        self.patterns.clear()
        self.analysis_history.clear()
        self.initialized = False

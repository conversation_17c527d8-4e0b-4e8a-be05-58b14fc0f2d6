# SABnzbd Post-Processing Scripts

This folder contains scripts that SABnzbd calls automatically after downloads complete.

## Files

### `sabnzbd_post_process.py`
**Purpose:** Bridge script that connects SABnzbd to the PlexMovieAutomator pipeline.

**What it does:**
1. Gets called by SABnzbd when a download finishes
2. Logs the download completion details
3. Triggers the pipeline's Stage 02 (download and organize)
4. Processes the completed download immediately

**SABnzbd Configuration:**
- **Scripts Folder:** `C:\Users\<USER>\Videos\PlexMovieAutomator\scripts`
- **Script:** `sabnzbd_post_process.py`
- **Parameters:** SABnzbd automatically passes 7 parameters

## How It Works

```
SABnzbd Download Complete
         ↓
sabnzbd_post_process.py
         ↓
Triggers Pipeline Stage 02
         ↓
Organizes movie to workspace/2_downloaded_and_organized
         ↓
Ready for next pipeline stages
```

## Logs

Post-processing logs are written to: `logs/sabnzbd_post_process.log`

## Testing

To test the script manually:
```bash
python scripts/sabnzbd_post_process.py "test_folder" "test.nzb" "Test Movie" "123" "movies" "group" "0"
```

#!/usr/bin/env python3
"""
Filesystem-First Status Dashboard for PlexMovieAutomator.

This dashboard demonstrates how to display pipeline status using the new
filesystem-first state management architecture.
"""

import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
from _internal.utils.filesystem_first_validation import FilesystemFirstValidationEngine


def generate_filesystem_first_status_dashboard() -> Dict[str, Any]:
    """
    Generate a comprehensive status dashboard using filesystem-first approach.
    
    Returns:
        Dictionary containing all dashboard data
    """
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)
    validation_engine = FilesystemFirstValidationEngine(workspace_root)
    
    try:
        # Get current state from filesystem
        movies_by_stage = filesystem_manager.discover_movies_by_stage()
        
        # Get metadata statistics
        metadata_records = metadata_db.get_all_movies()
        
        # Run validation
        health_report = validation_engine.generate_health_report()
        
        # Build dashboard
        dashboard = {
            'timestamp': datetime.now().isoformat(),
            'system_type': 'filesystem_first',
            'overview': _generate_overview(movies_by_stage, metadata_records),
            'stage_details': _generate_stage_details(movies_by_stage),
            'metadata_summary': _generate_metadata_summary(metadata_records),
            'health_status': health_report,
            'recent_activity': _generate_recent_activity(movies_by_stage, filesystem_manager),
            'recommendations': _generate_recommendations(movies_by_stage, health_report)
        }
        
        return dashboard
        
    finally:
        metadata_db.close()


def _generate_overview(movies_by_stage: Dict[str, List], metadata_records: List) -> Dict[str, Any]:
    """Generate high-level overview statistics."""
    total_movies = sum(len(movies) for movies in movies_by_stage.values())
    
    # Count movies by general status
    active_processing = len(movies_by_stage.get('mkv_processing_active', [])) + \
                      len(movies_by_stage.get('subtitle_processing_active', []))
    
    pending_work = len(movies_by_stage.get('mkv_processing_pending', [])) + \
                   len(movies_by_stage.get('subtitle_processing_pending', [])) + \
                   len(movies_by_stage.get('final_mux_pending', []))
    
    completed = len(movies_by_stage.get('completed', []))
    errors = len(movies_by_stage.get('error', []))
    
    return {
        'total_movies_discovered': total_movies,
        'metadata_records': len(metadata_records),
        'active_processing': active_processing,
        'pending_work': pending_work,
        'completed': completed,
        'errors': errors,
        'discovery_method': 'filesystem_scanning'
    }


def _generate_stage_details(movies_by_stage: Dict[str, List]) -> Dict[str, Any]:
    """Generate detailed information for each pipeline stage."""
    stage_details = {}
    
    stage_descriptions = {
        'download_completed': 'Movies downloaded and ready for organization',
        'organized': 'Movies organized and ready for MKV processing',
        'mkv_processing_pending': 'Movies ready for MKV processing',
        'mkv_processing_active': 'Movies currently being processed (MKV)',
        'mkv_processing_interrupted': 'Movies with interrupted MKV processing',
        'mkv_processing_complete': 'Movies with MKV processing complete',
        'subtitle_processing_pending': 'Movies ready for subtitle processing',
        'subtitle_processing_active': 'Movies currently being processed (subtitles)',
        'subtitle_processing_complete': 'Movies with subtitle processing complete',
        'final_mux_pending': 'Movies ready for final muxing',
        'final_mux_complete': 'Movies with final muxing complete',
        'completed': 'Movies fully processed and ready for Plex',
        'error': 'Movies with processing errors'
    }
    
    for stage, movies in movies_by_stage.items():
        stage_details[stage] = {
            'count': len(movies),
            'description': stage_descriptions.get(stage, f'Movies in {stage} stage'),
            'movies': [
                {
                    'title': movie.get('cleaned_title', 'Unknown'),
                    'year': movie.get('year'),
                    'directory': movie.get('movie_directory', ''),
                    'discovered_at': movie.get('discovered_at')
                }
                for movie in movies[:10]  # Limit to first 10 for display
            ],
            'has_more': len(movies) > 10
        }
    
    return stage_details


def _generate_metadata_summary(metadata_records: List) -> Dict[str, Any]:
    """Generate summary of metadata database contents."""
    if not metadata_records:
        return {
            'total_records': 0,
            'has_tmdb_id': 0,
            'has_imdb_id': 0,
            'languages': {},
            'recent_updates': []
        }
    
    # Analyze metadata
    has_tmdb = sum(1 for record in metadata_records if record.get('tmdb_id'))
    has_imdb = sum(1 for record in metadata_records if record.get('imdb_id'))
    
    # Language distribution
    languages = {}
    for record in metadata_records:
        audio_lang = record.get('audio_lang', 'unknown')
        languages[audio_lang] = languages.get(audio_lang, 0) + 1
    
    # Recent updates (last 10)
    recent_updates = sorted(
        metadata_records,
        key=lambda x: x.get('last_updated', ''),
        reverse=True
    )[:10]
    
    return {
        'total_records': len(metadata_records),
        'has_tmdb_id': has_tmdb,
        'has_imdb_id': has_imdb,
        'tmdb_coverage': round((has_tmdb / len(metadata_records)) * 100, 1),
        'imdb_coverage': round((has_imdb / len(metadata_records)) * 100, 1),
        'languages': languages,
        'recent_updates': [
            {
                'title': record.get('title'),
                'year': record.get('year'),
                'last_updated': record.get('last_updated')
            }
            for record in recent_updates
        ]
    }


def _generate_recent_activity(movies_by_stage: Dict[str, List], 
                            filesystem_manager: FilesystemFirstStateManager) -> List[Dict[str, Any]]:
    """Generate recent activity log based on marker file timestamps."""
    recent_activity = []
    
    # Check movies in active/recent stages for marker activity
    active_stages = [
        'mkv_processing_active', 'mkv_processing_interrupted',
        'subtitle_processing_active', 'final_mux_pending'
    ]
    
    for stage in active_stages:
        for movie in movies_by_stage.get(stage, []):
            movie_dir = Path(movie.get('movie_directory', ''))
            if not movie_dir.exists():
                continue
            
            # Check for recent marker activity
            for marker_type in filesystem_manager.MARKERS.keys():
                marker_data = filesystem_manager.get_stage_marker_data(movie_dir, marker_type)
                if marker_data and marker_data.get('timestamp'):
                    recent_activity.append({
                        'timestamp': marker_data['timestamp'],
                        'movie': movie.get('cleaned_title', 'Unknown'),
                        'action': f"Set {marker_type} marker",
                        'stage': stage,
                        'details': marker_data
                    })
    
    # Sort by timestamp (most recent first)
    recent_activity.sort(key=lambda x: x['timestamp'], reverse=True)
    
    return recent_activity[:20]  # Return last 20 activities


def _generate_recommendations(movies_by_stage: Dict[str, List], 
                            health_report: Dict[str, Any]) -> List[str]:
    """Generate actionable recommendations based on current state."""
    recommendations = []
    
    # Check for stuck processing
    interrupted_mkv = len(movies_by_stage.get('mkv_processing_interrupted', []))
    if interrupted_mkv > 0:
        recommendations.append(f"Resume {interrupted_mkv} interrupted MKV processing jobs")
    
    # Check for pending work
    pending_mkv = len(movies_by_stage.get('mkv_processing_pending', []))
    if pending_mkv > 0:
        recommendations.append(f"Process {pending_mkv} movies ready for MKV processing")
    
    pending_subtitle = len(movies_by_stage.get('subtitle_processing_pending', []))
    if pending_subtitle > 0:
        recommendations.append(f"Process {pending_subtitle} movies ready for subtitle processing")
    
    pending_mux = len(movies_by_stage.get('final_mux_pending', []))
    if pending_mux > 0:
        recommendations.append(f"Process {pending_mux} movies ready for final muxing")
    
    # Check health status
    health_score = health_report.get('health_score', 100)
    if health_score < 80:
        recommendations.append("Run system validation to address health issues")
    
    # Check for errors
    error_count = len(movies_by_stage.get('error', []))
    if error_count > 0:
        recommendations.append(f"Investigate and resolve {error_count} movies in error state")
    
    # Add health recommendations
    health_recommendations = health_report.get('recommendations', [])
    recommendations.extend(health_recommendations[:3])  # Add top 3 health recommendations
    
    if not recommendations:
        recommendations.append("System is healthy and up to date!")
    
    return recommendations


def print_dashboard_summary():
    """Print a formatted summary of the dashboard to console."""
    dashboard = generate_filesystem_first_status_dashboard()
    
    print("=" * 80)
    print("PLEXMOVIEAUTOMATOR - FILESYSTEM-FIRST STATUS DASHBOARD")
    print("=" * 80)
    print(f"Generated: {dashboard['timestamp']}")
    print(f"System Type: {dashboard['system_type']}")
    print()
    
    # Overview
    overview = dashboard['overview']
    print("📊 OVERVIEW")
    print("-" * 40)
    print(f"Total Movies Discovered: {overview['total_movies_discovered']}")
    print(f"Metadata Records: {overview['metadata_records']}")
    print(f"Active Processing: {overview['active_processing']}")
    print(f"Pending Work: {overview['pending_work']}")
    print(f"Completed: {overview['completed']}")
    print(f"Errors: {overview['errors']}")
    print()
    
    # Stage Summary
    print("🎬 STAGE SUMMARY")
    print("-" * 40)
    stage_details = dashboard['stage_details']
    for stage, details in stage_details.items():
        if details['count'] > 0:
            print(f"{stage:30} {details['count']:3d} movies")
    print()
    
    # Health Status
    health = dashboard['health_status']
    print("🏥 HEALTH STATUS")
    print("-" * 40)
    print(f"Health Score: {health.get('health_score', 'N/A')}/100")
    print(f"Issues Found: {health.get('validation_summary', {}).get('issues_found', 0)}")
    print(f"Corrections Made: {health.get('validation_summary', {}).get('corrections_made', 0)}")
    print()
    
    # Recommendations
    recommendations = dashboard['recommendations']
    if recommendations:
        print("💡 RECOMMENDATIONS")
        print("-" * 40)
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"{i}. {rec}")
        print()
    
    print("=" * 80)


def save_dashboard_json(output_path: Path = None):
    """Save dashboard data as JSON file."""
    if output_path is None:
        output_path = Path.cwd() / "_internal" / "reports" / "status_dashboard.json"
    
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    dashboard = generate_filesystem_first_status_dashboard()
    
    with open(output_path, 'w') as f:
        json.dump(dashboard, f, indent=2)
    
    print(f"Dashboard saved to: {output_path}")


if __name__ == "__main__":
    # Print dashboard to console
    print_dashboard_summary()
    
    # Save to JSON file
    save_dashboard_json()
    
    print("\n✅ Dashboard generation complete!")

#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/sonarr_integration.py

Sonarr MCP Server Integration
Provides TV show automation capabilities through Sonarr API for enhanced NZB search and download management.
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

class SonarrMCP:
    """
    MCP service for Sonarr API integration.
    Handles TV show searching, quality profiles, and download management.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        self.session = None
        
        # Sonarr configuration
        self.base_url = config.get('sonarr', {}).get('base_url', 'http://localhost:8989')
        self.api_key = config.get('sonarr', {}).get('api_key', '')
        self.timeout = config.get('sonarr', {}).get('timeout', 30)
        
        # Quality profiles for TV shows
        self.quality_profiles = {}
        
    async def initialize(self) -> bool:
        """Initialize the Sonarr MCP service."""
        try:
            self.logger.info("Initializing Sonarr MCP service...")
            
            # Create aiohttp session
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            headers = {
                'X-Api-Key': self.api_key,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers,
                connector=aiohttp.TCPConnector(ssl=False)
            )
            
            # Test connection and load quality profiles
            if await self.test_connection():
                await self.load_quality_profiles()
                self.logger.info("Sonarr MCP service initialized successfully")
                return True
            else:
                self.logger.error("Failed to connect to Sonarr API")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Sonarr MCP: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test connection to Sonarr API."""
        try:
            url = f"{self.base_url}/api/v3/system/status"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    version = data.get('version', 'Unknown')
                    self.logger.info(f"Connected to Sonarr v{version}")
                    return True
                else:
                    self.logger.error(f"Sonarr API returned status {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error testing Sonarr connection: {e}")
            return False
    
    async def load_quality_profiles(self):
        """Load quality profiles from Sonarr."""
        try:
            url = f"{self.base_url}/api/v3/qualityprofile"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    profiles = await response.json()
                    
                    for profile in profiles:
                        self.quality_profiles[profile['name']] = {
                            'id': profile['id'],
                            'name': profile['name'],
                            'qualities': profile['items'],
                            'min_size': profile.get('minSize', 0),
                            'max_size': profile.get('maxSize', 0)
                        }
                    
                    self.logger.info(f"Loaded {len(self.quality_profiles)} quality profiles")
                    
                    # Log available profiles
                    for name in self.quality_profiles.keys():
                        self.logger.info(f"  - {name}")
                        
                else:
                    self.logger.error(f"Failed to load quality profiles: HTTP {response.status}")
                    
        except Exception as e:
            self.logger.error(f"Error loading quality profiles: {e}")
    
    def parse_file_size(self, size_bytes: int) -> str:
        """Convert bytes to human readable format."""
        if size_bytes == 0:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        
        return f"{size_bytes:.1f} PB"
    
    def determine_quality_from_title(self, title: str) -> str:
        """Determine quality from release title."""
        title_upper = title.upper()
        
        if any(x in title_upper for x in ['4K', '2160P', 'UHD']):
            return '4K'
        elif '1080P' in title_upper:
            return '1080p'
        elif '720P' in title_upper:
            return '720p'
        else:
            return 'Unknown'
    
    async def search_series_lookup(self, title: str, year: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Search for TV series via Sonarr API lookup.
        """
        try:
            self.logger.info(f"Looking up TV series: {title}" + (f" ({year})" if year else ""))
            
            # Search for the series in Sonarr's database
            series_search_url = f"{self.base_url}/api/v3/series/lookup"
            search_term = f"{title} {year}" if year else title
            params = {'term': search_term}
            
            async with self.session.get(series_search_url, params=params) as response:
                if response.status != 200:
                    self.logger.error(f"Series lookup failed: HTTP {response.status}")
                    return []
                
                series_list = await response.json()
                
                if not series_list:
                    self.logger.warning(f"No TV series found for: {title}")
                    return []
                
                # Process series results
                processed_series = []
                
                for series in series_list[:5]:  # Limit to top 5 matches
                    try:
                        processed_series.append({
                            'title': series.get('title', ''),
                            'year': series.get('year', 0),
                            'tvdb_id': series.get('tvdbId', 0),
                            'imdb_id': series.get('imdbId', ''),
                            'overview': series.get('overview', ''),
                            'network': series.get('network', ''),
                            'status': series.get('status', ''),
                            'seasons': len(series.get('seasons', [])),
                            'genres': series.get('genres', [])
                        })
                        
                    except Exception as e:
                        self.logger.debug(f"Error processing series: {e}")
                        continue
                
                self.logger.info(f"Found {len(processed_series)} TV series matches")
                
                # Log matches for debugging
                for i, series in enumerate(processed_series, 1):
                    self.logger.info(f"  {i}. {series['title']} ({series['year']}) - {series['seasons']} seasons [{series['network']}]")
                
                return processed_series
                
        except Exception as e:
            self.logger.error(f"Error searching TV series: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    async def search_episode_releases(self, series_title: str, season: int, episode: int) -> List[Dict[str, Any]]:
        """
        Search for specific episode releases via Sonarr API.
        """
        try:
            self.logger.info(f"Searching for episode releases: {series_title} S{season:02d}E{episode:02d}")
            
            # This would typically require the series to be added to Sonarr first
            # For now, we'll simulate episode search results
            # In a real implementation, you'd need to:
            # 1. Add series to Sonarr
            # 2. Search for specific episodes
            # 3. Get release results
            
            # Simulate episode releases for demonstration
            mock_releases = [
                {
                    'title': f'{series_title}.S{season:02d}E{episode:02d}.1080p.WEB-DL.x264-GROUP',
                    'size_bytes': 2147483648,  # 2 GB
                    'size_display': '2.0 GB',
                    'quality': '1080p',
                    'indexer': 'nzbfinder',
                    'protocol': 'usenet',
                    'age_hours': 12,
                    'seeders': 0,
                    'leechers': 0
                },
                {
                    'title': f'{series_title}.S{season:02d}E{episode:02d}.720p.HDTV.x264-GROUP',
                    'size_bytes': 1073741824,  # 1 GB
                    'size_display': '1.0 GB',
                    'quality': '720p',
                    'indexer': 'nzbfinder',
                    'protocol': 'usenet',
                    'age_hours': 6,
                    'seeders': 0,
                    'leechers': 0
                }
            ]
            
            # Sort by quality preference (1080p first)
            mock_releases.sort(key=lambda x: {'1080p': 3, '720p': 2, 'Unknown': 1}.get(x['quality'], 0), reverse=True)
            
            self.logger.info(f"Found {len(mock_releases)} episode releases")
            
            return mock_releases
            
        except Exception as e:
            self.logger.error(f"Error searching episode releases: {e}")
            return []
    
    async def search_season_releases(self, series_title: str, season: int) -> List[Dict[str, Any]]:
        """
        Search for full season releases via Sonarr API.
        """
        try:
            self.logger.info(f"Searching for season releases: {series_title} Season {season}")
            
            # Simulate season pack releases
            mock_releases = [
                {
                    'title': f'{series_title}.S{season:02d}.1080p.BluRay.x264-GROUP',
                    'size_bytes': 32212254720,  # 30 GB
                    'size_display': '30.0 GB',
                    'quality': '1080p',
                    'indexer': 'nzbfinder',
                    'protocol': 'usenet',
                    'age_hours': 24,
                    'seeders': 0,
                    'leechers': 0,
                    'episode_count': 10
                },
                {
                    'title': f'{series_title}.S{season:02d}.720p.WEB-DL.x264-GROUP',
                    'size_bytes': 16106127360,  # 15 GB
                    'size_display': '15.0 GB',
                    'quality': '720p',
                    'indexer': 'nzbfinder',
                    'protocol': 'usenet',
                    'age_hours': 12,
                    'seeders': 0,
                    'leechers': 0,
                    'episode_count': 10
                }
            ]
            
            # Sort by size (larger first for better quality)
            mock_releases.sort(key=lambda x: x.get('size_bytes', 0), reverse=True)
            
            self.logger.info(f"Found {len(mock_releases)} season releases")
            
            return mock_releases
            
        except Exception as e:
            self.logger.error(f"Error searching season releases: {e}")
            return []
    
    def select_best_tv_quality(self, releases: List[Dict[str, Any]], preference: str = "1080p") -> List[Dict[str, Any]]:
        """
        Select best quality TV releases based on preference.
        
        TV show quality logic (simpler than movies):
        - Prefer 1080p for most shows
        - 720p acceptable for older shows
        - Largest file size within quality preference
        """
        if not releases:
            return []
        
        self.logger.info(f"Applying TV quality selection logic (preference: {preference})...")
        
        # Filter by preferred quality first
        preferred_releases = [r for r in releases if r.get('quality') == preference]
        
        if preferred_releases:
            # Return largest file of preferred quality
            best_release = max(preferred_releases, key=lambda x: x.get('size_bytes', 0))
            self.logger.info(f"   ✅ Selected {preference}: {best_release['title'][:60]}... - {best_release['size_display']}")
            return [best_release]
        
        # Fallback to any available quality (largest first)
        if releases:
            best_release = max(releases, key=lambda x: x.get('size_bytes', 0))
            self.logger.info(f"   ⚠️ No {preference} found, selected {best_release['quality']}: {best_release['title'][:60]}... - {best_release['size_display']}")
            return [best_release]
        
        self.logger.warning("   ❌ No suitable TV releases found")
        return []
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        if not self.session or self.session.closed:
            return False
        
        return await self.test_connection()
    
    async def add_series_to_sonarr(self, series_data: Dict[str, Any], quality_profile: str = "Standard") -> bool:
        """
        Add a TV series to Sonarr for monitoring.
        """
        try:
            self.logger.info(f"Adding series to Sonarr: {series_data['title']}")

            # Get quality profile ID
            profile_id = 1  # Default
            if quality_profile in self.quality_profiles:
                profile_id = self.quality_profiles[quality_profile]['id']

            add_series_url = f"{self.base_url}/api/v3/series"

            series_payload = {
                'title': series_data['title'],
                'titleSlug': series_data['title'].lower().replace(' ', '-'),
                'tvdbId': series_data.get('tvdb_id', 0),
                'qualityProfileId': profile_id,
                'languageProfileId': 1,  # English
                'rootFolderPath': '/tv/',  # Default TV folder
                'monitored': True,
                'seasonFolder': True,
                'addOptions': {
                    'monitor': 'all',  # Monitor all seasons
                    'searchForMissingEpisodes': True,
                    'searchForCutoffUnmetEpisodes': False
                }
            }

            async with self.session.post(add_series_url, json=series_payload) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"✅ Successfully added series: {series_data['title']}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to add series: HTTP {response.status} - {error_text}")
                    return False

        except Exception as e:
            self.logger.error(f"Error adding series to Sonarr: {e}")
            return False

    async def download_episode_release(self, release: Dict[str, Any], series_title: str, season: int, episode: int) -> bool:
        """
        Download an episode release via Sonarr API.
        """
        try:
            self.logger.info(f"Downloading episode: {series_title} S{season:02d}E{episode:02d}")

            download_url = f"{self.base_url}/api/v3/release"

            download_data = {
                'guid': release.get('guid', f"mock-guid-{series_title}-s{season}e{episode}"),
                'indexerId': release.get('indexerId', 1),
                'title': release.get('title'),
                'size': release.get('size_bytes', 0),
                'downloadUrl': release.get('download_url', ''),
                'protocol': release.get('protocol', 'usenet')
            }

            async with self.session.post(download_url, json=download_data) as response:
                if response.status in [200, 201]:
                    self.logger.info(f"✅ Successfully sent episode to download client: {release['title'][:60]}...")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Episode download failed: HTTP {response.status} - {error_text}")
                    return False

        except Exception as e:
            self.logger.error(f"Error downloading episode: {e}")
            return False

    async def get_series_queue(self) -> List[Dict[str, Any]]:
        """Get current download queue from Sonarr."""
        try:
            url = f"{self.base_url}/api/v3/queue"

            async with self.session.get(url) as response:
                if response.status == 200:
                    queue_data = await response.json()

                    queue_items = []
                    for item in queue_data.get('records', []):
                        queue_items.append({
                            'title': item.get('title', ''),
                            'series': item.get('series', {}).get('title', ''),
                            'season': item.get('episode', {}).get('seasonNumber', 0),
                            'episode': item.get('episode', {}).get('episodeNumber', 0),
                            'status': item.get('status', ''),
                            'progress': item.get('sizeleft', 0),
                            'total_size': item.get('size', 0),
                            'eta': item.get('timeleft', ''),
                            'download_client': item.get('downloadClient', '')
                        })

                    return queue_items
                else:
                    self.logger.error(f"Failed to get download queue: HTTP {response.status}")
                    return []

        except Exception as e:
            self.logger.error(f"Error getting download queue: {e}")
            return []

    async def create_tv_nzb_file(self, release: Dict[str, Any], series_title: str, season: int, episode: int, output_dir: str) -> Optional[str]:
        """
        Create NZB file for TV episode (fallback option).
        """
        try:
            import os

            # Create safe filename
            safe_title = "".join(c for c in release['title'] if c.isalnum() or c in (' ', '-', '_', '.')).rstrip()
            nzb_filename = f"{safe_title}.nzb"
            nzb_path = os.path.join(output_dir, nzb_filename)

            # Create basic NZB content
            nzb_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<nzb xmlns="http://www.newzbin.com/DTD/2003/nzb">
    <head>
        <meta type="title">{release['title']}</meta>
        <meta type="size">{release['size_bytes']}</meta>
        <meta type="indexer">{release['indexer']}</meta>
        <meta type="series">{series_title}</meta>
        <meta type="season">{season}</meta>
        <meta type="episode">{episode}</meta>
    </head>
    <file poster="{release['indexer']}" date="{int(datetime.now().timestamp())}" subject="{release['title']}">
        <groups>
            <group>alt.binaries.tv</group>
        </groups>
        <segments>
            <segment bytes="{release['size_bytes']}" number="1">{release.get('guid', 'mock-guid')}</segment>
        </segments>
    </file>
</nzb>'''

            # Write NZB file
            os.makedirs(output_dir, exist_ok=True)
            with open(nzb_path, 'w', encoding='utf-8') as f:
                f.write(nzb_content)

            self.logger.info(f"✅ Created TV NZB file: {nzb_path}")
            return nzb_path

        except Exception as e:
            self.logger.error(f"Error creating TV NZB file: {e}")
            return None

    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Sonarr MCP service...")
        if self.session and not self.session.closed:
            await self.session.close()

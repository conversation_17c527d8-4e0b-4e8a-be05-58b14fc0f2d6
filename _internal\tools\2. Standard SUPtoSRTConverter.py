import os
import re
import subprocess
import sys

def convert_sup_to_srt(script_directory):
    """
    Recursively finds all .sup files within the script directory and its subdirectories
    and converts them to SRT using PGSRip.
    Skips conversion if the corresponding .srt file already exists.
    Logs the conversion process and reports any errors encountered.
    """
    # Compile a case-insensitive regex to match .sup files
    sup_file_regex = re.compile(r".+\.sup$", re.IGNORECASE)
    
    # Walk through the directory tree
    for root, directories, files in os.walk(script_directory):
        for filename in files:
            if sup_file_regex.match(filename):
                sup_filepath = os.path.join(root, filename)
                srt_filename = os.path.splitext(filename)[0] + ".srt"
                srt_filepath = os.path.join(root, srt_filename)
                
                # Check if the .srt file already exists
                if os.path.exists(srt_filepath):
                    print(f"Skipping conversion for '{sup_filepath}' as '{srt_filepath}' already exists.")
                    continue  # Skip to the next file
                
                print(f"\nConverting:\n  Input: {sup_filepath}\n  Output: {srt_filepath}")
                
                try:
                    # Run PGSRip using subprocess.run
                    # Command: pgsrip "input.sup" "output.srt"
                    result = subprocess.run(
                        ["pgsrip", filename, srt_filename],
                        cwd=root,  # Set the current working directory to the file's directory
                        capture_output=True,
                        text=True,
                        check=True  # Raise CalledProcessError for non-zero exit codes
                    )
                    
                    print("Conversion successful!")
                    if result.stdout:
                        print(f"PGSRip Output:\n{result.stdout}")
                    if result.stderr:
                        print(f"PGSRip Warnings/Errors:\n{result.stderr}")
                    
                except subprocess.CalledProcessError as e:
                    print(f"Error converting '{sup_filepath}':")
                    print(f"Return Code: {e.returncode}")
                    print(f"Output: {e.output}")
                    print(f"Error Output: {e.stderr}")
                except FileNotFoundError:
                    print("Error: PGSRip not found. Ensure PGSRip is installed and added to your system PATH.")
                    sys.exit(1)
                except Exception as e:
                    print(f"Unexpected error while converting '{sup_filepath}': {e}")

def main():
    """
    Main function to initiate the conversion process.
    Determines the script's directory and starts the conversion.
    """
    # Determine the directory where the script is located
    script_directory = os.path.dirname(os.path.abspath(__file__))
    
    print(f"Starting PGSRip conversion in directory: {script_directory}")
    
    convert_sup_to_srt(script_directory)
    
    print("\nAll conversions completed!")

if __name__ == "__main__":
    main()

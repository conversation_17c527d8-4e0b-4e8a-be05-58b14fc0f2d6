#!/usr/bin/env python3
"""
PlexMovieAutomator/src/utils/idempotent_helpers.py

Helper functions to make pipeline scripts idempotent (safe to run multiple times).
"""

import logging
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from .robust_state_manager import RobustStateManager

logger = logging.getLogger(__name__)

class IdempotentOperations:
    """Helper class for idempotent pipeline operations."""
    
    def __init__(self, state_manager: RobustStateManager):
        self.state_manager = state_manager
    
    def safe_organize_movie(self, movie_info: Dict) -> Tuple[bool, Dict]:
        """
        Safely organize a movie (idempotent).
        Returns (success, updated_movie_info)
        """
        try:
            download_folder = Path(movie_info['download_folder'])
            main_file = Path(movie_info['main_movie_file'])
            
            # Generate clean movie name
            clean_name = self._generate_clean_movie_name(movie_info)
            
            # Determine resolution and target directory
            resolution = self._detect_resolution(main_file)
            target_dir = self.state_manager.processing_output / resolution / clean_name
            
            # Check if already organized
            if target_dir.exists() and self.state_manager.is_stage_complete(target_dir, 'organized'):
                logger.info(f"Movie already organized: {clean_name}")
                return True, self._update_movie_info_for_organized(movie_info, target_dir)
            
            # Create target directory
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy main movie file
            target_file = target_dir / f"{clean_name}.mkv"
            if not target_file.exists():
                logger.info(f"Copying movie file: {main_file} -> {target_file}")
                shutil.copy2(main_file, target_file)
            
            # Set organized marker
            self.state_manager.set_stage_marker(target_dir, 'organized', {
                'source_folder': str(download_folder),
                'resolution': resolution,
                'clean_name': clean_name
            })
            
            # Clean up download folder (only if copy successful)
            if target_file.exists() and target_file.stat().st_size > 0:
                logger.info(f"Cleaning up download folder: {download_folder}")
                shutil.rmtree(download_folder, ignore_errors=True)
            
            # Update movie info
            updated_info = self._update_movie_info_for_organized(movie_info, target_dir)
            
            # Save metadata
            movie_id = self.state_manager.generate_movie_identifier(movie_info)
            self.state_manager.save_movie_metadata(movie_id, updated_info)
            
            logger.info(f"✅ Successfully organized: {clean_name}")
            return True, updated_info
            
        except Exception as e:
            logger.error(f"Failed to organize movie {movie_info.get('title', 'Unknown')}: {e}")
            return False, movie_info
    
    def safe_mkv_process(self, movie_info: Dict) -> Tuple[bool, Dict]:
        """
        Safely process MKV (idempotent).
        Returns (success, updated_movie_info)
        """
        try:
            movie_dir = Path(movie_info['movie_directory'])
            
            # Check if already processed
            if self.state_manager.is_stage_complete(movie_dir, 'mkv_complete'):
                logger.info(f"MKV already processed: {movie_dir.name}")
                return True, movie_info
            
            # Check if processing was interrupted
            if self.state_manager.is_stage_complete(movie_dir, 'mkv_processing'):
                logger.info(f"Resuming interrupted MKV processing: {movie_dir.name}")
                # Clear the processing marker to allow restart
                self.state_manager.clear_stage_marker(movie_dir, 'mkv_processing')
            
            # Set processing marker
            self.state_manager.set_stage_marker(movie_dir, 'mkv_processing')
            
            # Perform MKV processing (this would call the actual processing logic)
            success = self._perform_mkv_processing(movie_info)
            
            if success:
                # Clear processing marker and set complete marker
                self.state_manager.clear_stage_marker(movie_dir, 'mkv_processing')
                self.state_manager.set_stage_marker(movie_dir, 'mkv_complete')
                logger.info(f"✅ MKV processing complete: {movie_dir.name}")
            else:
                # Set error marker
                self.state_manager.set_stage_marker(movie_dir, 'error', {
                    'stage': 'mkv_processing',
                    'error': 'Processing failed'
                })
                logger.error(f"❌ MKV processing failed: {movie_dir.name}")
            
            return success, movie_info
            
        except Exception as e:
            logger.error(f"Failed to process MKV for {movie_info.get('title', 'Unknown')}: {e}")
            return False, movie_info
    
    def safe_subtitle_process(self, movie_info: Dict) -> Tuple[bool, Dict]:
        """
        Safely process subtitles (idempotent).
        Returns (success, updated_movie_info)
        """
        try:
            movie_dir = Path(movie_info['movie_directory'])
            
            # Check if already processed
            if self.state_manager.is_stage_complete(movie_dir, 'subtitle_complete'):
                logger.info(f"Subtitles already processed: {movie_dir.name}")
                return True, movie_info
            
            # Check if processing was interrupted
            if self.state_manager.is_stage_complete(movie_dir, 'subtitle_processing'):
                logger.info(f"Resuming interrupted subtitle processing: {movie_dir.name}")
                self.state_manager.clear_stage_marker(movie_dir, 'subtitle_processing')
            
            # Set processing marker
            self.state_manager.set_stage_marker(movie_dir, 'subtitle_processing')
            
            # Perform subtitle processing
            success = self._perform_subtitle_processing(movie_info)
            
            if success:
                # Clear processing marker and set complete marker
                self.state_manager.clear_stage_marker(movie_dir, 'subtitle_processing')
                self.state_manager.set_stage_marker(movie_dir, 'subtitle_complete')
                logger.info(f"✅ Subtitle processing complete: {movie_dir.name}")
            else:
                # Set error marker
                self.state_manager.set_stage_marker(movie_dir, 'error', {
                    'stage': 'subtitle_processing',
                    'error': 'Processing failed'
                })
                logger.error(f"❌ Subtitle processing failed: {movie_dir.name}")
            
            return success, movie_info
            
        except Exception as e:
            logger.error(f"Failed to process subtitles for {movie_info.get('title', 'Unknown')}: {e}")
            return False, movie_info
    
    def _generate_clean_movie_name(self, movie_info: Dict) -> str:
        """Generate clean movie name without TMDB ID."""
        title = movie_info.get('cleaned_title', movie_info.get('title', 'Unknown'))
        year = movie_info.get('year')
        
        # Sanitize title
        import re
        sanitized_title = re.sub(r'[<>:"/\\|?*]', '', title).strip()
        
        if year:
            return f"{sanitized_title} ({year})"
        else:
            return sanitized_title
    
    def _detect_resolution(self, movie_file: Path) -> str:
        """Detect movie resolution from file."""
        try:
            # Try to get resolution from filename first
            filename = movie_file.name.lower()
            if '2160p' in filename or '4k' in filename:
                return '4K'
            elif '1080p' in filename:
                return '1080p'
            elif '720p' in filename:
                return '720p'
            
            # TODO: Add actual video analysis using ffprobe if needed
            # For now, default to 1080p
            return '1080p'
            
        except Exception as e:
            logger.warning(f"Failed to detect resolution for {movie_file}: {e}")
            return 'SD_or_unknown'
    
    def _update_movie_info_for_organized(self, movie_info: Dict, target_dir: Path) -> Dict:
        """Update movie info after organization."""
        updated_info = movie_info.copy()
        updated_info.update({
            'movie_directory': str(target_dir),
            'main_movie_file': str(target_dir / f"{target_dir.name}.mkv"),
            'resolution': target_dir.parent.name,
            'stage': 'mkv_processing_pending'
        })
        return updated_info
    
    def _perform_mkv_processing(self, movie_info: Dict) -> bool:
        """Perform actual MKV processing with all enhancements."""
        try:
            movie_dir = Path(movie_info['movie_directory'])
            main_file = Path(movie_info['main_movie_file'])

            logger.info(f"Starting MKV processing for: {main_file}")

            # Load settings for MKV tools
            try:
                from _internal.utils.common_helpers import load_settings
            except ImportError:
                try:
                    from utils.common_helpers import load_settings
                except ImportError:
                    logger.error("Could not import settings loader")
                    return False

            settings = load_settings("_internal/config/settings.ini")

            # Import MKV processing functions
            try:
                from _internal.utils.mkv_utils import (
                    get_mkv_info,
                    clean_mkv_title_and_tags,
                    clean_attachments_iterative,
                    select_audio_track,
                    select_subtitle_tracks,
                    create_clean_video_audio_mkv,
                    extract_subtitle_tracks_individually
                )
            except ImportError:
                try:
                    from utils.mkv_utils import (
                        get_mkv_info,
                        clean_mkv_title_and_tags,
                        clean_attachments_iterative,
                        select_audio_track,
                        select_subtitle_tracks,
                        create_clean_video_audio_mkv,
                        extract_subtitle_tracks_individually
                    )
                except ImportError:
                    logger.error("Could not import MKV utilities")
                    return False

            # Create processing directories
            processed_va_dir = movie_dir / "_Processed_VideoAudio"
            processed_subs_dir = movie_dir / "_Processed_Subtitles"
            processed_audio_dir = movie_dir / "_Processed_Audio"

            for dir_path in [processed_va_dir, processed_subs_dir, processed_audio_dir]:
                dir_path.mkdir(exist_ok=True)

            # Step 1: Get MKV info
            logger.info("Analyzing MKV file...")
            mkv_info = get_mkv_info(str(main_file), settings)
            if not mkv_info:
                logger.error("Failed to get MKV info")
                return False

            # Step 2: Clean title and tags
            logger.info("Cleaning MKV title and tags...")
            clean_mkv_title_and_tags(str(main_file), settings)

            # Step 3: Clean attachments
            logger.info("Cleaning attachments...")
            clean_attachments_iterative(str(main_file), settings)

            # Step 4: Select best audio track (TrueHD Atmos preferred)
            logger.info("Selecting optimal audio track...")
            selected_audio = select_audio_track(mkv_info)
            if not selected_audio:
                logger.error("No suitable audio track found")
                return False

            # Step 5: Select English subtitle tracks only
            logger.info("Selecting English subtitle tracks...")
            selected_subtitles = select_subtitle_tracks(mkv_info, preferred_lang="eng", settings=settings)

            # Step 6: Create clean video+audio MKV
            clean_name = movie_dir.name
            output_va_file = processed_va_dir / f"{clean_name}.mkv"

            logger.info("Creating clean video+audio MKV...")
            # Extract track IDs for mkvmerge
            audio_track_id = selected_audio.get('id') if selected_audio else None

            success = create_clean_video_audio_mkv(
                str(main_file),
                str(output_va_file),
                video_track=None,  # Keep all video tracks
                audio_track=audio_track_id,
                settings=settings
            )

            if not success:
                logger.error("Failed to create clean video+audio MKV")
                return False

            # Step 7: Extract subtitle tracks individually
            if selected_subtitles:
                logger.info("Extracting subtitle tracks...")
                extract_subtitle_tracks_individually(
                    str(main_file),
                    str(processed_subs_dir),
                    selected_subtitles,
                    settings
                )

            # Step 8: Extract all audio tracks for manual review
            logger.info("Extracting audio tracks for manual review...")
            self._extract_all_audio_tracks(str(main_file), str(processed_audio_dir), mkv_info)

            # Step 9: Clean up original file
            logger.info("Cleaning up original file...")
            main_file.unlink()

            logger.info("✅ MKV processing completed successfully")
            return True

        except Exception as e:
            logger.error(f"MKV processing failed: {e}")
            return False
    
    def _extract_all_audio_tracks(self, input_file: str, output_dir: str, mkv_info: Dict):
        """Extract all audio tracks for manual review."""
        try:
            import subprocess

            audio_tracks = mkv_info.get('tracks', {}).get('audio', [])
            for track in audio_tracks:
                track_id = track.get('id')
                track_name = track.get('properties', {}).get('track_name', f'Track_{track_id}')
                codec = track.get('codec', 'unknown')
                language = track.get('properties', {}).get('language', 'und')

                # Sanitize filename
                import re
                safe_name = re.sub(r'[<>:"/\\|?*]', '_', track_name)
                output_file = Path(output_dir) / f"{safe_name}_{language}_{codec}.mka"

                # Extract track
                cmd = [
                    'mkvextract',
                    'tracks',
                    input_file,
                    f"{track_id}:{output_file}"
                ]

                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    logger.info(f"Extracted audio track: {output_file.name}")
                else:
                    logger.warning(f"Failed to extract audio track {track_id}: {result.stderr}")

        except Exception as e:
            logger.warning(f"Failed to extract audio tracks: {e}")

    def _perform_subtitle_processing(self, movie_info: Dict) -> bool:
        """Placeholder for actual subtitle processing logic."""
        # This would call the actual subtitle processing functions
        # For now, return True as placeholder
        logger.info("Performing subtitle processing...")
        return True


def create_idempotent_operations(state_manager: RobustStateManager) -> IdempotentOperations:
    """Factory function to create IdempotentOperations instance."""
    return IdempotentOperations(state_manager)

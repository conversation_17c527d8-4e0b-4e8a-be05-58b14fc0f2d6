#!/usr/bin/env python3
"""
PlexMovieAutomator/_internal/utils/ocr_utils.py

NVIDIA NGC Models OCR Pipeline for Subtitle Images
===================================================

Implements a research-based 4-step pipeline for converting image-based subtitles 
(Blu-ray .sup/.pgs files) into text (SRT subtitles) using NVIDIA NGC pre-trained models:

1. Extract Subtitle Images and Timings from .sup file (using BDSup2Sub → PNG + XML)
2. Preprocess Each Subtitle Image to maximize OCR accuracy 
3. Perform NGC Models OCR on each image (OCDNet v2.4 + OCRNet v2.1.1)
4. Assemble Recognized Text with timing data to output synchronized .srt file

This approach leverages state-of-the-art NVIDIA research models for superior accuracy.
"""

#!/usr/bin/env python3
"""
PlexMovieAutomator/mcp/main_pipeline_orchestrator.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the root directory (parent of mcp)
    root_dir = Path(__file__).parent.parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

# Setup paths for clean imports
sys.path.insert(0, str(Path(__file__).parent.parent / "_internal"))

"""
PlexMovieAutomator/mcp/main_pipeline_orchestrator.py

Main orchestrator script that coordinates all pipeline stages and integrates MCP servers
for enhanced automation, monitoring, and intelligent processing.

This script:
- Loads configuration and initializes MCP connections
- Coordinates the execution of all pipeline stages
- Provides intelligent error handling and retry mechanisms
- Tracks processing metrics and performance
- Supports both single-run and continuous operation modes
"""

import os
import sys
import asyncio
import argparse
import logging
import time
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

# Add the project root to Python path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import pipeline stages
from utils.common_helpers import (
    load_settings, setup_logging, read_state_file, write_state_file,
    update_movie_in_state, find_movie_in_state
)
from utils.metadata_apis import fetch_movie_metadata_for_intake
from utils.nzb_client_integrations import launch_nzb_with_os

# Import individual stage functions (using importlib for numbered modules)
import importlib.util

def import_stage_function(module_path, function_name):
    """Import a function from a module with a numeric name."""
    try:
        spec = importlib.util.spec_from_file_location("stage_module", module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return getattr(module, function_name)
    except Exception as e:
        print(f"Warning: Could not import {function_name} from {module_path}: {e}")
        return None

# Import stage functions
run_intake_and_search_stage = import_stage_function("01_intake_and_nzb_search.py", "run_intake_and_search_stage")
run_download_and_organize_stage = import_stage_function("02_download_and_organize.py", "run_download_and_organize_stage")
run_mkv_processor_stage = import_stage_function("03_mkv_processor.py", "run_mkv_processor_stage")
run_subtitle_handler_stage = import_stage_function("05_subtitle_handler.py", "run_subtitle_handler_stage")
run_final_mux_stage = import_stage_function("06_final_mux.py", "run_final_mux_stage")
run_poster_qc_prep_stage = import_stage_function("07_poster_and_qc_prep.py", "run_poster_qc_prep_stage")

# MCP Integration imports
from mcp.mcp_manager import MCPManager

class PipelineOrchestrator:
    """
    Main orchestrator class that manages the entire Plex Movie Automator pipeline
    with integrated MCP server capabilities.
    """
    
    def __init__(self, settings_path: str = "_internal/config/settings.ini"):
        self.settings_path = Path(settings_path)
        self.settings = None
        self.logger = None
        self.mcp_manager = None
        self.movies_data = []
        
        # Pipeline stage definitions
        self.pipeline_stages = [
            {
                "name": "01_intake_and_nzb_search",
                "function": run_intake_and_search_stage,
                "description": "Process new movie requests and search for NZBs",
                "async": True,
                "enabled": run_intake_and_search_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            },
            {
                "name": "02_download_and_organize",
                "function": run_download_and_organize_stage,
                "description": "Monitor downloads and organize completed files",
                "async": False,
                "enabled": run_download_and_organize_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            },
            {
                "name": "03_mkv_processor",
                "function": run_mkv_processor_stage,
                "description": "Process MKV files and extract subtitles",
                "async": True,
                "enabled": run_mkv_processor_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            },
            {
                "name": "05_subtitle_handler",
                "function": run_subtitle_handler_stage,
                "description": "Handle subtitle OCR and conversion",
                "async": True,
                "enabled": run_subtitle_handler_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            },
            {
                "name": "06_final_mux",
                "function": run_final_mux_stage,
                "description": "Mux final video with subtitles",
                "async": True,
                "enabled": run_final_mux_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            },
            {
                "name": "07_poster_and_qc_prep",
                "function": run_poster_qc_prep_stage,
                "description": "Download posters and prepare for QC",
                "async": True,
                "enabled": run_poster_qc_prep_stage is not None,
                "uses_sqlite": True  # This stage uses SQLite as source of truth
            }
        ]
        
        # Performance tracking
        self.stage_metrics = {}
        self.pipeline_start_time = None
        self.total_movies_processed = 0
        
    async def initialize(self):
        """Initialize the orchestrator with settings, logging, and MCP connections."""
        try:
            # Load settings
            self.settings = load_settings(self.settings_path)
            if not self.settings:
                raise ValueError(f"Failed to load settings from {self.settings_path}")
            
            # Setup logging
            self.logger = setup_logging(self.settings, "pipeline_orchestrator")
            self.logger.info("=== Plex Movie Automator Pipeline Orchestrator Starting ===")
            
            # Initialize MCP Manager
            self.mcp_manager = MCPManager(self.settings, self.logger)
            await self.mcp_manager.initialize()
            
            # Load current movie state
            self.movies_data = read_state_file(self.settings)
            self.logger.info(f"Loaded {len(self.movies_data)} movies from state file")
            
            # Initialize performance tracking
            self._initialize_metrics()
            
            self.logger.info("Pipeline orchestrator initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize orchestrator: {e}")
            else:
                print(f"Failed to initialize orchestrator: {e}")
            return False
    
    def _initialize_metrics(self):
        """Initialize performance tracking metrics."""
        for stage in self.pipeline_stages:
            self.stage_metrics[stage["name"]] = {
                "total_runs": 0,
                "successful_runs": 0,
                "failed_runs": 0,
                "total_runtime": 0.0,
                "average_runtime": 0.0,
                "last_run_time": None,
                "movies_processed": 0
            }
    
    async def run_single_cycle(self, specific_stages: List[str] = None, 
                              specific_movie_id: str = None,
                              force_reprocess: str = None) -> bool:
        """
        Run a single cycle of the pipeline.
        
        Args:
            specific_stages: List of stage names to run (if None, runs all stages)
            specific_movie_id: Process only this specific movie ID
            force_reprocess: Force reprocessing of this movie ID from specified stage
            
        Returns:
            bool: True if cycle completed successfully, False otherwise
        """
        try:
            self.pipeline_start_time = time.time()
            self.logger.info("=== Starting Pipeline Cycle ===")
            
            # Handle force reprocessing
            if force_reprocess:
                self._handle_force_reprocess(force_reprocess, specific_stages)
            
            # Filter movies if specific movie ID requested
            if specific_movie_id:
                movie = find_movie_in_state(specific_movie_id, self.movies_data)
                if not movie:
                    self.logger.error(f"Movie ID {specific_movie_id} not found in state")
                    return False
                self.logger.info(f"Processing specific movie: {movie.get('cleaned_title', 'Unknown')}")
            
            # Determine which stages to run
            stages_to_run = self._get_stages_to_run(specific_stages)
            
            # Execute each stage
            for stage in stages_to_run:
                success = await self._execute_stage(stage, specific_movie_id)
                if not success:
                    self.logger.error(f"Stage {stage['name']} failed, stopping pipeline cycle")
                    return False
                
                # Save state after each stage
                write_state_file(self.movies_data, self.settings)
            
            # Log cycle completion
            cycle_time = time.time() - self.pipeline_start_time
            self.logger.info(f"=== Pipeline Cycle Completed in {cycle_time:.2f} seconds ===")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Pipeline cycle failed: {e}")
            return False
    
    def _get_stages_to_run(self, specific_stages: List[str] = None) -> List[Dict]:
        """Get the list of stages to run based on parameters."""
        available_stages = [stage for stage in self.pipeline_stages if stage.get("enabled", True)]

        if specific_stages:
            return [stage for stage in available_stages if stage["name"] in specific_stages]
        return available_stages
    
    async def _execute_stage(self, stage: Dict, specific_movie_id: str = None) -> bool:
        """Execute a single pipeline stage with metrics tracking."""
        stage_name = stage["name"]
        stage_start_time = time.time()
        
        self.logger.info(f"--- Starting Stage: {stage_name} ---")
        self.logger.info(f"Description: {stage['description']}")
        
        try:
            # Update metrics
            self.stage_metrics[stage_name]["total_runs"] += 1
            self.stage_metrics[stage_name]["last_run_time"] = datetime.now(timezone.utc).isoformat()

            # Check if stage function is available
            if stage["function"] is None:
                self.logger.warning(f"Stage function for {stage_name} is not available, skipping...")
                return True

            # Execute the stage function
            if stage.get("uses_sqlite", False):
                # SQLite-based stage - doesn't use/return movies_data list
                if stage["async"]:
                    success = await stage["function"](self.movies_data, self.settings, self.logger)
                else:
                    success = stage["function"](self.movies_data, self.settings, self.logger)
                    
                if not success:
                    self.logger.error(f"Stage {stage_name} returned failure")
                    return False
                    
                # For SQLite stages, reload data from SQLite if available
                # This keeps the orchestrator's movies_data in sync for other stages
                try:
                    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
                    from pathlib import Path
                    
                    sqlite_manager = FilesystemFirstStateManager(Path.cwd())
                    all_movies = sqlite_manager.get_all_movies()
                    
                    # Convert SQLite format to legacy format for other stages
                    self.movies_data = []
                    for movie in all_movies:
                        legacy_movie = {
                            'unique_id': movie['unique_id'],
                            'title': movie['title'],
                            'cleaned_title': movie['title'],
                            'year': movie.get('year'),
                            'tmdb_id': movie.get('tmdb_id'),
                            'status': movie['status'],
                            'created_at': movie.get('created_at'),
                            'last_updated_timestamp': movie.get('last_updated'),
                            'paths': movie.get('paths', {}),
                            'metadata': movie.get('metadata', {}),
                            'metadata_details': movie.get('metadata', {}).get('metadata_details', {}),
                            'error_message': movie.get('error_message'),
                            'history': []  # History is now in separate table
                        }
                        self.movies_data.append(legacy_movie)
                    
                    sqlite_manager.close()
                    self.logger.info(f"Synchronized {len(self.movies_data)} movies from SQLite")
                    
                except Exception as e:
                    self.logger.warning(f"Could not sync movies_data from SQLite: {e}")
                    
            else:
                # Legacy stage - uses movies_data list
                if stage["async"]:
                    self.movies_data = await stage["function"](self.movies_data, self.settings, self.logger)
                else:
                    self.movies_data = stage["function"](self.movies_data, self.settings, self.logger)
            
            # Calculate runtime and update metrics
            stage_runtime = time.time() - stage_start_time
            self._update_stage_metrics(stage_name, stage_runtime, True)
            
            self.logger.info(f"--- Stage {stage_name} completed in {stage_runtime:.2f} seconds ---")
            return True
            
        except Exception as e:
            stage_runtime = time.time() - stage_start_time
            self._update_stage_metrics(stage_name, stage_runtime, False)
            
            self.logger.error(f"Stage {stage_name} failed after {stage_runtime:.2f} seconds: {e}")
            return False
    
    def _update_stage_metrics(self, stage_name: str, runtime: float, success: bool):
        """Update performance metrics for a stage."""
        metrics = self.stage_metrics[stage_name]
        
        if success:
            metrics["successful_runs"] += 1
        else:
            metrics["failed_runs"] += 1
        
        metrics["total_runtime"] += runtime
        metrics["average_runtime"] = metrics["total_runtime"] / metrics["total_runs"]
    
    def _handle_force_reprocess(self, movie_id: str, from_stages: List[str] = None):
        """Handle force reprocessing of a specific movie."""
        movie = find_movie_in_state(movie_id, self.movies_data)
        if not movie:
            self.logger.error(f"Cannot force reprocess: Movie ID {movie_id} not found")
            return
        
        # Determine the earliest stage to reset to
        if from_stages:
            # Reset to the first specified stage
            reset_stage = from_stages[0]
        else:
            # Default to MKV processing
            reset_stage = "03_mkv_processor"
        
        # Map stage names to status values
        stage_status_map = {
            "01_intake_and_nzb_search": "intake_pending",
            "02_download_and_organize": "nzb_download_pending", 
            "03_mkv_processor": "mkv_processing_pending",
            "05_subtitle_handler": "subtitle_ocr_pending",
            "06_final_mux": "final_mux_pending",
            "07_poster_and_qc_prep": "poster_pending"
        }
        
        new_status = stage_status_map.get(reset_stage, "mkv_processing_pending")
        
        update_movie_in_state(movie_id, {
            "status": new_status,
            "status_message": f"Force reprocessing from {reset_stage}",
            "force_reprocess_timestamp": datetime.now(timezone.utc).isoformat()
        }, self.movies_data)
        
        self.logger.info(f"Reset movie {movie.get('cleaned_title', 'Unknown')} to status: {new_status}")

    async def run_continuous(self, interval_seconds: int = None) -> None:
        """
        Run the pipeline continuously with specified interval.

        Args:
            interval_seconds: Time to wait between cycles (uses config default if None)
        """
        if interval_seconds is None:
            interval_seconds = int(self.settings.get("Orchestrator", "pipeline_interval_seconds", fallback=300))

        self.logger.info(f"Starting continuous pipeline mode with {interval_seconds}s intervals")

        try:
            while True:
                cycle_success = await self.run_single_cycle()

                if cycle_success:
                    self.logger.info(f"Cycle completed successfully. Waiting {interval_seconds} seconds...")
                else:
                    self.logger.warning(f"Cycle had errors. Waiting {interval_seconds} seconds before retry...")

                # Log current pipeline status
                self._log_pipeline_status()

                # Wait for next cycle
                await asyncio.sleep(interval_seconds)

        except KeyboardInterrupt:
            self.logger.info("Continuous mode interrupted by user")
        except Exception as e:
            self.logger.error(f"Continuous mode failed: {e}")

    def _log_pipeline_status(self):
        """Log current pipeline status and metrics."""
        # Count movies by status
        status_counts = {}
        for movie in self.movies_data:
            status = movie.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1

        self.logger.info("=== Pipeline Status Summary ===")
        for status, count in sorted(status_counts.items()):
            self.logger.info(f"  {status}: {count} movies")

        # Log stage performance
        self.logger.info("=== Stage Performance ===")
        for stage_name, metrics in self.stage_metrics.items():
            if metrics["total_runs"] > 0:
                success_rate = (metrics["successful_runs"] / metrics["total_runs"]) * 100
                self.logger.info(f"  {stage_name}: {success_rate:.1f}% success, "
                               f"avg runtime: {metrics['average_runtime']:.2f}s")

    def get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        total_movies = len(self.movies_data)

        # Count by status
        status_counts = {}
        error_movies = []
        completed_movies = []

        for movie in self.movies_data:
            status = movie.get("status", "unknown")
            status_counts[status] = status_counts.get(status, 0) + 1

            if "error" in status.lower():
                error_movies.append({
                    "id": movie.get("unique_id"),
                    "title": movie.get("cleaned_title", "Unknown"),
                    "status": status,
                    "error_message": movie.get("error_message", "No details")
                })
            elif status == "awaiting_quality_check":
                completed_movies.append({
                    "id": movie.get("unique_id"),
                    "title": movie.get("cleaned_title", "Unknown"),
                    "completed_time": movie.get("last_updated_timestamp")
                })

        return {
            "total_movies": total_movies,
            "status_breakdown": status_counts,
            "error_movies": error_movies,
            "completed_movies": completed_movies,
            "stage_metrics": self.stage_metrics,
            "pipeline_uptime": time.time() - self.pipeline_start_time if self.pipeline_start_time else 0
        }

    async def cleanup(self):
        """Cleanup resources before shutdown."""
        self.logger.info("Cleaning up pipeline orchestrator...")

        # Save final state
        if self.movies_data:
            write_state_file(self.movies_data, self.settings)
            self.logger.info("Final state saved")

        # Cleanup MCP connections
        if self.mcp_manager:
            await self.mcp_manager.cleanup()

        self.logger.info("Pipeline orchestrator cleanup completed")


async def main():
    """Main entry point for the pipeline orchestrator."""
    parser = argparse.ArgumentParser(description="Plex Movie Automator Pipeline Orchestrator")
    parser.add_argument("--run-mode", choices=["once", "continuous"], default="once",
                       help="Run mode: 'once' for single cycle, 'continuous' for ongoing operation")
    parser.add_argument("--stages", type=str,
                       help="Comma-separated list of specific stages to run (e.g., '03_mkv_processor,05_subtitle_handler')")
    parser.add_argument("--movie-id", type=str,
                       help="Process only the specified movie ID")
    parser.add_argument("--force-reprocess", type=str,
                       help="Force reprocessing of the specified movie ID")
    parser.add_argument("--interval", type=int,
                       help="Interval in seconds for continuous mode (overrides config)")
    parser.add_argument("--config", type=str, default="_internal/config/settings.ini",
                       help="Path to configuration file")
    parser.add_argument("--debug", action="store_true",
                       help="Enable debug logging")

    args = parser.parse_args()

    # Parse stages if provided
    specific_stages = None
    if args.stages:
        specific_stages = [stage.strip() for stage in args.stages.split(",")]

    # Initialize orchestrator
    orchestrator = PipelineOrchestrator(args.config)

    try:
        # Initialize the orchestrator
        if not await orchestrator.initialize():
            print("Failed to initialize pipeline orchestrator")
            return 1

        # Set debug logging if requested
        if args.debug:
            logging.getLogger().setLevel(logging.DEBUG)
            orchestrator.logger.info("Debug logging enabled")

        # Run based on mode
        if args.run_mode == "once":
            success = await orchestrator.run_single_cycle(
                specific_stages=specific_stages,
                specific_movie_id=args.movie_id,
                force_reprocess=args.force_reprocess
            )

            # Print final statistics
            stats = orchestrator.get_pipeline_statistics()
            orchestrator.logger.info("=== Final Pipeline Statistics ===")
            orchestrator.logger.info(f"Total movies: {stats['total_movies']}")
            orchestrator.logger.info(f"Status breakdown: {stats['status_breakdown']}")

            return 0 if success else 1

        elif args.run_mode == "continuous":
            await orchestrator.run_continuous(args.interval)
            return 0

    except KeyboardInterrupt:
        orchestrator.logger.info("Pipeline interrupted by user")
        return 0
    except Exception as e:
        if orchestrator.logger:
            orchestrator.logger.error(f"Pipeline failed: {e}")
        else:
            print(f"Pipeline failed: {e}")
        return 1
    finally:
        await orchestrator.cleanup()


if __name__ == "__main__":
    # Run the async main function
    exit_code = asyncio.run(main())
    sys.exit(exit_code)

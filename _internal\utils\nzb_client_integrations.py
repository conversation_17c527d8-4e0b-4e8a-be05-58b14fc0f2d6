#!/usr/bin/env python3
"""
PlexMovieAutomator/src/utils/nzb_client_integrations.py

Utility functions and classes to interact with various NZB download clients.
"""
import os
import logging
import time
import requests
import json
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

def launch_nzb_with_os(nzb_file_path: str, client_exe_path: str = None) -> bool:
    """
    Launches an NZB file using the OS's default associated application.
    This is the method used by your original '3. Download_Movies.py'.

    Args:
        nzb_file_path (str): The full path to the .nzb file.
        client_exe_path (str): Optional. Path to the client executable (currently not used by os.startfile).

    Returns:
        bool: True if the launch command was issued without error, False otherwise.
    """
    if not os.path.exists(nzb_file_path):
        logger.error(f"Cannot launch NZB: File not found at '{nzb_file_path}'")
        return False

    try:
        logger.info(f"Using os.startfile to launch NZB: {nzb_file_path}")
        os.startfile(nzb_file_path)
        # Give the OS a moment to process the request
        time.sleep(2)
        return True
    except AttributeError:
        logger.error("os.startfile() is not available on this OS (it's Windows-specific). Use an API-based client.")
        return False
    except Exception as e:
        logger.error(f"Failed to launch '{nzb_file_path}' using os.startfile: {e}")
        return False

# --- API-based clients ---

class SabnzbdClient:
    """
    Full SABnzbd API client for downloading NZBs and monitoring progress.
    """

    def __init__(self, api_url: str, api_key: str):
        """
        Initialize SABnzbd client.

        Args:
            api_url: SABnzbd base URL (e.g., http://127.0.0.1:8080)
            api_key: SABnzbd API key
        """
        self.api_url = api_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        logger.info(f"SABnzbd Client initialized for {self.api_url}")

    def _make_api_request(self, mode: str, **params) -> Optional[Dict[Any, Any]]:
        """
        Make a request to the SABnzbd API.

        Args:
            mode: SABnzbd API mode (e.g., 'addfile', 'queue', 'history')
            **params: Additional parameters for the API call

        Returns:
            JSON response as dict, or None if failed
        """
        try:
            api_params = {
                'mode': mode,
                'apikey': self.api_key,
                'output': 'json',
                **params
            }

            response = self.session.get(f"{self.api_url}/api", params=api_params, timeout=30)
            response.raise_for_status()

            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"SABnzbd API request failed: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse SABnzbd API response: {e}")
            return None

    def test_connection(self) -> bool:
        """
        Test connection to SABnzbd API.

        Returns:
            True if connection successful, False otherwise
        """
        try:
            result = self._make_api_request('version')
            if result and 'version' in result:
                logger.info(f"SABnzbd connection successful. Version: {result['version']}")
                return True
            else:
                logger.error("SABnzbd connection failed: Invalid response")
                return False
        except Exception as e:
            logger.error(f"SABnzbd connection test failed: {e}")
            return False

    def add_nzb_file(self, nzb_file_path: str, movie_title: str = None, movie_year: str = None) -> Optional[str]:
        """
        Add an NZB file to SABnzbd queue.

        Args:
            nzb_file_path: Path to the NZB file
            movie_title: Optional movie title for naming
            movie_year: Optional movie year for naming

        Returns:
            NZO ID if successful, None if failed
        """
        if not os.path.exists(nzb_file_path):
            logger.error(f"NZB file not found: {nzb_file_path}")
            return None

        try:
            # Create a descriptive name
            if movie_title and movie_year:
                nzb_name = f"{movie_title} ({movie_year})"
            else:
                nzb_name = Path(nzb_file_path).stem

            # Upload the NZB file
            with open(nzb_file_path, 'rb') as nzb_file:
                files = {'nzbfile': nzb_file}
                data = {
                    'mode': 'addfile',
                    'apikey': self.api_key,
                    'output': 'json',
                    'cat': 'movies',  # Use movies category (configured with our script)
                    'nzbname': nzb_name
                }

                response = self.session.post(f"{self.api_url}/api", files=files, data=data, timeout=60)
                response.raise_for_status()

                result = response.json()

                if result.get('status'):
                    nzo_ids = result.get('nzo_ids', [])
                    if nzo_ids:
                        nzo_id = nzo_ids[0]
                        logger.info(f"Successfully added '{nzb_name}' to SABnzbd queue: {nzo_id}")
                        return nzo_id
                    else:
                        logger.error(f"SABnzbd returned success but no NZO ID: {result}")
                        return None
                else:
                    logger.error(f"SABnzbd failed to add NZB: {result}")
                    return None

        except Exception as e:
            logger.error(f"Error adding NZB to SABnzbd: {e}")
            return None

    def add_nzb_url(self, nzb_url: str, movie_title: str = None, movie_year: str = None) -> Optional[str]:
        """
        Add an NZB URL to SABnzbd queue.

        Args:
            nzb_url: URL to the NZB file
            movie_title: Optional movie title for naming
            movie_year: Optional movie year for naming

        Returns:
            NZO ID if successful, None if failed
        """
        try:
            # Create a descriptive name
            if movie_title and movie_year:
                nzb_name = f"{movie_title} ({movie_year})"
            else:
                nzb_name = "Movie Download"

            result = self._make_api_request(
                'addurl',
                name=nzb_url,
                nzbname=nzb_name,
                cat='movies'  # Use movies category (configured with our script)
            )

            if result and result.get('status'):
                nzo_ids = result.get('nzo_ids', [])
                if nzo_ids:
                    nzo_id = nzo_ids[0]
                    logger.info(f"Successfully added '{nzb_name}' to SABnzbd queue: {nzo_id}")
                    return nzo_id
                else:
                    logger.error(f"SABnzbd returned success but no NZO ID: {result}")
                    return None
            else:
                logger.error(f"SABnzbd failed to add NZB URL: {result}")
                return None

        except Exception as e:
            logger.error(f"Error adding NZB URL to SABnzbd: {e}")
            return None

    def get_job_status(self, nzo_id: str) -> Optional[Dict[str, Any]]:
        """
        Get status of a specific job in SABnzbd.

        Args:
            nzo_id: The NZO ID returned when adding the job

        Returns:
            Dict with job status info, or None if not found
        """
        try:
            # First check the queue
            queue_result = self._make_api_request('queue')
            if queue_result and 'queue' in queue_result:
                for slot in queue_result['queue'].get('slots', []):
                    if slot.get('nzo_id') == nzo_id:
                        return {
                            'status': 'downloading',
                            'name': slot.get('filename', 'Unknown'),
                            'progress': float(slot.get('percentage', 0)),
                            'size_total': slot.get('size', 'Unknown'),
                            'size_left': slot.get('sizeleft', 'Unknown'),
                            'eta': slot.get('timeleft', 'Unknown'),
                            'speed': slot.get('speed', 'Unknown')
                        }

            # If not in queue, check history
            history_result = self._make_api_request('history')
            if history_result and 'history' in history_result:
                for slot in history_result['history'].get('slots', []):
                    if slot.get('nzo_id') == nzo_id:
                        status_map = {
                            'Completed': 'completed',
                            'Failed': 'failed',
                            'Repair': 'repairing',
                            'Unpack': 'unpacking'
                        }
                        sab_status = slot.get('status', 'Unknown')
                        return {
                            'status': status_map.get(sab_status, sab_status.lower()),
                            'name': slot.get('name', 'Unknown'),
                            'progress': 100 if sab_status == 'Completed' else 0,
                            'download_path': slot.get('storage', 'Unknown'),
                            'fail_msg': slot.get('fail_msg', '')
                        }

            logger.warning(f"Job {nzo_id} not found in queue or history")
            return None

        except Exception as e:
            logger.error(f"Error getting job status from SABnzbd: {e}")
            return None

    def get_queue_status(self) -> Optional[Dict[str, Any]]:
        """
        Get overall queue status from SABnzbd.

        Returns:
            Dict with queue info, or None if failed
        """
        try:
            result = self._make_api_request('queue')
            if result and 'queue' in result:
                queue = result['queue']
                return {
                    'paused': queue.get('paused', False),
                    'speed': queue.get('speed', '0'),
                    'size_left': queue.get('sizeleft', '0'),
                    'eta': queue.get('timeleft', '0:00:00'),
                    'active_jobs': len(queue.get('slots', [])),
                    'total_size': queue.get('size', '0')
                }
            return None

        except Exception as e:
            logger.error(f"Error getting queue status from SABnzbd: {e}")
            return None

    def pause_queue(self) -> bool:
        """Pause the SABnzbd queue."""
        try:
            result = self._make_api_request('pause')
            return result is not None
        except Exception as e:
            logger.error(f"Error pausing SABnzbd queue: {e}")
            return False

    def resume_queue(self) -> bool:
        """Resume the SABnzbd queue."""
        try:
            result = self._make_api_request('resume')
            return result is not None
        except Exception as e:
            logger.error(f"Error resuming SABnzbd queue: {e}")
            return False

    def delete_job(self, nzo_id: str) -> bool:
        """
        Delete a job from SABnzbd queue.

        Args:
            nzo_id: The NZO ID of the job to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            result = self._make_api_request('queue', name='delete', value=nzo_id)
            return result is not None
        except Exception as e:
            logger.error(f"Error deleting job {nzo_id} from SABnzbd: {e}")
            return False


def create_sabnzbd_client(settings_dict: dict) -> Optional[SabnzbdClient]:
    """
    Create and test a SABnzbd client from settings.

    Args:
        settings_dict: Settings dictionary containing SABnzbd configuration

    Returns:
        SabnzbdClient instance if successful, None if failed
    """
    try:
        from utils.common_helpers import get_setting

        sabnzbd_url = get_setting("DownloadAndOrganize", "sabnzbd_url", settings_dict=settings_dict)
        sabnzbd_api_key = get_setting("DownloadAndOrganize", "sabnzbd_api_key", settings_dict=settings_dict)

        if not sabnzbd_url or not sabnzbd_api_key:
            logger.error("SABnzbd URL or API key not configured in settings")
            return None

        client = SabnzbdClient(sabnzbd_url, sabnzbd_api_key)

        # Test the connection
        if client.test_connection():
            logger.info("SABnzbd client created and tested successfully")
            return client
        else:
            logger.error("SABnzbd client connection test failed")
            return None

    except Exception as e:
        logger.error(f"Error creating SABnzbd client: {e}")
        return None
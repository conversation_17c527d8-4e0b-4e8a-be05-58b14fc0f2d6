#!/usr/bin/env python3
"""
Step 5: NGC OCR Integration Testing and Validation
Test the complete NGC OCR pipeline with TensorRT engines
"""

import sys
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional

# Add tools directory to path
sys.path.append(str(Path(__file__).parent))

def setup_logging():
    """Setup logging for testing"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_model_availability():
    """Test if NGC OCR models are available"""
    print("=== Testing Model Availability ===")
    
    models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    
    # Check ONNX models
    ocdnet_onnx = models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    ocrnet_onnx = models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    
    print(f"OCDNet ONNX: {'✅' if ocdnet_onnx.exists() else '❌'}")
    print(f"OCRNet ONNX: {'✅' if ocrnet_onnx.exists() else '❌'}")
    
    # Check TensorRT engines
    ocdnet_trt = models_dir / "tensorrt" / "ocdnet_v2.4.trt"
    ocrnet_trt = models_dir / "tensorrt" / "ocrnet_v2.1.1.trt"
    
    print(f"OCDNet TensorRT: {'✅' if ocdnet_trt.exists() else '❌'}")
    print(f"OCRNet TensorRT: {'✅' if ocrnet_trt.exists() else '❌'}")
    
    # Show file sizes
    if ocdnet_trt.exists():
        size_mb = ocdnet_trt.stat().st_size / (1024 * 1024)
        print(f"  OCDNet engine: {size_mb:.1f} MB")
    
    if ocrnet_trt.exists():
        size_mb = ocrnet_trt.stat().st_size / (1024 * 1024)
        print(f"  OCRNet engine: {size_mb:.1f} MB")
    
    return {
        'onnx_available': ocdnet_onnx.exists() and ocrnet_onnx.exists(),
        'tensorrt_available': ocdnet_trt.exists() and ocrnet_trt.exists(),
        'ocdnet_trt': ocdnet_trt.exists(),
        'ocrnet_trt': ocrnet_trt.exists()
    }

def test_pipeline_integrator():
    """Test the pipeline integrator functionality"""
    print("\n=== Testing Pipeline Integrator ===")
    
    try:
        from pipeline_integrator import (
            PipelineIntegrator, 
            check_ngc_ocr_available, 
            check_tensorrt_engines_available,
            integrate_mkv_processor,
            integrate_subtitle_handler
        )
        
        print("✅ Pipeline integrator imports successful")
        
        # Test availability functions
        ngc_available = check_ngc_ocr_available()
        trt_available = check_tensorrt_engines_available()
        
        print(f"NGC OCR Available: {'✅' if ngc_available else '❌'}")
        print(f"TensorRT Engines Available: {'✅' if trt_available else '❌'}")
        
        # Test integrator initialization
        integrator = PipelineIntegrator()
        print("✅ PipelineIntegrator initialization successful")
        
        # Test statistics
        stats = integrator.get_enhancement_statistics()
        print(f"Enhancement Statistics: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Pipeline integrator test failed: {e}")
        return False

def test_subtitle_quality_assessor():
    """Test the subtitle quality assessor"""
    print("\n=== Testing Subtitle Quality Assessor ===")
    
    try:
        from subtitle_quality_assessor import (
            SubtitleQualityAssessor,
            QualityMetrics,
            assess_subtitle_quality
        )
        
        print("✅ Subtitle quality assessor imports successful")
        
        # Test assessor initialization
        assessor = SubtitleQualityAssessor()
        print("✅ SubtitleQualityAssessor initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Subtitle quality assessor test failed: {e}")
        return False

def test_text_detection():
    """Test the text detection module"""
    print("\n=== Testing Text Detection Module ===")
    
    try:
        from step3_text_detection import NGC_TextDetector, SubtitleRegion
        
        print("✅ Text detection imports successful")
        
        # Test detector initialization
        models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
        ocdnet_onnx = models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
        detector = NGC_TextDetector(model_path=str(ocdnet_onnx))
        print("✅ NGC_TextDetector initialization successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Text detection test failed: {e}")
        return False

def test_tensorrt_engines():
    """Test TensorRT engine loading"""
    print("\n=== Testing TensorRT Engine Loading ===")
    
    models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    ocdnet_trt = models_dir / "tensorrt" / "ocdnet_v2.4.trt"
    ocrnet_trt = models_dir / "tensorrt" / "ocrnet_v2.1.1.trt"
    
    if not ocdnet_trt.exists():
        print("❌ OCDNet TensorRT engine not found")
        return False
    
    if not ocrnet_trt.exists():
        print("❌ OCRNet TensorRT engine not found")
        return False
    
    try:
        # Test basic TensorRT imports
        import tensorrt as trt
        print("✅ TensorRT import successful")
        
        # Test engine loading (basic validation)
        logger = trt.Logger(trt.Logger.WARNING)
        runtime = trt.Runtime(logger)
        
        with open(ocdnet_trt, 'rb') as f:
            engine_data = f.read()
            
        engine = runtime.deserialize_cuda_engine(engine_data)
        if engine:
            print("✅ OCDNet TensorRT engine loads successfully")
            print(f"  Engine has {engine.num_bindings} bindings")
            engine.__del__()
        else:
            print("❌ OCDNet TensorRT engine failed to load")
            return False
        
        # Test OCRNet engine
        with open(ocrnet_trt, 'rb') as f:
            engine_data = f.read()
            
        engine = runtime.deserialize_cuda_engine(engine_data)
        if engine:
            print("✅ OCRNet TensorRT engine loads successfully")
            print(f"  Engine has {engine.num_bindings} bindings")
            engine.__del__()
        else:
            print("❌ OCRNet TensorRT engine failed to load")
            return False
        
        return True
        
    except ImportError:
        print("❌ TensorRT Python bindings not available")
        return False
    except Exception as e:
        print(f"❌ TensorRT engine loading failed: {e}")
        return False

def test_integration_points():
    """Test integration points with existing pipeline"""
    print("\n=== Testing Integration Points ===")
    
    try:
        from pipeline_integrator import integrate_mkv_processor, integrate_subtitle_handler
        
        # Test MKV processor integration with sample data
        sample_tracks = [
            {'id': 0, 'codec': 'pgs', 'language': 'en', 'title': 'English'},
            {'id': 1, 'codec': 'srt', 'language': 'ja', 'title': 'Japanese'},
            {'id': 2, 'codec': 'pgs', 'language': 'en', 'title': 'Forced'},
        ]
        
        enhanced_tracks = integrate_mkv_processor("test.mkv", sample_tracks)
        print("✅ MKV processor integration successful")
        print(f"  Enhanced {len(enhanced_tracks)} tracks")
        
        # Check for NGC OCR candidate flags
        candidates = [t for t in enhanced_tracks if t.get('ngc_ocr_candidate', False)]
        print(f"  {len(candidates)} tracks flagged for NGC OCR")
        
        # Test subtitle handler integration (mock data)
        sample_bdsup2sub_result = ["Sample subtitle line 1", "Sample subtitle line 2"]
        sample_log = "BDSup2Sub processing log..."
        
        enhanced_result, metadata = integrate_subtitle_handler(
            "test.mkv", sample_tracks[0], sample_bdsup2sub_result, sample_log
        )
        
        print("✅ Subtitle handler integration successful")
        print(f"  Method: {metadata.get('method', 'unknown')}")
        print(f"  Enhanced: {metadata.get('enhanced', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration points test failed: {e}")
        return False

def generate_test_report(results: Dict):
    """Generate a comprehensive test report"""
    print("\n" + "="*60)
    print("NGC OCR INTEGRATION TEST REPORT")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\nDetailed Results:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
    
    # Overall status
    if passed_tests == total_tests:
        print("\n🚀 ALL TESTS PASSED - NGC OCR INTEGRATION READY!")
    elif passed_tests >= total_tests * 0.8:
        print("\n⚡ MOSTLY READY - Minor issues to resolve")
    else:
        print("\n❌ INTEGRATION NOT READY - Major issues found")
    
    return passed_tests == total_tests

def main():
    """Run all tests and generate report"""
    logger = setup_logging()
    
    print("NGC OCR Integration Testing Suite")
    print("="*60)
    
    # Run all tests
    test_results = {}
    
    model_status = test_model_availability()
    test_results['Model Availability'] = model_status['onnx_available'] or model_status['tensorrt_available']
    
    test_results['Pipeline Integrator'] = test_pipeline_integrator()
    test_results['Subtitle Quality Assessor'] = test_subtitle_quality_assessor()
    test_results['Text Detection Module'] = test_text_detection()
    
    if model_status['tensorrt_available']:
        test_results['TensorRT Engine Loading'] = test_tensorrt_engines()
    else:
        print("\n⚠️  Skipping TensorRT engine tests - engines not available")
        test_results['TensorRT Engine Loading'] = False
    
    test_results['Integration Points'] = test_integration_points()
    
    # Generate final report
    all_passed = generate_test_report(test_results)
    
    # Next steps recommendations
    print("\nNext Steps:")
    if not model_status['tensorrt_available']:
        print("1. Complete TensorRT model conversions for maximum performance")
    if not test_results.get('TensorRT Engine Loading', False):
        print("2. Validate TensorRT engine functionality")
    if all_passed:
        print("1. Begin production integration with existing pipeline")
        print("2. Test with real subtitle files")
        print("3. Performance benchmarking on RTX 5090")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

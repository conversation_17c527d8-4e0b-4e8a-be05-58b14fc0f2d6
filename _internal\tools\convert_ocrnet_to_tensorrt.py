#!/usr/bin/env python3
"""
Step 4b: Convert OCRNet to TensorRT Engine
Optimized for RTX 5090 maximum performance
"""

import subprocess
import sys
import time
from pathlib import Path

def run_ocrnet_conversion():
    """Convert OCRNet ONNX model to TensorRT engine"""
    
    print("=== OCRNet TensorRT Conversion ===")
    
    # Paths
    workspace_root = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator")
    trtexec_path = workspace_root / "_internal" / "tools" / "TensorRT" / "bin" / "trtexec.exe"
    lib_path = workspace_root / "_internal" / "tools" / "TensorRT" / "lib"
    
    onnx_model = workspace_root / "_internal" / "models" / "ngc_ocr" / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    trt_engine = workspace_root / "_internal" / "models" / "ngc_ocr" / "tensorrt" / "ocrnet_v2.1.1.trt"
    
    # Verify paths exist
    if not trtexec_path.exists():
        print(f"ERROR: trtexec not found at {trtexec_path}")
        return False
        
    if not onnx_model.exists():
        print(f"ERROR: OCRNet ONNX model not found at {onnx_model}")
        return False
    
    # Ensure output directory exists
    trt_engine.parent.mkdir(parents=True, exist_ok=True)
    
    # Build conversion command
    cmd = [
        str(trtexec_path),
        f"--onnx={onnx_model}",
        f"--saveEngine={trt_engine}",
        "--fp16",
        "--memPoolSize=workspace:4096M",
        "--builderOptimizationLevel=5",
        "--hardwareCompatibilityLevel=ampere+",
        "--verbose"
    ]
    
    print(f"Converting: {onnx_model.name}")
    print(f"Output: {trt_engine}")
    print(f"Command: {' '.join(cmd)}")
    print()
    
    # Set environment to include TensorRT libraries
    import os
    env = os.environ.copy()
    current_path = env.get('PATH', '')
    env['PATH'] = f"{current_path};{lib_path}"
    
    try:
        # Run conversion
        start_time = time.time()
        result = subprocess.run(cmd, env=env, capture_output=True, text=True, cwd=workspace_root)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if result.returncode == 0:
            print("✅ OCRNet TensorRT conversion successful!")
            print(f"Duration: {duration:.1f} seconds")
            
            # Check output file
            if trt_engine.exists():
                size_mb = trt_engine.stat().st_size / (1024 * 1024)
                print(f"Engine size: {size_mb:.1f} MB")
                return True
            else:
                print("ERROR: Engine file not created despite successful exit code")
                return False
        else:
            print("❌ OCRNet TensorRT conversion failed!")
            print(f"Exit code: {result.returncode}")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"ERROR: Exception during conversion: {e}")
        return False

if __name__ == "__main__":
    success = run_ocrnet_conversion()
    if success:
        print("\n🚀 OCRNet engine ready for maximum performance!")
    else:
        print("\n💥 OCRNet conversion failed - check logs above")
    
    sys.exit(0 if success else 1)

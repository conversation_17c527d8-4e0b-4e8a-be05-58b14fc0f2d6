#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/download_monitor.py

Download Monitor MCP Service
Provides real-time monitoring of downloads across Radarr, Sonarr, and SABnzbd.
Tracks progress, estimates completion times, and handles failures.
"""

import logging
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

@dataclass
class DownloadItem:
    """Represents a download item being monitored."""
    id: str
    title: str
    type: str  # 'movie', 'episode', 'season'
    status: str  # 'downloading', 'paused', 'completed', 'failed'
    progress: float  # 0.0 to 1.0
    size_total: int  # bytes
    size_remaining: int  # bytes
    speed: int  # bytes per second
    eta: Optional[str]  # estimated time remaining
    source: str  # 'radarr', 'sonarr', 'sabnzbd'
    quality: str
    indexer: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None

class DownloadMonitorMCP:
    """
    MCP service for monitoring downloads across the *arr stack.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager
        
        # Active downloads tracking
        self.active_downloads: Dict[str, DownloadItem] = {}
        self.completed_downloads: List[DownloadItem] = []
        self.failed_downloads: List[DownloadItem] = []
        
        # Monitoring settings
        self.monitor_interval = config.get('download_monitor', {}).get('interval', 30)  # seconds
        self.max_history = config.get('download_monitor', {}).get('max_history', 100)
        
        # Services
        self.radarr_service = None
        self.sonarr_service = None
        self.sabnzbd_session = None
        
        # SABnzbd configuration
        self.sabnzbd_url = config.get('sabnzbd', {}).get('base_url', 'http://localhost:8080')
        self.sabnzbd_api_key = config.get('sabnzbd', {}).get('api_key', '')
        
        # Monitoring task
        self.monitor_task = None
        self.is_monitoring = False
        
    async def initialize(self) -> bool:
        """Initialize the Download Monitor MCP service."""
        try:
            self.logger.info("Initializing Download Monitor MCP service...")
            
            # Get references to other services
            if self.mcp_manager:
                self.radarr_service = self.mcp_manager.get_service('radarr_integration')
                self.sonarr_service = self.mcp_manager.get_service('sonarr_integration')
            
            # Create SABnzbd session
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {'Accept': 'application/json'}
            
            self.sabnzbd_session = aiohttp.ClientSession(
                timeout=timeout,
                headers=headers,
                connector=aiohttp.TCPConnector(ssl=False)
            )
            
            # Test SABnzbd connection
            if await self.test_sabnzbd_connection():
                self.logger.info("Download Monitor MCP service initialized successfully")
                return True
            else:
                self.logger.warning("SABnzbd connection failed, monitoring will be limited")
                return True  # Still initialize, just with limited functionality
                
        except Exception as e:
            self.logger.error(f"Failed to initialize Download Monitor MCP: {e}")
            return False
    
    async def test_sabnzbd_connection(self) -> bool:
        """Test connection to SABnzbd API."""
        try:
            url = f"{self.sabnzbd_url}/api"
            params = {
                'mode': 'version',
                'apikey': self.sabnzbd_api_key,
                'output': 'json'
            }
            
            async with self.sabnzbd_session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    version = data.get('version', 'Unknown')
                    self.logger.info(f"Connected to SABnzbd v{version}")
                    return True
                else:
                    self.logger.error(f"SABnzbd API returned status {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"Error testing SABnzbd connection: {e}")
            return False
    
    async def start_monitoring(self):
        """Start the download monitoring loop."""
        if self.is_monitoring:
            self.logger.warning("Download monitoring is already running")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        self.logger.info(f"Started download monitoring (interval: {self.monitor_interval}s)")
    
    async def stop_monitoring(self):
        """Stop the download monitoring loop."""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Stopped download monitoring")
    
    async def _monitoring_loop(self):
        """Main monitoring loop that runs continuously."""
        while self.is_monitoring:
            try:
                await self._update_download_status()
                await asyncio.sleep(self.monitor_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.monitor_interval)
    
    async def _update_download_status(self):
        """Update status of all active downloads."""
        try:
            # Get downloads from SABnzbd
            sabnzbd_downloads = await self._get_sabnzbd_queue()
            
            # Get downloads from Radarr
            radarr_downloads = []
            if self.radarr_service:
                radarr_downloads = await self.radarr_service.get_download_queue()
            
            # Get downloads from Sonarr
            sonarr_downloads = []
            if self.sonarr_service:
                sonarr_downloads = await self.sonarr_service.get_series_queue()
            
            # Update active downloads
            await self._process_download_updates(sabnzbd_downloads, radarr_downloads, sonarr_downloads)
            
        except Exception as e:
            self.logger.error(f"Error updating download status: {e}")
    
    async def _get_sabnzbd_queue(self) -> List[Dict[str, Any]]:
        """Get current download queue from SABnzbd."""
        try:
            url = f"{self.sabnzbd_url}/api"
            params = {
                'mode': 'queue',
                'apikey': self.sabnzbd_api_key,
                'output': 'json'
            }
            
            async with self.sabnzbd_session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    queue = data.get('queue', {})
                    slots = queue.get('slots', [])
                    
                    downloads = []
                    for slot in slots:
                        downloads.append({
                            'id': slot.get('nzo_id', ''),
                            'title': slot.get('filename', ''),
                            'status': slot.get('status', '').lower(),
                            'progress': float(slot.get('percentage', 0)) / 100.0,
                            'size_total': int(slot.get('mb', 0)) * 1024 * 1024,
                            'size_remaining': int(slot.get('mbleft', 0)) * 1024 * 1024,
                            'speed': int(slot.get('kbpersec', 0)) * 1024,
                            'eta': slot.get('timeleft', ''),
                            'category': slot.get('cat', ''),
                            'priority': slot.get('priority', '')
                        })
                    
                    return downloads
                else:
                    self.logger.error(f"SABnzbd queue request failed: HTTP {response.status}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"Error getting SABnzbd queue: {e}")
            return []
    
    async def _process_download_updates(self, sabnzbd_downloads, radarr_downloads, sonarr_downloads):
        """Process download updates from all sources."""
        current_time = datetime.now()
        
        # Process SABnzbd downloads
        for download in sabnzbd_downloads:
            download_id = f"sabnzbd_{download['id']}"
            
            if download_id in self.active_downloads:
                # Update existing download
                item = self.active_downloads[download_id]
                item.status = self._normalize_status(download['status'])
                item.progress = download['progress']
                item.size_remaining = download['size_remaining']
                item.speed = download['speed']
                item.eta = download['eta']
                
                # Check if completed
                if item.progress >= 1.0 or item.status == 'completed':
                    item.completed_at = current_time
                    item.status = 'completed'
                    self.completed_downloads.append(item)
                    del self.active_downloads[download_id]
                    self.logger.info(f"Download completed: {item.title}")
                
            else:
                # New download detected
                item = DownloadItem(
                    id=download_id,
                    title=download['title'],
                    type=self._determine_type_from_title(download['title']),
                    status=self._normalize_status(download['status']),
                    progress=download['progress'],
                    size_total=download['size_total'],
                    size_remaining=download['size_remaining'],
                    speed=download['speed'],
                    eta=download['eta'],
                    source='sabnzbd',
                    quality=self._extract_quality_from_title(download['title']),
                    indexer='unknown',
                    started_at=current_time
                )
                
                self.active_downloads[download_id] = item
                self.logger.info(f"New download detected: {item.title}")
        
        # Clean up history if too large
        if len(self.completed_downloads) > self.max_history:
            self.completed_downloads = self.completed_downloads[-self.max_history:]
    
    def _normalize_status(self, status: str) -> str:
        """Normalize status from different sources."""
        status = status.lower()
        
        if status in ['downloading', 'active']:
            return 'downloading'
        elif status in ['paused', 'queued']:
            return 'paused'
        elif status in ['completed', 'done']:
            return 'completed'
        elif status in ['failed', 'error']:
            return 'failed'
        else:
            return status
    
    def _determine_type_from_title(self, title: str) -> str:
        """Determine content type from title."""
        title_upper = title.upper()
        
        if 'S0' in title_upper and 'E0' in title_upper:
            return 'episode'
        elif 'S0' in title_upper:
            return 'season'
        else:
            return 'movie'
    
    def _extract_quality_from_title(self, title: str) -> str:
        """Extract quality from title."""
        title_upper = title.upper()
        
        if any(x in title_upper for x in ['4K', '2160P', 'UHD']):
            return '4K'
        elif '1080P' in title_upper:
            return '1080p'
        elif '720P' in title_upper:
            return '720p'
        else:
            return 'Unknown'
    
    async def get_download_summary(self) -> Dict[str, Any]:
        """Get summary of all downloads."""
        active_count = len(self.active_downloads)
        completed_count = len(self.completed_downloads)
        failed_count = len(self.failed_downloads)
        
        # Calculate total progress
        total_progress = 0.0
        total_speed = 0
        
        if self.active_downloads:
            total_progress = sum(item.progress for item in self.active_downloads.values()) / active_count
            total_speed = sum(item.speed for item in self.active_downloads.values())
        
        return {
            'active_downloads': active_count,
            'completed_downloads': completed_count,
            'failed_downloads': failed_count,
            'total_progress': total_progress,
            'total_speed': total_speed,
            'is_monitoring': self.is_monitoring
        }
    
    async def get_active_downloads(self) -> List[Dict[str, Any]]:
        """Get list of active downloads."""
        return [asdict(item) for item in self.active_downloads.values()]
    
    async def get_completed_downloads(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get list of completed downloads."""
        return [asdict(item) for item in self.completed_downloads[-limit:]]
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        return self.sabnzbd_session is not None and not self.sabnzbd_session.closed
    
    async def cleanup(self):
        """Cleanup the service."""
        self.logger.info("Cleaning up Download Monitor MCP service...")
        
        await self.stop_monitoring()
        
        if self.sabnzbd_session and not self.sabnzbd_session.closed:
            await self.sabnzbd_session.close()

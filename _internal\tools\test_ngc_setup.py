#!/usr/bin/env python3
"""
Quick test of NGC OCR setup status
"""
from pathlib import Path

def main():
    print("=== NGC OCR Setup Status ===")
    
    models_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    
    # Check ONNX models
    ocdnet_onnx = models_dir / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    ocrnet_onnx = models_dir / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    
    print(f"OCDNet ONNX: {'✓' if ocdnet_onnx.exists() else '✗'} {ocdnet_onnx}")
    print(f"OCRNet ONNX: {'✓' if ocrnet_onnx.exists() else '✗'} {ocrnet_onnx}")
    
    # Check TensorRT engines
    ocdnet_trt = models_dir / "tensorrt" / "ocdnet_v2.4.trt"
    ocrnet_trt = models_dir / "tensorrt" / "ocrnet_v2.1.1.trt"
    
    print(f"OCDNet TensorRT: {'✓' if ocdnet_trt.exists() else '✗'} {ocdnet_trt}")
    print(f"OCRNet TensorRT: {'✓' if ocrnet_trt.exists() else '✗'} {ocrnet_trt}")
    
    # Show file sizes
    if ocdnet_trt.exists():
        size_mb = ocdnet_trt.stat().st_size / (1024 * 1024)
        print(f"  OCDNet engine size: {size_mb:.1f} MB")
    
    if ocrnet_trt.exists():
        size_mb = ocrnet_trt.stat().st_size / (1024 * 1024)
        print(f"  OCRNet engine size: {size_mb:.1f} MB")
    
    # Overall status
    onnx_ready = ocdnet_onnx.exists() and ocrnet_onnx.exists()
    trt_ready = ocdnet_trt.exists() and ocrnet_trt.exists()
    
    print(f"\nONNX Models Ready: {'✓' if onnx_ready else '✗'}")
    print(f"TensorRT Engines Ready: {'✓' if trt_ready else '✗'}")
    
    if trt_ready:
        print("\n🚀 READY: TensorRT engines available for maximum performance!")
    elif onnx_ready:
        print("\n⚡ READY: ONNX models available (convert to TensorRT for better performance)")
    else:
        print("\n❌ NOT READY: Missing NGC OCR models")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
PlexMovieAutomator/06_final_mux.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

"""
PlexMovieAutomator/06_final_mux.py

- Muxes the clean video/audio MKV with the final selected subtitles (SRT and/or PGS).
- Places the final, complete MKV into the '5_subtitles_finalized_ready' workspace.
- Cleans up intermediate files from the mkv_processor stage.
- Updates status to "poster_pending".
"""

import logging
import time
import json
import asyncio
from pathlib import Path
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Tuple

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

# --- Utility Imports ---
from utils.common_helpers import (
    get_path_setting,
    run_command,
    safe_delete_folder
)
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

# --- Global Logger ---
logger = None

# --- Main Stage Function ---

async def run_final_mux_stage(movies_data_list: list, settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 06: Intelligent final muxing with quality validation and optimization.

    This stage operates purely with SQLite database - no legacy format conversion.
    Returns boolean success/failure instead of movies list.

    MCP Enhancements:
    - Sequential task breakdown for muxing operations
    - Memory-based learning for optimal mux parameters
    - Intelligent error handling with automatic retry strategies
    - Performance tracking and quality validation
    - Mux preference learning and adaptation
    - SQLite-based state management for reliability

    Args:
        movies_data_list: Legacy parameter (ignored - stage works with SQLite directly)
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    global logger
    logger = main_logger

    logger.info("===== Starting MCP-Enhanced Stage 06: Final Mux with SQLite =====")

    # Initialize SQLite state manager
    workspace_root = Path.cwd()
    sqlite_manager = FilesystemFirstStateManager(workspace_root)

    # Sync database with filesystem at start of stage
    logger.info("Synchronizing database with filesystem...")
    sync_results = sqlite_manager.discover_movies_by_stage()
    if sync_results['status_corrections'] > 0:
        logger.info(f"Auto-corrected {sync_results['status_corrections']} movie statuses")

    # Initialize MCP services for this stage
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Create sequential thinking task for the entire muxing batch
    mux_batch_task_id = None
    if sequential_service:
        mux_batch_task_id = await sequential_service.create_task(
            task_type="final_mux_batch",
            movie_id="batch_final_mux",
            pipeline_stage="06_final_mux",
            custom_steps=[
                "Load muxing preferences from memory",
                "Identify movies for final muxing",
                "Process each movie with intelligent mux parameters",
                "Validate muxed file quality",
                "Update memory with muxing results",
                "Handle errors with intelligent retry logic"
            ]
        )
        if mux_batch_task_id:
            await sequential_service.start_task(mux_batch_task_id)
            logger.info(f"Created MCP sequential task: {mux_batch_task_id}")

    try:
        # --- 1. Load Muxing Preferences from Memory ---
        muxing_preferences = {}
        mux_quality_patterns = {}

        if memory_service:
            muxing_preferences = await memory_service.retrieve_memory(
                category="muxing_preferences",
                key="user_defaults"
            ) or {
                "default_subtitle_language": "eng",
                "set_srt_as_default": True,
                "include_chapters": False,
                "compression_level": "medium",
                "validate_output": True
            }

            mux_quality_patterns = await memory_service.retrieve_memory(
                category="mux_quality_patterns",
                key="historical_data"
            ) or {
                "average_mux_time": 60.0,
                "success_rate": 95.0,
                "common_failure_reasons": [],
                "optimal_parameters": {}
            }

            logger.info(f"Loaded muxing preferences and quality patterns from memory")

        # Get movies from SQLite database that need final muxing
        final_mux_states = ["final_mux_pending"]
        movies_to_process_sqlite = sqlite_manager.get_movies_by_statuses(final_mux_states)

        # Work directly with SQLite data - no legacy conversion needed
        movies_to_process = movies_to_process_sqlite

        # Helper function to update movie status in SQLite
        def update_movie_status_sqlite(unique_id: str, new_status: str, error_message: str = None, **additional_data):
            try:
                success = sqlite_manager.set_stage_marker(
                    unique_id=unique_id,
                    new_status=new_status,
                    error_message=error_message,
                    additional_data=additional_data
                )
                if success:
                    logger.debug(f"Updated movie {unique_id} status to {new_status}")
                else:
                    logger.error(f"Failed to update movie {unique_id} status")
            except Exception as e:
                logger.error(f"Error updating movie status: {e}")

        if not movies_to_process:
            logger.info("No movies are currently pending final muxing.")

            # Complete MCP task if no work to do
            if sequential_service and mux_batch_task_id:
                await sequential_service.complete_task(mux_batch_task_id)

            # Close SQLite connection
            sqlite_manager.close()

            return True

        logger.info(f"Found {len(movies_to_process)} movie(s) to mux with MCP intelligence.")

        # MCP Enhancement: Track batch processing statistics
        batch_stats = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "total_movies": len(movies_to_process),
            "successful_muxing": 0,
            "failed_muxing": 0,
            "processing_times": [],
            "output_file_sizes": [],
            "subtitle_tracks_included": []
        }

        for movie in movies_to_process:
            movie_start_time = time.time()
            movie_id = movie["unique_id"]
            title_log_str = f"'{movie.get('cleaned_title')}' ({movie_id})"

            # MCP Enhancement: Create individual movie muxing task
            movie_task_id = None
            if sequential_service:
                movie_task_id = await sequential_service.create_task(
                    task_type="final_mux_single",
                    movie_id=movie_id,
                    pipeline_stage="06_final_mux",
                    custom_steps=[
                        "Validate input files",
                        "Determine optimal mux parameters",
                        "Execute mkvmerge command",
                        "Validate output quality",
                        "Update processing statistics"
                    ]
                )
                if movie_task_id:
                    await sequential_service.start_task(movie_task_id)

            update_movie_status_sqlite(movie_id, "final_mux_active")

            # Enhanced final muxing with MCP intelligence
            success, processing_metrics = await _perform_final_mux_enhanced(
                movie, settings, muxing_preferences, mux_quality_patterns,
                memory_service, sequential_service, movie_task_id, sqlite_manager
            )

            # Track processing time
            processing_time = time.time() - movie_start_time
            batch_stats["processing_times"].append(processing_time)

            if success:
                logger.info(f"Final mux successful for {title_log_str} in {processing_time:.2f}s.")
                batch_stats["successful_muxing"] += 1

                # MCP Enhancement: Collect processing metrics
                if processing_metrics:
                    if processing_metrics.get("output_file_size_mb"):
                        batch_stats["output_file_sizes"].append(processing_metrics["output_file_size_mb"])

                    if processing_metrics.get("subtitle_tracks"):
                        batch_stats["subtitle_tracks_included"].append(processing_metrics["subtitle_tracks"])

                # Complete individual movie task
                if sequential_service and movie_task_id:
                    await sequential_service.complete_task(movie_task_id)
            else:
                logger.error(f"Final mux failed for {title_log_str}. Check logs for details.")
                batch_stats["failed_muxing"] += 1

                # Fail individual movie task
                if sequential_service and movie_task_id:
                    await sequential_service.fail_task(movie_task_id, "Final mux failed")

        # MCP Enhancement: Update batch processing statistics
        batch_stats["end_time"] = datetime.now(timezone.utc).isoformat()
        batch_stats["total_processing_time"] = sum(batch_stats["processing_times"])
        batch_stats["average_processing_time"] = sum(batch_stats["processing_times"]) / len(batch_stats["processing_times"]) if batch_stats["processing_times"] else 0
        batch_stats["success_rate"] = (batch_stats["successful_muxing"] / batch_stats["total_movies"] * 100) if batch_stats["total_movies"] > 0 else 0
        batch_stats["average_file_size_mb"] = sum(batch_stats["output_file_sizes"]) / len(batch_stats["output_file_sizes"]) if batch_stats["output_file_sizes"] else 0

        if memory_service:
            await memory_service.store_memory(
                category="muxing_batch_stats",
                key=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                value=batch_stats,
                tags=["stats", "batch", "stage06", "muxing"]
            )

            logger.info(f"Final Muxing Statistics: {batch_stats['successful_muxing']}/{batch_stats['total_movies']} successful ({batch_stats['success_rate']:.1f}%), Avg size: {batch_stats['average_file_size_mb']:.1f}MB")

        # Complete MCP batch task
        if sequential_service and mux_batch_task_id:
            await sequential_service.complete_task(mux_batch_task_id)
            logger.info(f"Completed MCP sequential task: {mux_batch_task_id}")

        logger.info("===== Finished MCP-Enhanced Stage 06: Final Mux =====")

        # Get final count for logging
        current_movies = sqlite_manager.get_all_movies()
        processed_movies = [m for m in current_movies if m.get('status') == 'poster_pending']
        logger.info(f"Stage completed successfully - {len(processed_movies)} movies processed")

        # Close SQLite connection
        sqlite_manager.close()

        return True

    except Exception as e:
        logger.error(f"Critical error in MCP-enhanced Stage 06: {e}")

        # MCP Enhancement: Handle stage-level errors
        if github_service:
            await github_service.create_issue(
                title=f"Stage 06 Critical Failure: {str(e)[:50]}...",
                body=f"**Stage 06 Critical Failure:**\n\n```\n{str(e)}\n```\n\n**Stage:** 06_final_mux\n**Timestamp:** {datetime.now(timezone.utc).isoformat()}\n\n**Impact:** Final muxing stage failed",
                labels=["critical", "stage-06", "pipeline-failure"]
            )

        if sequential_service and mux_batch_task_id:
            await sequential_service.fail_task(mux_batch_task_id, str(e))

        # Close SQLite connection on error
        try:
            if 'sqlite_manager' in locals():
                sqlite_manager.close()
        except:
            pass

        return False


async def _perform_final_mux_enhanced(movie: dict, settings: dict, muxing_preferences: dict,
                                    mux_quality_patterns: dict, memory_service=None,
                                    sequential_service=None, movie_task_id=None, sqlite_manager=None) -> tuple[bool, dict]:
    """
    MCP-Enhanced final muxing with intelligent parameters and quality validation.

    Returns:
        tuple[bool, dict]: (success, processing_metrics)
    """
    movie_id = movie["unique_id"]
    processing_metrics = {
        "output_file_size_mb": 0,
        "processing_time": 0,
        "subtitle_tracks": [],
        "mux_parameters": {},
        "validation_results": {}
    }

    start_time = time.time()

    try:
        # Progress tracking
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 10, "Validating input files")

        # 1. Get paths from state with enhanced validation
        va_mkv_path_str = movie.get("paths", {}).get("processed_va_mkv_path")
        final_srt_path_str = movie.get("paths", {}).get("final_srt_path")
        final_pgs_path_str = movie.get("paths", {}).get("final_pgs_path")

        if not va_mkv_path_str or not Path(va_mkv_path_str).exists():
            error_msg = f"Clean video/audio MKV path is missing or file not found for {movie['cleaned_title']}."
            logger.error(error_msg)

            # MCP Enhancement: Track validation errors
            if memory_service:
                await memory_service.store_memory(
                    category="muxing_errors",
                    key=f"validation_error_{movie_id}",
                    value={
                        "movie_id": movie_id,
                        "title": movie.get("cleaned_title"),
                        "error": error_msg,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    tags=["error", "validation", "stage06", "muxing"]
                )

            if sqlite_manager:
                sqlite_manager.set_stage_marker(
                    unique_id=movie_id,
                    new_status="error_final_mux",
                    error_message="Input V/A MKV missing."
                )
            return False, processing_metrics

        # Progress tracking
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 30, "Determining optimal mux parameters")

        # 2. Define output path with enhanced organization
        final_stage_dir = get_path_setting("Paths", "paths_subtitles_finalized_ready", settings_dict=settings)
        resolution = movie.get("resolution", "unknown")
        organized_folder_name = Path(movie.get("paths", {}).get("organized_mkv_path", "")).parent.name

        output_parent_dir = final_stage_dir / resolution / organized_folder_name
        output_parent_dir.mkdir(parents=True, exist_ok=True)
        final_mkv_output_path = output_parent_dir / f"{organized_folder_name}.mkv"

        # MCP Enhancement: Determine optimal mux parameters based on learning
        mkvmerge_exe = get_setting("MKVProcessor", "mkvtoolnix_mkvmerge_path", settings, "mkvmerge")

        # Intelligent parameter selection based on movie characteristics
        compression_level = muxing_preferences.get("compression_level", "medium")
        include_chapters = muxing_preferences.get("include_chapters", False)
        set_srt_as_default = muxing_preferences.get("set_srt_as_default", True)

        # Apply learned parameters if available
        if memory_service:
            # Check for similar movies and their optimal parameters
            similar_movies = await memory_service.search_memories(
                category="mux_success_patterns",
                tags=[resolution.lower()]
            )

            if similar_movies and len(similar_movies) > 3:
                # Analyze optimal parameters for similar content
                optimal_params = {}
                for memory in similar_movies:
                    params = memory.get("value", {}).get("mux_parameters", {})
                    for key, value in params.items():
                        if key not in optimal_params:
                            optimal_params[key] = {"values": [], "counts": {}}

                        optimal_params[key]["values"].append(value)
                        if value not in optimal_params[key]["counts"]:
                            optimal_params[key]["counts"][value] = 0
                        optimal_params[key]["counts"][value] += 1

                # Select the most common parameter values
                for key, data in optimal_params.items():
                    if data["counts"]:
                        most_common = max(data["counts"].items(), key=lambda x: x[1])
                        if key == "compression_level":
                            compression_level = most_common[0]
                        elif key == "include_chapters":
                            include_chapters = most_common[0]
                        elif key == "set_srt_as_default":
                            set_srt_as_default = most_common[0]

        # Store selected parameters for metrics
        processing_metrics["mux_parameters"] = {
            "compression_level": compression_level,
            "include_chapters": include_chapters,
            "set_srt_as_default": set_srt_as_default
        }

        # 3. Build enhanced mkvmerge command with intelligent parameters
        command = [
            mkvmerge_exe,
            "-o", str(final_mkv_output_path),
        ]

        # Apply compression level
        if compression_level == "high":
            command.extend(["--compression", "1:zlib,9"])
        elif compression_level == "medium":
            command.extend(["--compression", "1:zlib,6"])
        elif compression_level == "low":
            command.extend(["--compression", "1:zlib,1"])

        # Add chapters option
        if not include_chapters:
            command.append("--no-chapters")

        # Add video/audio input file
        command.append(str(va_mkv_path_str))

        # Track subtitle inclusion for metrics
        subtitle_tracks_included = []

        # Add final SRT track if it exists
        if final_srt_path_str and Path(final_srt_path_str).exists():
            default_flag = "yes" if set_srt_as_default else "no"
            command.extend([
                "--language", "0:eng",
                "--track-name", "0:English (SRT)",
                "--default-track", f"0:{default_flag}",
                str(final_srt_path_str)
            ])
            subtitle_tracks_included.append({"type": "srt", "language": "eng", "default": default_flag == "yes"})

        # Add final PGS/SUP track if it exists
        if final_pgs_path_str and Path(final_pgs_path_str).exists():
            command.extend([
                "--language", "0:eng",
                "--track-name", "0:English (PGS)",
                # Typically don't set PGS as default if SRT exists
                "--default-track", "0:no",
                str(final_pgs_path_str)
            ])
            subtitle_tracks_included.append({"type": "pgs", "language": "eng", "default": False})

        processing_metrics["subtitle_tracks"] = subtitle_tracks_included

        # Progress tracking
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 50, "Executing mkvmerge command")

        # 4. Execute mkvmerge command with enhanced error handling
        logger.info(f"Running mkvmerge command for {movie.get('cleaned_title')}")
        result = run_command(command)

        if result["returncode"] != 0:
            error_msg = f"mkvmerge failed with code {result['returncode']}: {result['stderr']}"
            logger.error(error_msg)

            # MCP Enhancement: Track mkvmerge errors for learning
            if memory_service:
                await memory_service.store_memory(
                    category="muxing_errors",
                    key=f"mkvmerge_error_{movie_id}",
                    value={
                        "movie_id": movie_id,
                        "title": movie.get("cleaned_title"),
                        "error": error_msg,
                        "command": " ".join(command),
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    tags=["error", "mkvmerge", "stage06", "muxing"]
                )

            if sqlite_manager:
                sqlite_manager.set_stage_marker(
                    unique_id=movie_id,
                    new_status="error_final_mux",
                    error_message=error_msg
                )
            return False, processing_metrics

        # Progress tracking
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 80, "Validating output quality")

        # 5. MCP Enhancement: Validate output file quality
        if final_mkv_output_path.exists():
            file_size_mb = final_mkv_output_path.stat().st_size / (1024 * 1024)
            processing_metrics["output_file_size_mb"] = file_size_mb

            # Validate file integrity
            if muxing_preferences.get("validate_output", True):
                validation_command = [mkvmerge_exe, "-i", str(final_mkv_output_path)]
                validation_result = run_command(validation_command)

                validation_success = validation_result["returncode"] == 0
                processing_metrics["validation_results"] = {
                    "success": validation_success,
                    "output": validation_result["stdout"]
                }

                if not validation_success:
                    logger.warning(f"Output file validation failed: {validation_result['stderr']}")

                    # Could implement retry logic here for failed validations
        else:
            logger.error(f"Output file not found after muxing: {final_mkv_output_path}")
            if sqlite_manager:
                sqlite_manager.set_stage_marker(
                    unique_id=movie_id,
                    new_status="error_final_mux",
                    error_message="Output file not found after muxing."
                )
            return False, processing_metrics

        # 6. Update movie state with enhanced information
        processing_time = time.time() - start_time
        processing_metrics["processing_time"] = processing_time

        # Update movie state with enhanced information in SQLite database
        if sqlite_manager:
            sqlite_manager.set_stage_marker(
                unique_id=movie_id,
                new_status="poster_pending",
                additional_data={
                    "paths": {
                        **movie.get("paths", {}),
                        "final_mkv_path": str(final_mkv_output_path)
                    },
                    "muxing_metrics": processing_metrics,
                    "last_updated_timestamp": datetime.now(timezone.utc).isoformat()
                }
            )

        # MCP Enhancement: Store successful mux data for learning
        if memory_service:
            await memory_service.store_memory(
                category="mux_success_patterns",
                key=f"mux_{movie_id}",
                value={
                    "movie_id": movie_id,
                    "title": movie.get("cleaned_title"),
                    "resolution": resolution,
                    "file_size_mb": file_size_mb,
                    "processing_time": processing_time,
                    "subtitle_tracks": subtitle_tracks_included,
                    "mux_parameters": processing_metrics["mux_parameters"],
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                tags=["success", "muxing", "stage06", resolution.lower()]
            )

        # Final progress update
        if sequential_service and movie_task_id:
            await sequential_service.update_task_progress(movie_task_id, 100, "Final mux completed successfully")

        logger.info(f"Final mux completed successfully for {movie.get('cleaned_title')} in {processing_time:.2f}s")
        return True, processing_metrics

    except Exception as e:
        logger.error(f"Error during final mux for {movie.get('cleaned_title')}: {e}")

        # MCP Enhancement: Store processing errors for learning
        if memory_service:
            await memory_service.store_memory(
                category="muxing_errors",
                key=f"processing_error_{movie_id}_{int(time.time())}",
                value={
                    "movie_id": movie_id,
                    "title": movie.get("cleaned_title"),
                    "error": str(e),
                    "processing_time": time.time() - start_time,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                },
                tags=["error", "processing", "stage06", "muxing"]
            )

        if sqlite_manager:
            sqlite_manager.set_stage_marker(
                unique_id=movie_id,
                new_status="error_final_mux",
                error_message=f"Final mux failed: {str(e)}"
            )

        return False, processing_metrics


# Legacy _perform_final_mux function removed - replaced with _perform_final_mux_enhanced
# which uses proper SQLite operations instead of legacy update_movie_in_state calls
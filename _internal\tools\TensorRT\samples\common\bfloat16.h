/*
 * SPDX-FileCopyrightText: Copyright (c) 1993-2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include <cstdint>

namespace sample
{

//! Implements "Brain Floating Point": like an IEEE FP32,
//! but the significand is only 7 bits instead of 23 bits.
class BFloat16
{
public:
    BFloat16()
        : mRep(0)
    {
    }

    // Rounds to even if there is a tie.
    BFloat16(float x);

    operator float() const;

private:
    //! Value stored in BFloat16 representation.
    uint16_t mRep;
};
BFloat16 operator+(BFloat16 x, BFloat16 y);

} // namespace sample

#!/usr/bin/env python3
"""
Step 3 Testing & Verification Script
Test NGC OCR integration with PlexMovieAutomator pipeline
"""

import os
import sys
from pathlib import Path

def test_step3_integration():
    """Test that Step 3 components are ready"""
    print("🔍 STEP 3: NGC OCR INTEGRATION TESTING")
    print("=" * 60)
    print()
    
    # Test 1: Check NGC models are available
    print("📁 TEST 1: NGC Model Availability")
    print("-" * 40)
    
    models_base = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    
    # Check OCDNet (Text Detection)
    ocdnet_path = models_base / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    if ocdnet_path.exists():
        print(f"✅ OCDNet (Text Detection): Found")
        print(f"   📍 {ocdnet_path}")
    else:
        print(f"❌ OCDNet: Not found at {ocdnet_path}")
    
    # Check OCRNet (Text Recognition)
    ocrnet_path = models_base / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    if ocrnet_path.exists():
        print(f"✅ OCRNet (Text Recognition): Found")
        print(f"   📍 {ocrnet_path}")
    else:
        print(f"❌ OCRNet: Not found at {ocrnet_path}")
    
    print()
    
    # Test 2: Check dependencies
    print("📚 TEST 2: Dependency Verification")
    print("-" * 40)
    
    dependencies = {
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'onnxruntime-gpu': 'onnxruntime',
        'tensorrt': 'tensorrt'
    }
    
    for package, import_name in dependencies.items():
        try:
            # Special handling for TensorRT
            if import_name == 'tensorrt':
                # Set up TensorRT path
                tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
                current_path = os.environ.get('PATH', '')
                if tensorrt_lib_path not in current_path:
                    os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"
            
            __import__(import_name)
            print(f"✅ {package}: Available")
        except ImportError as e:
            print(f"❌ {package}: Not available ({e})")
        except Exception as e:
            print(f"⚠️  {package}: Issue loading ({e})")
    
    print()
    
    # Test 3: Integration components
    print("🔧 TEST 3: Integration Components")
    print("-" * 40)
    
    tools_dir = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools")
    
    components = [
        "step3_text_detection.py",
        "subtitle_quality_assessor.py", 
        "pipeline_integrator.py"
    ]
    
    for component in components:
        component_path = tools_dir / component
        if component_path.exists():
            print(f"✅ {component}: Ready")
        else:
            print(f"❌ {component}: Missing")
    
    print()
    
    # Test 4: Pipeline integration points
    print("🔗 TEST 4: Pipeline Integration Points")
    print("-" * 40)
    
    pipeline_files = [
        "03_mkv_processor.py",
        "05_subtitle_handler.py"
    ]
    
    workspace_root = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator")
    
    for pipeline_file in pipeline_files:
        file_path = workspace_root / pipeline_file
        if file_path.exists():
            print(f"✅ {pipeline_file}: Found - Ready for enhancement")
        else:
            print(f"❌ {pipeline_file}: Not found")
    
    print()
    
    # Test 5: Simple functionality test
    print("⚡ TEST 5: Basic Functionality")
    print("-" * 40)
    
    try:
        # Test quality assessment
        from subtitle_quality_assessor import assess_subtitle_quality
        
        # Test with dummy data
        test_log = "Successfully extracted text regions"
        test_text = ["Hello world", "This is a test subtitle"]
        
        quality_metrics = assess_subtitle_quality(test_log, test_text)
        print(f"✅ Quality Assessment: Working")
        print(f"   Confidence: {quality_metrics.confidence_score:.2f}")
        print(f"   Needs Enhancement: {quality_metrics.needs_enhancement}")
        
    except Exception as e:
        print(f"❌ Quality Assessment: Failed ({e})")
    
    try:
        # Test pipeline integrator
        from pipeline_integrator import check_ngc_ocr_available
        
        available = check_ngc_ocr_available()
        print(f"✅ Pipeline Integration: {'Ready' if available else 'Models missing'}")
        
    except Exception as e:
        print(f"❌ Pipeline Integration: Failed ({e})")
    
    print()
    
    # Summary
    print("🎯 STEP 3 INTEGRATION SUMMARY")
    print("-" * 40)
    
    # Check overall readiness
    models_ready = ocdnet_path.exists() and ocrnet_path.exists()
    
    if models_ready:
        print("✅ NGC Models: Ready for text detection and recognition")
        print("✅ Integration System: Built and tested")
        print("✅ Quality Assessment: Functional")
        print("✅ Pipeline Enhancement: Ready for deployment")
        print()
        print("🚀 STEP 3 STATUS: COMPLETE & READY FOR TESTING")
        print()
        print("📋 NEXT STEPS:")
        print("1. Test with actual video files")
        print("2. Compare BDSup2Sub vs NGC OCR results")
        print("3. Fine-tune quality thresholds")
        print("4. Deploy to production pipeline")
    else:
        print("⚠️  NGC Models: Need to be properly placed")
        print("📍 Expected locations:")
        print(f"   OCDNet: {ocdnet_path}")
        print(f"   OCRNet: {ocrnet_path}")

if __name__ == "__main__":
    test_step3_integration()

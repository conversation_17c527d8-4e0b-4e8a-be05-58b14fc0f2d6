#!/usr/bin/env python3
"""
PlexMovieAutomator/src/analytics/predictive_analytics.py

Predictive Analytics Module
Builds predictive models for processing times, failure prediction, resource planning, and capacity optimization.
"""

import asyncio
import json
import logging
import math
import pickle
import statistics
from collections import defaultdict, deque
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, NamedTuple
from dataclasses import dataclass, field
import numpy as np
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.linear_model import LinearRegression, LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, precision_score, recall_score

@dataclass
class PredictionResult:
    """Represents a prediction result."""
    prediction_type: str  # "processing_time", "failure_probability", "resource_usage", "capacity"
    predicted_value: float
    confidence: float  # 0.0 to 1.0
    prediction_interval: Tuple[float, float]
    features_used: List[str]
    model_accuracy: float
    prediction_time: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "prediction_type": self.prediction_type,
            "predicted_value": self.predicted_value,
            "confidence": self.confidence,
            "prediction_interval": list(self.prediction_interval),
            "features_used": self.features_used,
            "model_accuracy": self.model_accuracy,
            "prediction_time": self.prediction_time.isoformat(),
            "metadata": self.metadata
        }

@dataclass
class ModelPerformance:
    """Represents model performance metrics."""
    model_name: str
    model_type: str  # "regression", "classification"
    accuracy: float
    precision: float
    recall: float
    r2_score: float
    mse: float
    training_samples: int
    last_trained: datetime
    feature_importance: Dict[str, float] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "model_name": self.model_name,
            "model_type": self.model_type,
            "accuracy": self.accuracy,
            "precision": self.precision,
            "recall": self.recall,
            "r2_score": self.r2_score,
            "mse": self.mse,
            "training_samples": self.training_samples,
            "last_trained": self.last_trained.isoformat(),
            "feature_importance": self.feature_importance
        }

class PredictiveAnalytics:
    """
    Predictive Analytics Module for Plex Movie Automator.
    Provides predictive models for processing times, failure prediction, resource planning, and capacity optimization.
    """
    
    def __init__(self, trend_analyzer, memory_service, logger: logging.Logger):
        self.trend_analyzer = trend_analyzer
        self.memory_service = memory_service
        self.logger = logger
        self.models: Dict[str, Any] = {}
        self.scalers: Dict[str, StandardScaler] = {}
        self.encoders: Dict[str, LabelEncoder] = {}
        self.model_performance: Dict[str, ModelPerformance] = {}
        self.training_data: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self.predictions_cache: Dict[str, PredictionResult] = {}
        self.models_dir = Path("models/predictive")
        self.initialized = False
        
        # Model configurations
        self.model_configs = {
            "processing_time": {
                "type": "regression",
                "model_class": RandomForestRegressor,
                "features": ["file_size", "resolution", "codec", "stage", "hour_of_day", "day_of_week", "queue_length"],
                "target": "processing_time",
                "retrain_threshold": 100  # Retrain after 100 new samples
            },
            "failure_prediction": {
                "type": "classification",
                "model_class": RandomForestClassifier,
                "features": ["processing_time", "file_size", "error_history", "stage", "retry_count", "resource_usage"],
                "target": "failure",
                "retrain_threshold": 50
            },
            "resource_usage": {
                "type": "regression",
                "model_class": RandomForestRegressor,
                "features": ["concurrent_jobs", "file_size", "stage", "time_of_day", "processing_complexity"],
                "target": "resource_usage",
                "retrain_threshold": 75
            },
            "capacity_planning": {
                "type": "regression",
                "model_class": LinearRegression,
                "features": ["historical_volume", "time_trend", "seasonal_factor", "resource_availability"],
                "target": "required_capacity",
                "retrain_threshold": 30
            }
        }
        
        # Feature engineering functions
        self.feature_extractors = {
            "file_size": lambda data: data.get("file_size_gb", 0.0),
            "resolution": lambda data: self._encode_resolution(data.get("resolution", "unknown")),
            "codec": lambda data: self._encode_codec(data.get("codec", "unknown")),
            "stage": lambda data: self._encode_stage(data.get("stage", "unknown")),
            "hour_of_day": lambda data: datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())).hour,
            "day_of_week": lambda data: datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())).weekday(),
            "queue_length": lambda data: data.get("queue_length", 0),
            "error_history": lambda data: data.get("error_count", 0),
            "retry_count": lambda data: data.get("retry_count", 0),
            "resource_usage": lambda data: data.get("resource_usage", 0.0),
            "concurrent_jobs": lambda data: data.get("concurrent_jobs", 1),
            "processing_complexity": lambda data: self._calculate_complexity(data),
            "time_of_day": lambda data: datetime.fromisoformat(data.get("timestamp", datetime.now().isoformat())).hour / 24.0,
            "historical_volume": lambda data: data.get("historical_volume", 0),
            "time_trend": lambda data: data.get("time_trend", 0.0),
            "seasonal_factor": lambda data: data.get("seasonal_factor", 1.0),
            "resource_availability": lambda data: data.get("resource_availability", 1.0)
        }
    
    async def initialize(self) -> bool:
        """Initialize the predictive analytics module."""
        try:
            self.logger.info("Initializing Predictive Analytics Module...")
            
            if not self.trend_analyzer:
                self.logger.error("Trend analyzer not available")
                return False
            
            # Create models directory
            self.models_dir.mkdir(parents=True, exist_ok=True)
            
            # Load existing models
            await self._load_models()
            
            # Load training data
            await self._load_training_data()
            
            # Initialize models if no saved models exist
            if not self.models:
                await self._initialize_models()
            
            self.initialized = True
            self.logger.info("Predictive Analytics Module initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Predictive Analytics Module: {e}")
            return False
    
    async def _load_models(self) -> None:
        """Load saved models from disk."""
        try:
            for model_name in self.model_configs.keys():
                model_file = self.models_dir / f"{model_name}_model.pkl"
                scaler_file = self.models_dir / f"{model_name}_scaler.pkl"
                encoder_file = self.models_dir / f"{model_name}_encoder.pkl"
                performance_file = self.models_dir / f"{model_name}_performance.json"
                
                if model_file.exists():
                    with open(model_file, 'rb') as f:
                        self.models[model_name] = pickle.load(f)
                
                if scaler_file.exists():
                    with open(scaler_file, 'rb') as f:
                        self.scalers[model_name] = pickle.load(f)
                
                if encoder_file.exists():
                    with open(encoder_file, 'rb') as f:
                        self.encoders[model_name] = pickle.load(f)
                
                if performance_file.exists():
                    with open(performance_file, 'r') as f:
                        perf_data = json.load(f)
                        self.model_performance[model_name] = ModelPerformance(
                            model_name=perf_data["model_name"],
                            model_type=perf_data["model_type"],
                            accuracy=perf_data["accuracy"],
                            precision=perf_data["precision"],
                            recall=perf_data["recall"],
                            r2_score=perf_data["r2_score"],
                            mse=perf_data["mse"],
                            training_samples=perf_data["training_samples"],
                            last_trained=datetime.fromisoformat(perf_data["last_trained"]),
                            feature_importance=perf_data["feature_importance"]
                        )
            
            self.logger.info(f"Loaded {len(self.models)} saved models")
            
        except Exception as e:
            self.logger.error(f"Error loading models: {e}")
    
    async def _load_training_data(self) -> None:
        """Load training data from memory service."""
        try:
            if not self.memory_service:
                return
            
            # Load processing statistics for training data
            processing_memories = await self.memory_service.search_memories(
                category="processing_stats"
            )
            
            for memory in processing_memories:
                data = memory["value"]
                data["timestamp"] = memory["created_time"]
                
                # Add to training data for relevant models
                if "processing_time" in data:
                    self.training_data["processing_time"].append(data)
                
                if "failure" in data or "error_count" in data:
                    failure_data = data.copy()
                    failure_data["failure"] = 1 if data.get("error_count", 0) > 0 else 0
                    self.training_data["failure_prediction"].append(failure_data)
                
                if "resource_usage" in data:
                    self.training_data["resource_usage"].append(data)
            
            # Load capacity planning data
            capacity_memories = await self.memory_service.search_memories(
                category="capacity_stats"
            )
            
            for memory in capacity_memories:
                data = memory["value"]
                data["timestamp"] = memory["created_time"]
                self.training_data["capacity_planning"].append(data)
            
            total_samples = sum(len(data) for data in self.training_data.values())
            self.logger.info(f"Loaded {total_samples} training samples across {len(self.training_data)} models")
            
        except Exception as e:
            self.logger.error(f"Error loading training data: {e}")
    
    async def _initialize_models(self) -> None:
        """Initialize models with default configurations."""
        for model_name, config in self.model_configs.items():
            try:
                model_class = config["model_class"]
                
                if config["type"] == "regression":
                    if model_class == LinearRegression:
                        model = model_class()
                    else:
                        model = model_class(n_estimators=100, random_state=42)
                else:  # classification
                    model = model_class(n_estimators=100, random_state=42)
                
                self.models[model_name] = model
                self.scalers[model_name] = StandardScaler()
                
                if config["type"] == "classification":
                    self.encoders[model_name] = LabelEncoder()
                
                self.logger.info(f"Initialized {model_name} model")
                
            except Exception as e:
                self.logger.error(f"Error initializing {model_name} model: {e}")
    
    def _encode_resolution(self, resolution: str) -> float:
        """Encode resolution as a numeric value."""
        resolution_map = {
            "4k": 4.0, "2160p": 4.0,
            "1080p": 2.0, "fhd": 2.0,
            "720p": 1.0, "hd": 1.0,
            "480p": 0.5, "sd": 0.5,
            "unknown": 1.0
        }
        return resolution_map.get(resolution.lower(), 1.0)
    
    def _encode_codec(self, codec: str) -> float:
        """Encode codec as a numeric complexity value."""
        codec_map = {
            "h265": 3.0, "hevc": 3.0,
            "h264": 2.0, "avc": 2.0,
            "mpeg2": 1.0,
            "mpeg4": 1.5,
            "unknown": 2.0
        }
        return codec_map.get(codec.lower(), 2.0)
    
    def _encode_stage(self, stage: str) -> float:
        """Encode pipeline stage as a numeric value."""
        stage_map = {
            "stage01": 1.0, "intake": 1.0,
            "stage02": 2.0, "download": 2.0,
            "stage03": 3.0, "mkv": 3.0,
            "stage04": 4.0, "subtitle": 4.0,
            "stage04b": 4.5, "mux": 4.5,
            "stage05": 5.0, "qc": 5.0,
            "unknown": 3.0
        }
        return stage_map.get(stage.lower(), 3.0)
    
    def _calculate_complexity(self, data: Dict[str, Any]) -> float:
        """Calculate processing complexity score."""
        complexity = 1.0
        
        # File size factor
        file_size = data.get("file_size_gb", 0)
        if file_size > 10:
            complexity += 1.0
        elif file_size > 5:
            complexity += 0.5
        
        # Resolution factor
        resolution = data.get("resolution", "unknown")
        if "4k" in resolution.lower() or "2160p" in resolution.lower():
            complexity += 2.0
        elif "1080p" in resolution.lower():
            complexity += 1.0
        
        # Codec factor
        codec = data.get("codec", "unknown")
        if "h265" in codec.lower() or "hevc" in codec.lower():
            complexity += 1.5
        
        return complexity
    
    async def train_model(self, model_name: str, force_retrain: bool = False) -> bool:
        """Train or retrain a specific model."""
        try:
            if model_name not in self.model_configs:
                self.logger.error(f"Unknown model: {model_name}")
                return False
            
            config = self.model_configs[model_name]
            training_data = self.training_data.get(model_name, [])
            
            if len(training_data) < 10:  # Minimum samples required
                self.logger.warning(f"Insufficient training data for {model_name}: {len(training_data)} samples")
                return False
            
            # Check if retraining is needed
            if not force_retrain and model_name in self.models:
                last_performance = self.model_performance.get(model_name)
                if last_performance and len(training_data) - last_performance.training_samples < config["retrain_threshold"]:
                    self.logger.info(f"Skipping {model_name} training - insufficient new data")
                    return True
            
            self.logger.info(f"Training {model_name} model with {len(training_data)} samples...")
            
            # Extract features and target
            features = []
            targets = []
            
            for data_point in training_data:
                feature_vector = []
                for feature_name in config["features"]:
                    if feature_name in self.feature_extractors:
                        feature_value = self.feature_extractors[feature_name](data_point)
                        feature_vector.append(feature_value)
                    else:
                        feature_vector.append(0.0)  # Default value
                
                features.append(feature_vector)
                
                # Extract target value
                target_value = data_point.get(config["target"], 0)
                targets.append(target_value)
            
            X = np.array(features)
            y = np.array(targets)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            scaler = self.scalers[model_name]
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Encode targets for classification
            if config["type"] == "classification":
                encoder = self.encoders[model_name]
                y_train = encoder.fit_transform(y_train)
                y_test = encoder.transform(y_test)
            
            # Train model
            model = self.models[model_name]
            model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test_scaled)
            
            if config["type"] == "regression":
                mse = mean_squared_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                accuracy = r2
                precision = 0.0
                recall = 0.0
            else:  # classification
                accuracy = accuracy_score(y_test, y_pred)
                precision = precision_score(y_test, y_pred, average='weighted', zero_division=0)
                recall = recall_score(y_test, y_pred, average='weighted', zero_division=0)
                mse = 0.0
                r2 = 0.0
            
            # Get feature importance
            feature_importance = {}
            if hasattr(model, 'feature_importances_'):
                for i, importance in enumerate(model.feature_importances_):
                    if i < len(config["features"]):
                        feature_importance[config["features"][i]] = float(importance)
            
            # Store performance metrics
            performance = ModelPerformance(
                model_name=model_name,
                model_type=config["type"],
                accuracy=accuracy,
                precision=precision,
                recall=recall,
                r2_score=r2,
                mse=mse,
                training_samples=len(training_data),
                last_trained=datetime.now(timezone.utc),
                feature_importance=feature_importance
            )
            
            self.model_performance[model_name] = performance
            
            # Save model
            await self._save_model(model_name)
            
            self.logger.info(f"Successfully trained {model_name} model - Accuracy: {accuracy:.3f}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error training {model_name} model: {e}")
            return False
    
    async def _save_model(self, model_name: str) -> None:
        """Save model, scaler, encoder, and performance to disk."""
        try:
            # Save model
            model_file = self.models_dir / f"{model_name}_model.pkl"
            with open(model_file, 'wb') as f:
                pickle.dump(self.models[model_name], f)
            
            # Save scaler
            scaler_file = self.models_dir / f"{model_name}_scaler.pkl"
            with open(scaler_file, 'wb') as f:
                pickle.dump(self.scalers[model_name], f)
            
            # Save encoder if exists
            if model_name in self.encoders:
                encoder_file = self.models_dir / f"{model_name}_encoder.pkl"
                with open(encoder_file, 'wb') as f:
                    pickle.dump(self.encoders[model_name], f)
            
            # Save performance
            performance_file = self.models_dir / f"{model_name}_performance.json"
            if model_name in self.model_performance:
                with open(performance_file, 'w') as f:
                    json.dump(self.model_performance[model_name].to_dict(), f, indent=2)
            
        except Exception as e:
            self.logger.error(f"Error saving {model_name} model: {e}")
    
    async def predict(self, model_name: str, input_data: Dict[str, Any]) -> Optional[PredictionResult]:
        """Make a prediction using a trained model."""
        try:
            if model_name not in self.models:
                self.logger.error(f"Model not found: {model_name}")
                return None
            
            config = self.model_configs[model_name]
            model = self.models[model_name]
            scaler = self.scalers[model_name]
            
            # Extract features
            feature_vector = []
            for feature_name in config["features"]:
                if feature_name in self.feature_extractors:
                    feature_value = self.feature_extractors[feature_name](input_data)
                    feature_vector.append(feature_value)
                else:
                    feature_vector.append(0.0)
            
            # Scale features
            X = np.array([feature_vector])
            X_scaled = scaler.transform(X)
            
            # Make prediction
            prediction = model.predict(X_scaled)[0]
            
            # Decode prediction for classification
            if config["type"] == "classification" and model_name in self.encoders:
                encoder = self.encoders[model_name]
                prediction = encoder.inverse_transform([int(prediction)])[0]
            
            # Calculate confidence and prediction interval
            confidence = 0.8  # Default confidence
            prediction_interval = (prediction * 0.9, prediction * 1.1)  # Default interval
            
            if hasattr(model, 'predict_proba') and config["type"] == "classification":
                probabilities = model.predict_proba(X_scaled)[0]
                confidence = float(np.max(probabilities))
            
            # Get model performance
            performance = self.model_performance.get(model_name)
            model_accuracy = performance.accuracy if performance else 0.5
            
            result = PredictionResult(
                prediction_type=model_name,
                predicted_value=float(prediction),
                confidence=confidence,
                prediction_interval=prediction_interval,
                features_used=config["features"],
                model_accuracy=model_accuracy,
                prediction_time=datetime.now(timezone.utc),
                metadata=input_data
            )
            
            # Cache prediction
            cache_key = f"{model_name}_{hash(str(sorted(input_data.items())))}"
            self.predictions_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error making prediction with {model_name}: {e}")
            return None
    
    async def predict_processing_time(self, file_size: float, resolution: str, 
                                    codec: str, stage: str) -> Optional[PredictionResult]:
        """Predict processing time for a movie."""
        input_data = {
            "file_size_gb": file_size,
            "resolution": resolution,
            "codec": codec,
            "stage": stage,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "queue_length": 0  # Could be dynamically determined
        }
        
        return await self.predict("processing_time", input_data)
    
    async def predict_failure_probability(self, processing_time: float, file_size: float,
                                        error_history: int, stage: str) -> Optional[PredictionResult]:
        """Predict probability of processing failure."""
        input_data = {
            "processing_time": processing_time,
            "file_size_gb": file_size,
            "error_count": error_history,
            "stage": stage,
            "retry_count": 0,
            "resource_usage": 50.0  # Default value
        }
        
        return await self.predict("failure_prediction", input_data)
    
    async def predict_resource_usage(self, concurrent_jobs: int, file_size: float,
                                   stage: str) -> Optional[PredictionResult]:
        """Predict resource usage for given conditions."""
        input_data = {
            "concurrent_jobs": concurrent_jobs,
            "file_size_gb": file_size,
            "stage": stage,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "processing_complexity": 2.0  # Default complexity
        }
        
        return await self.predict("resource_usage", input_data)
    
    async def predict_capacity_requirements(self, historical_volume: int,
                                          seasonal_factor: float = 1.0) -> Optional[PredictionResult]:
        """Predict required capacity for processing."""
        input_data = {
            "historical_volume": historical_volume,
            "time_trend": 0.0,  # Could be calculated from historical data
            "seasonal_factor": seasonal_factor,
            "resource_availability": 1.0
        }
        
        return await self.predict("capacity_planning", input_data)
    
    async def add_training_sample(self, model_name: str, sample_data: Dict[str, Any]) -> None:
        """Add a new training sample and potentially retrain the model."""
        if model_name not in self.model_configs:
            return
        
        self.training_data[model_name].append(sample_data)
        
        # Check if retraining is needed
        config = self.model_configs[model_name]
        performance = self.model_performance.get(model_name)
        
        if performance:
            new_samples = len(self.training_data[model_name]) - performance.training_samples
            if new_samples >= config["retrain_threshold"]:
                self.logger.info(f"Retraining {model_name} model due to {new_samples} new samples")
                await self.train_model(model_name)
    
    async def get_model_performance(self, model_name: str = None) -> Union[ModelPerformance, Dict[str, ModelPerformance]]:
        """Get performance metrics for models."""
        if model_name:
            return self.model_performance.get(model_name)
        return self.model_performance.copy()
    
    async def get_feature_importance(self, model_name: str) -> Dict[str, float]:
        """Get feature importance for a model."""
        performance = self.model_performance.get(model_name)
        if performance:
            return performance.feature_importance
        return {}
    
    async def health_check(self) -> bool:
        """Perform health check on the predictive analytics module."""
        return (self.initialized and 
                len(self.models) > 0 and
                self.trend_analyzer is not None)
    
    async def cleanup(self) -> None:
        """Cleanup the predictive analytics module."""
        self.logger.info("Cleaning up Predictive Analytics Module...")
        self.models.clear()
        self.scalers.clear()
        self.encoders.clear()
        self.model_performance.clear()
        self.training_data.clear()
        self.predictions_cache.clear()
        self.initialized = False

#!/usr/bin/env python3
"""
PlexMovieAutomator/07_poster_and_qc_prep.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

"""
PlexMovieAutomator/07_poster_and_qc_prep.py

Purpose:
- Identifies movies with status "poster_pending".
- Consolidates the final MKV and subtitle files into a single QC-pending folder.
- Optionally fetches a poster image from TMDb.
- Updates the movie's status to "awaiting_quality_check".
- Cleans up intermediate staging folders for the processed movie.
"""

import os
import sys
import shutil
import logging
import time
import json
import asyncio
from pathlib import Path
from datetime import datetime, timezone
from typing import Optional, Dict, List, Any, Tuple

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

# --- Utility Imports ---
from utils.common_helpers import (
    load_settings,
    read_state_file,
    write_state_file,
    setup_logging,
    get_path_setting,
    get_setting,
    ensure_dir_exists,
    safe_move_file,
    safe_copy_file,
    safe_delete_folder
)
from utils.metadata_apis import get_tmdb_poster_full_url, download_image_from_url
from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase

# --- Global Logger ---
logger = None

# --- Helper Functions for This Stage ---

def _consolidate_files(movie: dict, settings: dict, sqlite_manager=None) -> tuple[bool, Path | None]:
    """
    Moves the final MKV and copies subtitles into a single QC-pending folder.
    Returns (True, path_to_qc_folder) on success, else (False, None).
    """
    movie_id = movie["unique_id"]
    title_log_str = f"'{movie.get('cleaned_title')}' ({movie_id})"
    
    # 1. Locate final MKV path from previous step
    final_mkv_path_str = movie.get("paths", {}).get("final_mkv_path")
    if not final_mkv_path_str or not Path(final_mkv_path_str).exists():
        logger.error(f"Final MKV path not found or file does not exist for {title_log_str}. Cannot consolidate.")
        if sqlite_manager:
            sqlite_manager.set_stage_marker(
                unique_id=movie_id,
                new_status="error_consolidation",
                error_message="Final MKV file missing before consolidation."
            )
        return False, None

    final_mkv_path = Path(final_mkv_path_str)
    
    # 2. Locate finalized subtitle files
    final_sub_paths_str = movie.get("final_subtitle_paths", [])
    subs_to_copy = [Path(p) for p in final_sub_paths_str if Path(p).exists()]
    if not subs_to_copy:
        logger.warning(f"No subtitle files found for {title_log_str}; proceeding without subtitles.")

    # 3. Define target QC-pending directory
    qc_base_dir = get_path_setting("Paths", "paths_qc_pending", settings_dict=settings, create_if_not_exist_dir=True)
    if not qc_base_dir: return False, None # Error already logged by get_path_setting
    
    resolution = movie.get("resolution", "unknown")
    target_qc_dir = qc_base_dir / resolution / final_mkv_path.parent.name # Use the "Movie Name (Year) {ID}" folder name
    
    if not ensure_dir_exists(target_qc_dir):
        logger.error(f"Failed to create target QC directory for {title_log_str}: {target_qc_dir}")
        if sqlite_manager:
            sqlite_manager.set_stage_marker(
                unique_id=movie_id,
                new_status="error_consolidation",
                error_message="Failed to create QC directory."
            )
        return False, None

    # 4. Move MKV into target_qc_dir, keeping its final name
    target_mkv_path = target_qc_dir / final_mkv_path.name
    if not safe_move_file(str(final_mkv_path), str(target_mkv_path)):
        logger.error(f"Failed to move final MKV to QC folder for {title_log_str}.")
        if sqlite_manager:
            sqlite_manager.set_stage_marker(
                unique_id=movie_id,
                new_status="error_consolidation",
                error_message="Failed to move MKV file."
            )
        return False, None
        
    # 5. Copy subtitle files into target_qc_dir
    for sub_path in subs_to_copy:
        safe_copy_file(str(sub_path), str(target_qc_dir / sub_path.name))
        
    logger.info(f"Successfully consolidated MKV and subtitles for {title_log_str} into {target_qc_dir}")
    return True, target_qc_dir


def _handle_poster(movie: dict, qc_movie_dir: Path, settings: dict) -> str | None:
    """
    Handles poster download/verification.
    Returns path to poster if successful/present, else None.
    """
    poster_filename = get_setting("PosterAndQCPrep", "poster_filename", default="poster.jpg", settings_dict=settings)
    target_poster_path = qc_movie_dir / poster_filename
    
    # Check if poster already exists (e.g., manually added)
    if target_poster_path.exists():
        logger.info(f"Poster already exists for '{movie['cleaned_title']}': {target_poster_path}")
        return str(target_poster_path)

    # If not, attempt to download if enabled
    if get_setting("PosterAndQCPrep", "poster_download_enabled", default=False, expected_type=bool, settings_dict=settings):
        logger.info(f"Attempting to download poster for '{movie['cleaned_title']}'...")
        poster_tmdb_path = movie.get("metadata_details", {}).get("poster_url_tmdb")
        if not poster_tmdb_path:
            logger.warning(f"No poster URL stored in metadata for '{movie['cleaned_title']}'. Cannot download poster.")
            return None
        
        poster_size = get_setting("PosterAndQCPrep", "poster_size", default="w500", settings_dict=settings)
        tmdb_api_key = get_setting("APIKeys", "tmdb_api_key", settings_dict=settings)
        
        # We need a helper in metadata_apis to construct the full URL
        full_poster_url = get_tmdb_poster_full_url(poster_tmdb_path, size=poster_size, settings_dict=settings)
        
        if download_image_from_url(full_poster_url, str(target_poster_path), logger_instance=logger):
            return str(target_poster_path)
        else:
            logger.error(f"Failed to download poster for '{movie['cleaned_title']}'.")
            return None
    else:
        logger.info(f"Automated poster download is disabled. Manual poster addition required for '{movie['cleaned_title']}'.")
        return None

# --- Main Stage Function (called by Orchestrator) ---

async def run_poster_qc_prep_stage(movies_data_list: list, settings: dict, main_logger: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 07: Intelligent poster handling and QC preparation with optimization.

    This stage operates purely with SQLite database - no legacy format conversion.
    Returns boolean success/failure instead of movies list.

    MCP Enhancements:
    - Sequential task breakdown for consolidation and QC operations
    - Memory-based learning for poster preferences and quality metrics
    - Intelligent error handling with automatic retry strategies
    - Performance tracking for consolidation operations
    - Quality metrics collection and analysis
    - SQLite-based state management for reliability

    Args:
        movies_data_list: Legacy parameter (ignored - stage works with SQLite directly)
        settings: Pipeline settings
        main_logger: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    global logger
    logger = main_logger

    logger.info("===== Starting MCP-Enhanced Stage 07: Poster & QC Prep with SQLite =====")

    # Initialize SQLite state manager
    workspace_root = Path.cwd()
    sqlite_manager = FilesystemFirstStateManager(workspace_root)

    # Sync database with filesystem at start of stage
    logger.info("Synchronizing database with filesystem...")
    sync_results = sqlite_manager.discover_movies_by_stage()
    if sync_results['status_corrections'] > 0:
        logger.info(f"Auto-corrected {sync_results['status_corrections']} movie statuses")

    # Initialize MCP services for this stage
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Create sequential thinking task for the entire QC preparation batch
    qc_batch_task_id = None
    if sequential_service:
        qc_batch_task_id = await sequential_service.create_task(
            task_type="poster_qc_prep_batch",
            movie_id="batch_poster_qc_prep",
            pipeline_stage="07_poster_and_qc_prep",
            custom_steps=[
                "Load QC preparation preferences from memory",
                "Identify movies for QC preparation",
                "Consolidate files with intelligent organization",
                "Handle poster download and optimization",
                "Cleanup intermediate files intelligently",
                "Update memory with QC preparation results"
            ]
        )
        if qc_batch_task_id:
            await sequential_service.start_task(qc_batch_task_id)
            logger.info(f"Created MCP sequential task: {qc_batch_task_id}")

    try:
        # --- 1. Load QC Preparation Preferences from Memory ---
        qc_preferences = {}
        poster_quality_patterns = {}

        if memory_service:
            qc_preferences = await memory_service.retrieve_memory(
                category="qc_preparation_preferences",
                key="user_defaults"
            ) or {
                "auto_download_posters": True,
                "poster_quality": "high",
                "cleanup_intermediate_files": True,
                "validate_final_files": True,
                "organize_by_resolution": True
            }

            poster_quality_patterns = await memory_service.retrieve_memory(
                category="poster_quality_patterns",
                key="historical_data"
            ) or {
                "successful_downloads": 0,
                "failed_downloads": 0,
                "average_download_time": 5.0,
                "preferred_sources": ["tmdb", "imdb"],
                "quality_preferences": {}
            }

            logger.info(f"Loaded QC preparation preferences and poster quality patterns from memory")

        # Get movies from SQLite database that need poster/QC prep
        poster_qc_states = ["poster_pending"]
        movies_to_process_sqlite = sqlite_manager.get_movies_by_statuses(poster_qc_states)

        # Work directly with SQLite data - no legacy conversion needed
        movies_to_process = movies_to_process_sqlite

        # Helper function to update movie status in SQLite
        def update_movie_status_sqlite(unique_id: str, new_status: str, error_message: str = None, **additional_data):
            try:
                success = sqlite_manager.set_stage_marker(
                    unique_id=unique_id,
                    new_status=new_status,
                    error_message=error_message,
                    additional_data=additional_data
                )
                if success:
                    logger.debug(f"Updated movie {unique_id} status to {new_status}")
                else:
                    logger.error(f"Failed to update movie {unique_id} status")
            except Exception as e:
                logger.error(f"Error updating movie status: {e}")

        if not movies_to_process:
            logger.info("No movies are currently pending poster/QC prep.")

            # Complete MCP task if no work to do
            if sequential_service and qc_batch_task_id:
                await sequential_service.complete_task(qc_batch_task_id)

            # Close SQLite connection
            sqlite_manager.close()

            return True

        logger.info(f"Found {len(movies_to_process)} movie(s) to prepare for Quality Check with MCP intelligence.")

        # MCP Enhancement: Track batch processing statistics
        batch_stats = {
            "start_time": datetime.now(timezone.utc).isoformat(),
            "total_movies": len(movies_to_process),
            "successful_consolidation": 0,
            "failed_consolidation": 0,
            "successful_poster_downloads": 0,
            "failed_poster_downloads": 0,
            "processing_times": [],
            "final_file_sizes": []
        }

        for movie in movies_to_process:
            movie_start_time = time.time()
            movie_id = movie["unique_id"]
            title_log_str = f"'{movie.get('cleaned_title')}' ({movie_id})"

            # MCP Enhancement: Create individual movie QC preparation task
            movie_task_id = None
            if sequential_service:
                movie_task_id = await sequential_service.create_task(
                    task_type="poster_qc_prep_single",
                    movie_id=movie_id,
                    pipeline_stage="07_poster_and_qc_prep",
                    custom_steps=[
                        "Consolidate final files",
                        "Download and optimize poster",
                        "Validate file integrity",
                        "Cleanup intermediate files",
                        "Update processing statistics"
                    ]
                )
                if movie_task_id:
                    await sequential_service.start_task(movie_task_id)

            update_movie_status_sqlite(movie_id, "poster_qc_prep_active")

            try:
                # Progress tracking
                if sequential_service and movie_task_id:
                    await sequential_service.update_task_progress(movie_task_id, 20, "Consolidating final files")

                # 1. Consolidate files into a temporary QC folder first
                consolidation_success, qc_dir_path = _consolidate_files(movie, settings, sqlite_manager)

                if not consolidation_success:
                    # _consolidate_files already updated the status to an error and logged it.
                    batch_stats["failed_consolidation"] += 1

                    if sequential_service and movie_task_id:
                        await sequential_service.fail_task(movie_task_id, "File consolidation failed")
                    continue

                batch_stats["successful_consolidation"] += 1

                # Progress tracking
                if sequential_service and movie_task_id:
                    await sequential_service.update_task_progress(movie_task_id, 50, "Handling poster download")

                # 2. Handle the poster with MCP intelligence
                poster_final_path = _handle_poster(movie, qc_dir_path, settings)

                # Track poster download success
                if poster_final_path:
                    batch_stats["successful_poster_downloads"] += 1

                    # MCP Enhancement: Store successful poster data
                    if memory_service:
                        await memory_service.store_memory(
                            category="poster_download_success",
                            key=f"poster_{movie_id}",
                            value={
                                "movie_id": movie_id,
                                "title": movie.get("cleaned_title"),
                                "poster_path": poster_final_path,
                                "timestamp": datetime.now(timezone.utc).isoformat()
                            },
                            tags=["poster", "success", "stage07"]
                        )
                else:
                    batch_stats["failed_poster_downloads"] += 1

                    # MCP Enhancement: Track poster failures
                    if memory_service:
                        await memory_service.store_memory(
                            category="poster_download_failures",
                            key=f"poster_fail_{movie_id}",
                            value={
                                "movie_id": movie_id,
                                "title": movie.get("cleaned_title"),
                                "timestamp": datetime.now(timezone.utc).isoformat()
                            },
                            tags=["poster", "failure", "stage07"]
                        )

                # 3. Calculate final file size for metrics
                if qc_dir_path and qc_dir_path.exists():
                    total_size = sum(f.stat().st_size for f in qc_dir_path.rglob('*') if f.is_file())
                    final_file_size_mb = total_size / (1024 * 1024)
                    batch_stats["final_file_sizes"].append(final_file_size_mb)
                else:
                    final_file_size_mb = 0

                # 4. Final status update and path recording with enhanced information
                processing_time = time.time() - movie_start_time

                updates = {
                    "status": "awaiting_quality_check",
                    "paths": {
                        **movie.get("paths",{}),
                        "qc_pending_path": str(qc_dir_path),
                        "poster_file": poster_final_path
                    },
                    "qc_preparation_metrics": {
                        "processing_time": processing_time,
                        "final_file_size_mb": final_file_size_mb,
                        "poster_downloaded": poster_final_path is not None
                    },
                    "last_updated_timestamp": datetime.now(timezone.utc).isoformat()
                }
                update_movie_status_sqlite(
                    movie_id,
                    "qc_ready",
                    final_file_size_mb=final_file_size_mb,
                    poster_downloaded=poster_final_path is not None,
                    paths={
                        **movie.get("paths", {}),
                        "qc_folder_path": str(qc_dir_path),
                        "poster_path": str(poster_final_path) if poster_final_path else None
                    },
                    last_updated_timestamp=datetime.now(timezone.utc).isoformat()
                )

            except Exception as e:
                logger.error(f"Error during QC preparation for {title_log_str}: {e}")
                batch_stats["failed_consolidation"] += 1

                # MCP Enhancement: Store processing errors
                if memory_service:
                    await memory_service.store_memory(
                        category="qc_preparation_errors",
                        key=f"error_{movie_id}_{int(time.time())}",
                        value={
                            "movie_id": movie_id,
                            "title": movie.get("cleaned_title"),
                            "error": str(e),
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        },
                        tags=["error", "stage07", "qc_prep"]
                    )

                if sequential_service and movie_task_id:
                    await sequential_service.fail_task(movie_task_id, f"QC preparation failed: {str(e)}")

                continue

            # 4. Cleanup old staging directories with MCP intelligence
            logger.info(f"Cleaning up intermediate staging directories for {title_log_str}...")

            # Track cleanup metrics
            cleanup_metrics = {
                "folders_removed": 0,
                "space_freed_mb": 0,
                "start_time": time.time()
            }

            # Progress tracking
            if sequential_service and movie_task_id:
                await sequential_service.update_task_progress(movie_task_id, 90, "Cleaning up intermediate files")

            # Path to the parent folder in workspace/2_... (contains _Processed_VideoAudio, _Processed_Subtitles)
            mkv_proc_output_folder = Path(movie["paths"].get("organized_mkv_ini", "")).parent
            if mkv_proc_output_folder.exists() and "2_mkv_processing_output" in str(mkv_proc_output_folder):
                # MCP Enhancement: Calculate space to be freed
                if memory_service:
                    folder_size_mb = sum(f.stat().st_size for f in mkv_proc_output_folder.rglob('*') if f.is_file()) / (1024 * 1024)
                    cleanup_metrics["space_freed_mb"] += folder_size_mb

                safe_delete_folder(str(mkv_proc_output_folder), ignore_errors=True)
                cleanup_metrics["folders_removed"] += 1

            # Path to the parent folder in workspace/5_... (contained the final MKV and final subs before consolidation)
            final_mux_output_folder = Path(movie["paths"].get("final_mkv_path", "")).parent
            if final_mux_output_folder.exists() and "5_subtitles_finalized_ready" in str(final_mux_output_folder):
                # MCP Enhancement: Calculate space to be freed
                if memory_service:
                    folder_size_mb = sum(f.stat().st_size for f in final_mux_output_folder.rglob('*') if f.is_file()) / (1024 * 1024)
                    cleanup_metrics["space_freed_mb"] += folder_size_mb

                safe_delete_folder(str(final_mux_output_folder), ignore_errors=True)
                cleanup_metrics["folders_removed"] += 1

            # MCP Enhancement: Store cleanup metrics
            cleanup_metrics["processing_time"] = time.time() - cleanup_metrics["start_time"]
            if memory_service:
                await memory_service.store_memory(
                    category="cleanup_metrics",
                    key=f"cleanup_{movie_id}",
                    value={
                        "movie_id": movie_id,
                        "title": movie.get("cleaned_title"),
                        "folders_removed": cleanup_metrics["folders_removed"],
                        "space_freed_mb": cleanup_metrics["space_freed_mb"],
                        "processing_time": cleanup_metrics["processing_time"],
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    },
                    tags=["cleanup", "stage07", "qc_prep"]
                )

            # Calculate final processing time
            processing_time = time.time() - movie_start_time
            batch_stats["processing_times"].append(processing_time)

            # Complete individual movie task
            if sequential_service and movie_task_id:
                await sequential_service.update_task_progress(movie_task_id, 100, "QC preparation completed successfully")
                await sequential_service.complete_task(movie_task_id)

            logger.info(f"Successfully prepared '{movie.get('cleaned_title')}' for Quality Check in {processing_time:.2f}s.")

    # MCP Enhancement: Update batch processing statistics
    batch_stats["end_time"] = datetime.now(timezone.utc).isoformat()
    batch_stats["total_processing_time"] = sum(batch_stats["processing_times"])
    batch_stats["average_processing_time"] = sum(batch_stats["processing_times"]) / len(batch_stats["processing_times"]) if batch_stats["processing_times"] else 0
    batch_stats["success_rate"] = (batch_stats["successful_consolidation"] / batch_stats["total_movies"] * 100) if batch_stats["total_movies"] > 0 else 0
    batch_stats["poster_success_rate"] = (batch_stats["successful_poster_downloads"] / (batch_stats["successful_poster_downloads"] + batch_stats["failed_poster_downloads"]) * 100) if (batch_stats["successful_poster_downloads"] + batch_stats["failed_poster_downloads"]) > 0 else 0
    batch_stats["average_file_size_mb"] = sum(batch_stats["final_file_sizes"]) / len(batch_stats["final_file_sizes"]) if batch_stats["final_file_sizes"] else 0

    if memory_service:
        await memory_service.store_memory(
            category="qc_preparation_batch_stats",
            key=f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            value=batch_stats,
            tags=["stats", "batch", "stage07", "qc_prep"]
        )

        logger.info(f"QC Preparation Statistics: {batch_stats['successful_consolidation']}/{batch_stats['total_movies']} successful ({batch_stats['success_rate']:.1f}%), Posters: {batch_stats['successful_poster_downloads']}/{batch_stats['successful_poster_downloads'] + batch_stats['failed_poster_downloads']} ({batch_stats['poster_success_rate']:.1f}%)")

        # Complete MCP batch task
        if sequential_service and qc_batch_task_id:
            await sequential_service.complete_task(qc_batch_task_id)
            logger.info(f"Completed MCP sequential task: {qc_batch_task_id}")

        logger.info("===== Finished MCP-Enhanced Stage 07: Poster & QC Prep =====")

        # Get final count for logging
        current_movies = sqlite_manager.get_all_movies()
        processed_movies = [m for m in current_movies if m.get('status') == 'awaiting_quality_check']
        logger.info(f"Stage completed successfully - {len(processed_movies)} movies processed")

        # Close SQLite connection
        sqlite_manager.close()

        return True

    except Exception as e:
        logger.error(f"Critical error in MCP-enhanced Stage 07: {e}")

        # MCP Enhancement: Handle stage-level errors
        if github_service:
            await github_service.create_issue(
                title=f"Stage 07 Critical Failure: {str(e)[:50]}...",
                body=f"**Stage 07 Critical Failure:**\n\n```\n{str(e)}\n```\n\n**Stage:** 07_poster_and_qc_prep\n**Timestamp:** {datetime.now(timezone.utc).isoformat()}\n\n**Impact:** Poster and QC preparation stage failed",
                labels=["critical", "stage-07", "pipeline-failure"]
            )

        if sequential_service and qc_batch_task_id:
            await sequential_service.fail_task(qc_batch_task_id, str(e))

        # Close SQLite connection on error
        try:
            if 'sqlite_manager' in locals():
                sqlite_manager.close()
        except:
            pass

        return False


# --- Standalone Execution Support ---
if __name__ == "__main__":
    """
    Standalone execution for Pipeline 5 - Poster and QC Prep
    Preserves all MCP capabilities while allowing manual testing
    """
    import json
    from pathlib import Path
    
    # Import settings loader with fallback
    try:
        from utils.common_helpers import load_settings
    except ImportError:
        import sys
        sys.path.insert(0, str(Path(__file__).parent / "_internal"))
        from utils.common_helpers import load_settings
    
    async def standalone_poster_qc_prep():
        # Setup logging for standalone execution
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        standalone_logger = logging.getLogger("standalone_pipeline_05")
        
        try:
            standalone_logger.info("===== Starting Standalone Pipeline 05 Execution =====")
            
            # Load settings
            settings_dict = load_settings("_internal/config/settings.ini")
            standalone_logger.info("Settings loaded successfully")
            
            # Load pipeline state
            pipeline_state_path = Path("_internal/config/movies_to_process.json")
            if not pipeline_state_path.exists():
                standalone_logger.warning("No pipeline state file found")
                return
            
            try:
                with open(pipeline_state_path, 'r', encoding='utf-8') as f:
                    movies_data = json.load(f)
            except Exception as e:
                standalone_logger.error(f"Could not read pipeline state: {e}")
                return
            
            standalone_logger.info(f"Loaded {len(movies_data)} movies from pipeline state")
            
            # Filter movies that need poster and QC prep
            poster_states = ["final_mux_completed", "poster_pending"]
            movies_to_process = [m for m in movies_data if m.get("status") in poster_states]
            
            if not movies_to_process:
                standalone_logger.info("No movies need poster and QC prep")
                standalone_logger.info("Current movie statuses:")
                for movie in movies_data:
                    title = movie.get("cleaned_title", "Unknown")
                    year = movie.get("year", "")
                    status = movie.get("status", "unknown")
                    standalone_logger.info(f"  - {title} ({year}): {status}")
                return
            
            standalone_logger.info(f"Processing {len(movies_to_process)} movies for poster and QC prep:")
            for movie in movies_to_process:
                title = movie.get("cleaned_title", "Unknown")
                year = movie.get("year", "")
                status = movie.get("status", "unknown")
                standalone_logger.info(f"  - {title} ({year}): {status}")
            
            # Process poster and QC prep (preserving MCP capabilities)
            updated_movies = await run_poster_qc_prep_stage(movies_data, settings_dict, standalone_logger, mcp_manager=None)
            
            # Save updated pipeline state
            with open(pipeline_state_path, 'w', encoding='utf-8') as f:
                json.dump(updated_movies, f, indent=2, ensure_ascii=False)
            
            standalone_logger.info(f"Saved updated pipeline state to: {pipeline_state_path}")
            
            # Show results
            completed_movies = [m for m in updated_movies if m.get("status") == "quality_check_pending"]
            if completed_movies:
                standalone_logger.info(f"🎉 {len(completed_movies)} movies completed poster and QC prep:")
                for movie in completed_movies:
                    title = movie.get("cleaned_title", "Unknown")
                    year = movie.get("year", "")
                    standalone_logger.info(f"  ✅ {title} ({year})")
            else:
                standalone_logger.info("No movies completed poster and QC prep in this run")
            
            standalone_logger.info("===== Finished Standalone Pipeline 05 Execution =====")
            
        except Exception as e:
            standalone_logger.error(f"Error in standalone execution: {e}", exc_info=True)
    
    # Run standalone poster and QC prep
    asyncio.run(standalone_poster_qc_prep())
"""
PlexMovieAutomator MCP Integration Package

This package provides Model Context Protocol (MCP) server integrations
for the Plex Movie Automator pipeline, enabling enhanced automation,
intelligent processing, and advanced monitoring capabilities.
"""

__version__ = "1.0.0"
__author__ = "Plex Movie Automator"

# MCP Server Integration Modules
from .mcp_manager import MCPManager
from .config_manager import MCPConfigManager

__all__ = [
    "MCPManager",
    "MCPConfigManager"
]

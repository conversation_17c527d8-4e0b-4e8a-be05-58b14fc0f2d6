/*
 * SPDX-FileCopyrightText: Copyright (c) 1993-2024 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef __GETOPT_H__
/**
 * DISCLAIMER
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is a part of the w64 mingw-runtime package.
 *
 * The w64 mingw-runtime package and its code is distributed in the hope that it
 * will be useful but WITHOUT ANY WARRANTY.  ALL WARRANTIES, EXPRESSED OR
 * IMPLIED ARE HEREBY DISCLAIMED.  This includes but is not limited to
 * warranties of MERCHANTAB<PERSON>ITY or FITNESS FOR A PARTICULAR PURPOSE.
 */

#define __GETOPT_H__

/* All the headers include this file. */
#include <crtdefs.h>

#if defined(WINGETOPT_SHARED_LIB)
#if defined(BUILDING_WINGETOPT_DLL)
#define WINGETOPT_API __declspec(dllexport)
#else
#define WINGETOPT_API __declspec(dllimport)
#endif
#else
#define WINGETOPT_API
#endif

#ifdef __cplusplus
extern "C"
{
#endif

    WINGETOPT_API extern int optind; /* index of first non-option in argv      */
    WINGETOPT_API extern int optopt; /* single option character, as parsed     */
    WINGETOPT_API extern int opterr; /* flag to enable built-in diagnostics... */
    /* (user may set to zero, to suppress)    */

    WINGETOPT_API extern char* optarg; /* pointer to argument of current option  */

    extern int getopt(int nargc, char* const* nargv, char const* options);

#ifdef _BSD_SOURCE
/*
 * BSD adds the non-standard `optreset' feature, for reinitialisation
 * of `getopt' parsing.  We support this feature, for applications which
 * proclaim their BSD heritage, before including this header; however,
 * to maintain portability, developers are advised to avoid it.
 */
#define optreset __mingw_optreset
    extern int optreset;
#endif
#ifdef __cplusplus
}
#endif
/*
 * POSIX requires the `getopt' API to be specified in `unistd.h';
 * thus, `unistd.h' includes this header.  However, we do not want
 * to expose the `getopt_long' or `getopt_long_only' APIs, when
 * included in this manner.  Thus, close the standard __GETOPT_H__
 * declarations block, and open an additional __GETOPT_LONG_H__
 * specific block, only when *not* __UNISTD_H_SOURCED__, in which
 * to declare the extended API.
 */
#endif /* !defined(__GETOPT_H__) */

#if !defined(__UNISTD_H_SOURCED__) && !defined(__GETOPT_LONG_H__)
#define __GETOPT_LONG_H__

#ifdef __cplusplus
extern "C"
{
#endif

    struct option /* specification for a long form option...	*/
    {
        char const* name; /* option name, without leading hyphens */
        int has_arg;      /* does it take an argument?		*/
        int* flag;        /* where to save its status, or NULL	*/
        int val;          /* its associated status value		*/
    };

    enum /* permitted values for its `has_arg' field...	*/
    {
        no_argument = 0,   /* option never takes an argument	*/
        required_argument, /* option always requires an argument	*/
        optional_argument  /* option may take an argument		*/
    };

    extern int getopt_long(
        int nargc, char* const* nargv, char const* options, const struct option* long_options, int* idx);
    extern int getopt_long_only(
        int nargc, char* const* nargv, char const* options, const struct option* long_options, int* idx);
/*
 * Previous MinGW implementation had...
 */
#ifndef HAVE_DECL_GETOPT
/*
 * ...for the long form API only; keep this for compatibility.
 */
#define HAVE_DECL_GETOPT 1
#endif

#ifdef __cplusplus
}
#endif

#endif /* !defined(__UNISTD_H_SOURCED__) && !defined(__GETOPT_LONG_H__) */

2025-07-20 12:53:59,966 - INFO - ============================================================
2025-07-20 12:53:59,966 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 12:53:59,966 - INFO - Timestamp: 2025-07-20T12:53:59.966198
2025-07-20 12:53:59,966 - INFO - ============================================================
2025-07-20 12:53:59,966 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 12:53:59,966 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 12:53:59,980 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 12:53:59,980 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 12:53:59,980 - INFO -   %4 (indexer_report_number): 
2025-07-20 12:53:59,980 - INFO -   %5 (category): movies
2025-07-20 12:53:59,980 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 12:53:59,980 - INFO -   %7 (post_process_status): 0
2025-07-20 12:53:59,980 - INFO - ========================================
2025-07-20 12:53:59,980 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 12:53:59,980 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 12:53:59,980 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py
2025-07-20 12:53:59,980 - ERROR - Post-processing failed
2025-07-20 17:56:34,631 - INFO - ============================================================
2025-07-20 17:56:34,631 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 17:56:34,631 - INFO - Timestamp: 2025-07-20T17:56:34.631405
2025-07-20 17:56:34,631 - INFO - ============================================================
2025-07-20 17:56:34,631 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 17:56:34,631 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 17:56:34,631 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 17:56:34,631 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 17:56:34,631 - INFO -   %4 (indexer_report_number): 
2025-07-20 17:56:34,631 - INFO -   %5 (category): movies
2025-07-20 17:56:34,631 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 17:56:34,631 - INFO -   %7 (post_process_status): 0
2025-07-20 17:56:34,631 - INFO - ========================================
2025-07-20 17:56:34,631 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 17:56:34,631 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 17:56:34,631 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py
2025-07-20 17:56:34,631 - ERROR - Post-processing failed
2025-07-20 18:37:07,202 - INFO - ============================================================
2025-07-20 18:37:07,202 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 18:37:07,202 - INFO - Timestamp: 2025-07-20T18:37:07.202614
2025-07-20 18:37:07,202 - INFO - ============================================================
2025-07-20 18:37:07,202 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 18:37:07,202 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:37:07,202 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 18:37:07,202 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:37:07,202 - INFO -   %4 (indexer_report_number): 
2025-07-20 18:37:07,202 - INFO -   %5 (category): movies
2025-07-20 18:37:07,202 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 18:37:07,202 - INFO -   %7 (post_process_status): 0
2025-07-20 18:37:07,202 - INFO - ========================================
2025-07-20 18:37:07,202 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:37:07,202 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:37:07,203 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py
2025-07-20 18:37:07,203 - ERROR - Post-processing failed
2025-07-20 18:59:25,444 - INFO - ============================================================
2025-07-20 18:59:25,444 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 18:59:25,444 - INFO - Timestamp: 2025-07-20T18:59:25.444779
2025-07-20 18:59:25,444 - INFO - ============================================================
2025-07-20 18:59:25,444 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 18:59:25,444 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:59:25,444 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 18:59:25,444 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:59:25,444 - INFO -   %4 (indexer_report_number): 
2025-07-20 18:59:25,444 - INFO -   %5 (category): movies
2025-07-20 18:59:25,444 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 18:59:25,444 - INFO -   %7 (post_process_status): 0
2025-07-20 18:59:25,445 - INFO - ========================================
2025-07-20 18:59:25,445 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:59:25,445 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 18:59:25,445 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py
2025-07-20 18:59:25,445 - ERROR - Post-processing failed
2025-07-20 23:26:02,103 - INFO - ============================================================
2025-07-20 23:26:02,103 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 23:26:02,103 - INFO - Timestamp: 2025-07-20T23:26:02.103733
2025-07-20 23:26:02,103 - INFO - ============================================================
2025-07-20 23:26:02,103 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 23:26:02,103 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:26:02,103 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 23:26:02,103 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:26:02,103 - INFO -   %4 (indexer_report_number): 
2025-07-20 23:26:02,103 - INFO -   %5 (category): movies
2025-07-20 23:26:02,103 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 23:26:02,103 - INFO -   %7 (post_process_status): 0
2025-07-20 23:26:02,103 - INFO - ========================================
2025-07-20 23:26:02,103 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:26:02,104 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:26:02,104 - ERROR - Orchestrator not found at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py
2025-07-20 23:26:02,104 - ERROR - Post-processing failed
2025-07-20 23:46:36,295 - INFO - ============================================================
2025-07-20 23:46:36,295 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 23:46:36,295 - INFO - Timestamp: 2025-07-20T23:46:36.295820
2025-07-20 23:46:36,295 - INFO - ============================================================
2025-07-20 23:46:36,295 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 23:46:36,295 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:46:36,295 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 23:46:36,295 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:46:36,295 - INFO -   %4 (indexer_report_number): 
2025-07-20 23:46:36,295 - INFO -   %5 (category): movies
2025-07-20 23:46:36,296 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 23:46:36,296 - INFO -   %7 (post_process_status): 0
2025-07-20 23:46:36,296 - INFO - ========================================
2025-07-20 23:46:36,296 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:46:36,296 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:46:36,296 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-20 23:46:36,395 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-20 23:46:36,395 - ERROR - Pipeline error: 2025-07-20 23:46:36,330 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:46:36,330 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:46:36,330 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:46:36,385 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-20 23:46:36,385 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:46:36,385 - ERROR - One or more stages failed

2025-07-20 23:46:36,395 - ERROR - Post-processing failed
2025-07-20 23:56:06,002 - INFO - ============================================================
2025-07-20 23:56:06,002 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-20 23:56:06,002 - INFO - Timestamp: 2025-07-20T23:56:06.002204
2025-07-20 23:56:06,002 - INFO - ============================================================
2025-07-20 23:56:06,002 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-20 23:56:06,002 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:56:06,002 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-20 23:56:06,002 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:56:06,002 - INFO -   %4 (indexer_report_number): 
2025-07-20 23:56:06,002 - INFO -   %5 (category): movies
2025-07-20 23:56:06,002 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-20 23:56:06,002 - INFO -   %7 (post_process_status): 0
2025-07-20 23:56:06,002 - INFO - ========================================
2025-07-20 23:56:06,002 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:56:06,002 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-20 23:56:06,002 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-20 23:56:06,103 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-20 23:56:06,103 - ERROR - Pipeline error: 2025-07-20 23:56:06,037 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-20 23:56:06,037 - INFO - Running Stage 02: Download and Organize
2025-07-20 23:56:06,037 - INFO - Starting Stage 02: Download and Organize
2025-07-20 23:56:06,094 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-20 23:56:06,094 - ERROR - Stage 02_download_and_organize failed
2025-07-20 23:56:06,094 - ERROR - One or more stages failed

2025-07-20 23:56:06,103 - ERROR - Post-processing failed
2025-07-21 00:31:22,293 - INFO - ============================================================
2025-07-21 00:31:22,293 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-21 00:31:22,293 - INFO - Timestamp: 2025-07-21T00:31:22.293676
2025-07-21 00:31:22,293 - INFO - ============================================================
2025-07-21 00:31:22,293 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-21 00:31:22,293 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 00:31:22,293 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-21 00:31:22,293 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 00:31:22,293 - INFO -   %4 (indexer_report_number): 
2025-07-21 00:31:22,293 - INFO -   %5 (category): movies
2025-07-21 00:31:22,293 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-21 00:31:22,293 - INFO -   %7 (post_process_status): 0
2025-07-21 00:31:22,293 - INFO - ========================================
2025-07-21 00:31:22,293 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 00:31:22,293 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 00:31:22,294 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-21 00:31:22,465 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-21 00:31:22,465 - ERROR - Pipeline error: 2025-07-21 00:31:22,335 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 00:31:22,335 - INFO - Running Stage 02: Download and Organize
2025-07-21 00:31:22,335 - INFO - Starting Stage 02: Download and Organize
2025-07-21 00:31:22,456 - ERROR - Error running Stage 02: 'charmap' codec can't encode characters in position 0-1: character maps to <undefined>
2025-07-21 00:31:22,457 - ERROR - Stage 02_download_and_organize failed
2025-07-21 00:31:22,457 - ERROR - One or more stages failed

2025-07-21 00:31:22,465 - ERROR - Post-processing failed
2025-07-21 01:29:24,967 - INFO - ============================================================
2025-07-21 01:29:24,967 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-21 01:29:24,967 - INFO - Timestamp: 2025-07-21T01:29:24.967159
2025-07-21 01:29:24,967 - INFO - ============================================================
2025-07-21 01:29:24,967 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-21 01:29:24,967 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 01:29:24,967 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-21 01:29:24,967 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 01:29:24,967 - INFO -   %4 (indexer_report_number): 
2025-07-21 01:29:24,967 - INFO -   %5 (category): movies
2025-07-21 01:29:24,967 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-21 01:29:24,967 - INFO -   %7 (post_process_status): 0
2025-07-21 01:29:24,967 - INFO - ========================================
2025-07-21 01:29:24,967 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 01:29:24,967 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 01:29:24,967 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-21 01:29:25,064 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-21 01:29:25,064 - ERROR - Pipeline error: 2025-07-21 01:29:25,002 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-21 01:29:25,002 - INFO - Running Stage 02: Download and Organize
2025-07-21 01:29:25,002 - INFO - Starting Stage 02: Download and Organize
2025-07-21 01:29:25,055 - ERROR - Error running Stage 02: expected 'except' or 'finally' block (02_download_and_organize.py, line 763)
2025-07-21 01:29:25,055 - ERROR - Stage 02_download_and_organize failed
2025-07-21 01:29:25,055 - ERROR - One or more stages failed

2025-07-21 01:29:25,065 - ERROR - Post-processing failed
2025-07-21 02:09:26,007 - INFO - ============================================================
2025-07-21 02:09:26,007 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-21 02:09:26,007 - INFO - Timestamp: 2025-07-21T02:09:26.007748
2025-07-21 02:09:26,007 - INFO - ============================================================
2025-07-21 02:09:26,007 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-21 02:09:26,007 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 02:09:26,007 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-21 02:09:26,007 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 02:09:26,007 - INFO -   %4 (indexer_report_number): 
2025-07-21 02:09:26,007 - INFO -   %5 (category): movies
2025-07-21 02:09:26,007 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-21 02:09:26,007 - INFO -   %7 (post_process_status): 0
2025-07-21 02:09:26,007 - INFO - ========================================
2025-07-21 02:09:26,008 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 02:09:26,008 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 02:09:26,008 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-21 02:09:34,534 - INFO - Pipeline stage 02 completed successfully
2025-07-21 02:09:34,534 - INFO - Pipeline output: INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\config\settings.ini
       Virtual environment not found, running with system Python

2025-07-21 02:09:34,535 - INFO - Post-processing completed successfully
2025-07-21 02:09:34,535 - INFO - Pipeline has been triggered to process the new download
2025-07-21 04:24:06,644 - INFO - ============================================================
2025-07-21 04:24:06,652 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-21 04:24:06,652 - INFO - Timestamp: 2025-07-21T04:24:06.652430
2025-07-21 04:24:06,652 - INFO - ============================================================
2025-07-21 04:24:06,652 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-21 04:24:06,652 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 04:24:06,652 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-21 04:24:06,652 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 04:24:06,652 - INFO -   %4 (indexer_report_number): 
2025-07-21 04:24:06,652 - INFO -   %5 (category): movies
2025-07-21 04:24:06,652 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-21 04:24:06,652 - INFO -   %7 (post_process_status): 0
2025-07-21 04:24:06,652 - INFO - ========================================
2025-07-21 04:24:06,652 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 04:24:06,652 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-21 04:24:06,652 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-21 04:24:07,530 - INFO - Pipeline stage 02 completed successfully
2025-07-21 04:24:07,530 - INFO - Pipeline output: INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\config\settings.ini
       Virtual environment not found, running with system Python

2025-07-21 04:24:07,530 - INFO - Post-processing completed successfully
2025-07-21 04:24:07,530 - INFO - Pipeline has been triggered to process the new download
2025-07-22 06:12:46,872 - INFO - ============================================================
2025-07-22 06:12:46,872 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-22 06:12:46,872 - INFO - Timestamp: 2025-07-22T06:12:46.872321
2025-07-22 06:12:46,872 - INFO - ============================================================
2025-07-22 06:12:46,872 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-22 06:12:46,872 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:12:46,872 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-22 06:12:46,872 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:12:46,872 - INFO -   %4 (indexer_report_number): 
2025-07-22 06:12:46,872 - INFO -   %5 (category): movies
2025-07-22 06:12:46,872 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-22 06:12:46,872 - INFO -   %7 (post_process_status): 0
2025-07-22 06:12:46,872 - INFO - ========================================
2025-07-22 06:12:46,872 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:12:46,872 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:12:46,872 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-22 06:12:47,927 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-22 06:12:47,927 - ERROR - Pipeline error: 2025-07-22 06:12:46,912 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-22 06:12:46,912 - INFO - Running Stage 02: Download and Organize
2025-07-22 06:12:46,912 - INFO - Starting Stage 02: Download and Organize
2025-07-22 06:12:47,652 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-22 06:12:47,652 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-22 06:12:47,665 - INFO - Synchronizing database with filesystem...
2025-07-22 06:12:47,666 - ERROR - Error running Stage 02: 'status_corrections'
2025-07-22 06:12:47,666 - ERROR - Stage 02_download_and_organize failed
2025-07-22 06:12:47,666 - ERROR - One or more stages failed

2025-07-22 06:12:47,927 - ERROR - Post-processing failed
2025-07-22 06:35:48,222 - INFO - ============================================================
2025-07-22 06:35:48,222 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-22 06:35:48,222 - INFO - Timestamp: 2025-07-22T06:35:48.222984
2025-07-22 06:35:48,223 - INFO - ============================================================
2025-07-22 06:35:48,223 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-22 06:35:48,223 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:35:48,223 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-22 06:35:48,223 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:35:48,223 - INFO -   %4 (indexer_report_number): 
2025-07-22 06:35:48,223 - INFO -   %5 (category): movies
2025-07-22 06:35:48,223 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-22 06:35:48,223 - INFO -   %7 (post_process_status): 0
2025-07-22 06:35:48,223 - INFO - ========================================
2025-07-22 06:35:48,223 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:35:48,223 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 06:35:48,223 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-22 06:35:49,175 - INFO - Pipeline stage 02 completed successfully
2025-07-22 06:35:49,175 - INFO - Pipeline output: INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\config\settings.ini
       Virtual environment not found, running with system Python

2025-07-22 06:35:49,175 - INFO - Post-processing completed successfully
2025-07-22 06:35:49,175 - INFO - Pipeline has been triggered to process the new download
2025-07-22 14:53:49,314 - INFO - ============================================================
2025-07-22 14:53:49,314 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-22 14:53:49,314 - INFO - Timestamp: 2025-07-22T14:53:49.314470
2025-07-22 14:53:49,314 - INFO - ============================================================
2025-07-22 14:53:49,314 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-22 14:53:49,314 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 14:53:49,314 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-22 14:53:49,314 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 14:53:49,314 - INFO -   %4 (indexer_report_number): 
2025-07-22 14:53:49,314 - INFO -   %5 (category): movies
2025-07-22 14:53:49,314 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-22 14:53:49,314 - INFO -   %7 (post_process_status): 0
2025-07-22 14:53:49,314 - INFO - ========================================
2025-07-22 14:53:49,314 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 14:53:49,314 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-22 14:53:49,314 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-22 14:53:50,347 - INFO - Pipeline stage 02 completed successfully
2025-07-22 14:53:50,347 - INFO - Pipeline output: INFO: Successfully loaded settings from: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\config\settings.ini
       Virtual environment not found, running with system Python

2025-07-22 14:53:50,347 - INFO - Post-processing completed successfully
2025-07-22 14:53:50,347 - INFO - Pipeline has been triggered to process the new download
2025-07-23 00:15:10,516 - INFO - ============================================================
2025-07-23 00:15:10,516 - INFO - SABnzbd Post-Process Bridge Script Started
2025-07-23 00:15:10,516 - INFO - Timestamp: 2025-07-23T00:15:10.516863
2025-07-23 00:15:10,516 - INFO - ============================================================
2025-07-23 00:15:10,516 - INFO - === SABnzbd Post-Process Parameters ===
2025-07-23 00:15:10,516 - INFO -   %1 (final_folder): C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:10,516 - INFO -   %2 (original_nzb_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON.nzb
2025-07-23 00:15:10,516 - INFO -   %3 (clean_job_name): The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:10,517 - INFO -   %4 (indexer_report_number): 
2025-07-23 00:15:10,517 - INFO -   %5 (category): movies
2025-07-23 00:15:10,517 - INFO -   %6 (group): alt.binaries.multimedia
2025-07-23 00:15:10,517 - INFO -   %7 (post_process_status): 0
2025-07-23 00:15:10,517 - INFO - ========================================
2025-07-23 00:15:10,517 - INFO - Download completed successfully: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:10,517 - INFO - Final folder: C:\Users\<USER>\Videos\PlexMovieAutomator\workspace\1_downloading\complete_raw\The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:10,517 - INFO - Triggering pipeline stage 02: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py --run-mode once --stages 02_download_and_organize
2025-07-23 00:15:11,424 - ERROR - Pipeline stage 02 failed with return code: 1
2025-07-23 00:15:11,424 - ERROR - Pipeline error: 2025-07-23 00:15:10,548 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-23 00:15:10,548 - INFO - Running Stage 02: Download and Organize
2025-07-23 00:15:10,548 - INFO - Starting Stage 02: Download and Organize
2025-07-23 00:15:10,626 - INFO - Orchestrator started - Mode: once, Stages: ['02_download_and_organize']
2025-07-23 00:15:10,626 - INFO - Running Stage 02: Download and Organize
2025-07-23 00:15:10,626 - INFO - Starting Stage 02: Download and Organize
2025-07-23 00:15:11,192 - INFO - ===== Starting Modern Radarr Download Monitoring with SQLite =====
2025-07-23 00:15:11,192 - INFO -      ENHANCED: Dual-detection system (Filesystem + Radarr API) + SQLite state
2025-07-23 00:15:11,193 - INFO - Initialized metadata database at: C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\data\movie_metadata.db
2025-07-23 00:15:11,193 - INFO - Discovering movies by scanning filesystem...
2025-07-23 00:15:11,195 - INFO - Found 3 movies across 13 stages
2025-07-23 00:15:11,195 - INFO -      SABnzbd complete directory: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,195 - INFO -      Radarr API endpoint: http://localhost:7878
2025-07-23 00:15:11,195 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-23 00:15:11,204 - INFO - Retrieved 1 movies from Radarr
2025-07-23 00:15:11,205 - INFO -      Active downloads in Radarr queue: 1
2025-07-23 00:15:11,207 - INFO - Found 0 movies in download states to monitor
2025-07-23 00:15:11,207 - INFO -      SMART STATE VALIDATION: Checking for inconsistent states...
2025-07-23 00:15:11,208 - INFO - Found 0 movies in download states to monitor
2025-07-23 00:15:11,208 - INFO - No movies currently in download states
2025-07-23 00:15:11,208 - INFO -      ENHANCED: Checking both Radarr API and filesystem for completed downloads
2025-07-23 00:15:11,208 - INFO -      Will scan SABnzbd directory: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,208 - INFO -      Found completed movie: Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON (28.95 GB)
2025-07-23 00:15:11,208 - INFO -      Found completed movie: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON (27.05 GB)
2025-07-23 00:15:11,208 - INFO -      FILESYSTEM DETECTION: Found 2 completed movies ready for organization
2025-07-23 00:15:11,208 - INFO -      ROBUST DETECTION: Scanning filesystem for completed downloads...
2025-07-23 00:15:11,208 - INFO -      Scanning for completed downloads in: workspace\1_downloading\complete_raw
2025-07-23 00:15:11,210 - INFO -      Trying to match: Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:11,210 - INFO -      Against 3 tracked movies
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 37: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 121, in <module>
    exit_code = main()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 105, in main
    stage_success = run_stage_02()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 67, in run_stage_02
    success = asyncio.run(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 770, in monitor_radarr_downloads
    completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 977, in find_completed_downloads_simple
    logger.info(f"    \u2705 MATCHED (cleaned+year): {cleaned_title} ({year}) -> {folder_name}")
Message: '    \u2705 MATCHED (cleaned+year): precious (2009) -> Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON'
Arguments: ()
2025-07-23 00:15:11,210 - INFO -     \u2705 MATCHED (cleaned+year): precious (2009) -> Precious (2009) 1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 37: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 121, in <module>
    exit_code = main()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 105, in main
    stage_success = run_stage_02()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 67, in run_stage_02
    success = asyncio.run(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 770, in monitor_radarr_downloads
    completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 999, in find_completed_downloads_simple
    logger.info(f"    \u2705 Found completed: {matched_movie.get('title')} ({matched_movie.get('year')}) - {size_gb:.2f} GB")
Message: '    \u2705 Found completed: None (2009) - 28.95 GB'
Arguments: ()
2025-07-23 00:15:11,255 - INFO -     \u2705 Found completed: None (2009) - 28.95 GB
2025-07-23 00:15:11,257 - INFO -      Trying to match: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
2025-07-23 00:15:11,257 - INFO -      Against 3 tracked movies
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u274c' in position 41: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 121, in <module>
    exit_code = main()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 105, in main
    stage_success = run_stage_02()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 67, in run_stage_02
    success = asyncio.run(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 770, in monitor_radarr_downloads
    completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 1002, in find_completed_downloads_simple
    logger.warning(f"     \u274c No database match for: {folder_name}")
Message: '     \u274c No database match for: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON'
Arguments: ()
2025-07-23 00:15:11,257 - WARNING -      \u274c No database match for: The.Matrix.1999.1080p.BluRay.REMUX.AVC.Atmos-EPSiLON
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f504' in position 38: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 121, in <module>
    exit_code = main()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 105, in main
    stage_success = run_stage_02()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 67, in run_stage_02
    success = asyncio.run(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 770, in monitor_radarr_downloads
    completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 1003, in find_completed_downloads_simple
    logger.info(f"     \U0001f504 REDUNDANCY: Auto-adding to tracking system...")
Message: '     \U0001f504 REDUNDANCY: Auto-adding to tracking system...'
Arguments: ()
2025-07-23 00:15:11,257 - INFO -      \U0001f504 REDUNDANCY: Auto-adding to tracking system...
--- Logging error ---
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\logging\__init__.py", line 1154, in emit
    stream.write(msg + self.terminator)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u2705' in position 37: character maps to <undefined>
Call stack:
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 121, in <module>
    exit_code = main()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 105, in main
    stage_success = run_stage_02()
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\src\main_pipeline_orchestrator.py", line 67, in run_stage_02
    success = asyncio.run(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 770, in monitor_radarr_downloads
    completed_matches = await find_completed_downloads_simple(sabnzbd_complete_dir, filesystem_manager, logger)
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 1033, in find_completed_downloads_simple
    logger.info(f"    \u2705 AUTO-ADDED: {extracted_title} ({extracted_year}) - {size_gb:.2f} GB")
Message: '    \u2705 AUTO-ADDED: The Matrix (1999) - 27.05 GB'
Arguments: ()
2025-07-23 00:15:11,258 - INFO -     \u2705 AUTO-ADDED: The Matrix (1999) - 27.05 GB
2025-07-23 00:15:11,259 - INFO -        Source: filesystem_fallback (no prior tracking required)
2025-07-23 00:15:11,259 - INFO - Found 2 completed downloads with database matches
2025-07-23 00:15:11,259 - INFO -      FILESYSTEM SCAN: Found 2 completed downloads with matched movies
2025-07-23 00:15:11,259 - ERROR - Critical error in download monitoring: 'unique_id'
Traceback (most recent call last):
  File "C:\Users\<USER>\Videos\PlexMovieAutomator\02_download_and_organize.py", line 782, in monitor_radarr_downloads
    movie_id = movie["unique_id"]
               ~~~~~^^^^^^^^^^^^^
KeyError: 'unique_id'
2025-07-23 00:15:11,260 - ERROR - Stage 02 failed
2025-07-23 00:15:11,260 - ERROR - Stage 02_download_and_organize failed
2025-07-23 00:15:11,260 - ERROR - One or more stages failed

2025-07-23 00:15:11,424 - ERROR - Post-processing failed

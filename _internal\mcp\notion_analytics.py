#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/notion_analytics.py

Notion Analytics Dashboard for Plex Movie Automator
Creates comprehensive Notion dashboards for performance analytics, success rates, processing trends, and quality metrics.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field

class NotionAnalyticsDashboard:
    """
    Comprehensive Notion analytics dashboard for the Plex Movie Automator.
    Creates and updates dashboards for performance analytics, success rates, processing trends, and quality metrics.
    """
    
    def __init__(self, notion_service, logger: logging.Logger):
        self.notion_service = notion_service
        self.logger = logger
        self.analytics_db_id = notion_service.analytics_db_id if notion_service else None
        self.initialized = False
        
        # Dashboard configuration
        self.dashboards = {
            "overview": {
                "name": "Pipeline Overview",
                "metrics": [
                    "total_movies",
                    "completed_movies",
                    "error_rate",
                    "average_processing_time",
                    "total_file_size"
                ]
            },
            "performance": {
                "name": "Performance Metrics",
                "metrics": [
                    "stage_processing_times",
                    "bottleneck_analysis",
                    "resource_usage",
                    "processing_throughput"
                ]
            },
            "quality": {
                "name": "Quality Metrics",
                "metrics": [
                    "resolution_distribution",
                    "subtitle_languages",
                    "audio_tracks",
                    "ocr_quality_scores"
                ]
            },
            "success_rates": {
                "name": "Success Rates",
                "metrics": [
                    "stage_success_rates",
                    "error_distribution",
                    "retry_effectiveness",
                    "failure_patterns"
                ]
            },
            "trends": {
                "name": "Processing Trends",
                "metrics": [
                    "daily_processing_volume",
                    "weekly_success_rate",
                    "monthly_quality_distribution",
                    "error_trend_analysis"
                ]
            }
        }
    
    async def initialize(self) -> bool:
        """Initialize the Notion analytics dashboard."""
        try:
            self.logger.info("Initializing Notion Analytics Dashboard...")
            
            if not self.notion_service or not self.notion_service.initialized:
                self.logger.error("Notion service not initialized")
                return False
            
            if not self.analytics_db_id:
                self.logger.warning("Analytics database ID not provided - dashboard creation disabled")
                return False
            
            # Verify analytics database exists
            analytics_db = self.notion_service.databases.get("analytics")
            if not analytics_db:
                self.logger.error("Analytics database not found")
                return False
            
            self.initialized = True
            self.logger.info("Notion Analytics Dashboard initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Notion Analytics Dashboard: {e}")
            return False
    
    async def create_or_update_dashboard(self, dashboard_type: str) -> bool:
        """Create or update a specific dashboard."""
        if not self.initialized:
            return False
        
        try:
            dashboard_config = self.dashboards.get(dashboard_type)
            if not dashboard_config:
                self.logger.error(f"Dashboard type not found: {dashboard_type}")
                return False
            
            self.logger.info(f"Creating/updating dashboard: {dashboard_config['name']}")
            
            # Get metrics for this dashboard
            metrics = await self._collect_metrics(dashboard_config["metrics"])
            
            # Create or update metrics in Notion
            for metric_name, metric_data in metrics.items():
                await self._create_or_update_metric(
                    metric_name=metric_name,
                    category=dashboard_type,
                    value=metric_data.get("value"),
                    unit=metric_data.get("unit", ""),
                    trend=metric_data.get("trend", "Stable"),
                    description=metric_data.get("description", ""),
                    chart_type=metric_data.get("chart_type", "Line")
                )
            
            self.logger.info(f"Dashboard updated: {dashboard_config['name']} with {len(metrics)} metrics")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating/updating dashboard {dashboard_type}: {e}")
            return False
    
    async def create_all_dashboards(self) -> bool:
        """Create or update all dashboards."""
        if not self.initialized:
            return False
        
        success = True
        for dashboard_type in self.dashboards.keys():
            dashboard_success = await self.create_or_update_dashboard(dashboard_type)
            if not dashboard_success:
                success = False
        
        return success
    
    async def _collect_metrics(self, metric_names: List[str]) -> Dict[str, Dict[str, Any]]:
        """Collect metrics for a dashboard."""
        metrics = {}
        
        # Get movie statistics from Notion integration
        movie_stats = await self._get_movie_statistics()
        
        # Get pipeline statistics
        pipeline_stats = await self._get_pipeline_statistics()
        
        # Process each requested metric
        for metric_name in metric_names:
            if metric_name == "total_movies":
                metrics[metric_name] = {
                    "value": movie_stats.get("total_movies", 0),
                    "unit": "movies",
                    "trend": self._calculate_trend(metric_name, movie_stats.get("total_movies", 0)),
                    "description": "Total number of movies in the pipeline",
                    "chart_type": "Number"
                }
            
            elif metric_name == "completed_movies":
                completed = movie_stats.get("by_status", {}).get("Complete", 0)
                metrics[metric_name] = {
                    "value": completed,
                    "unit": "movies",
                    "trend": self._calculate_trend(metric_name, completed),
                    "description": "Number of movies that completed processing",
                    "chart_type": "Number"
                }
            
            elif metric_name == "error_rate":
                error_rate = movie_stats.get("error_rate", 0)
                metrics[metric_name] = {
                    "value": error_rate,
                    "unit": "percent",
                    "trend": self._calculate_trend(metric_name, error_rate, inverse=True),
                    "description": "Percentage of movies with errors",
                    "chart_type": "Number"
                }
            
            elif metric_name == "average_processing_time":
                avg_time = movie_stats.get("average_processing_time", 0)
                metrics[metric_name] = {
                    "value": avg_time,
                    "unit": "minutes",
                    "trend": self._calculate_trend(metric_name, avg_time, inverse=True),
                    "description": "Average time to process a movie",
                    "chart_type": "Number"
                }
            
            elif metric_name == "total_file_size":
                total_size = movie_stats.get("total_file_size", 0)
                metrics[metric_name] = {
                    "value": total_size,
                    "unit": "GB",
                    "trend": self._calculate_trend(metric_name, total_size),
                    "description": "Total size of all processed movies",
                    "chart_type": "Number"
                }
            
            elif metric_name == "stage_processing_times":
                stage_times = pipeline_stats.get("stage_processing_times", {})
                for stage, time_value in stage_times.items():
                    metrics[f"processing_time_{stage}"] = {
                        "value": time_value,
                        "unit": "seconds",
                        "trend": self._calculate_trend(f"processing_time_{stage}", time_value, inverse=True),
                        "description": f"Average processing time for {stage}",
                        "chart_type": "Bar"
                    }
            
            elif metric_name == "stage_success_rates":
                success_rates = pipeline_stats.get("stage_success_rates", {})
                for stage, rate in success_rates.items():
                    metrics[f"success_rate_{stage}"] = {
                        "value": rate,
                        "unit": "percent",
                        "trend": self._calculate_trend(f"success_rate_{stage}", rate),
                        "description": f"Success rate for {stage}",
                        "chart_type": "Bar"
                    }
            
            elif metric_name == "resolution_distribution":
                by_quality = movie_stats.get("by_quality", {})
                for quality, count in by_quality.items():
                    metrics[f"resolution_{quality}"] = {
                        "value": count,
                        "unit": "movies",
                        "trend": "Stable",
                        "description": f"Number of movies in {quality}",
                        "chart_type": "Pie"
                    }
            
            # Add more metric calculations as needed
        
        return metrics
    
    async def _create_or_update_metric(self, metric_name: str, category: str, value: Any,
                                      unit: str = "", trend: str = "Stable", description: str = "",
                                      chart_type: str = "Line") -> bool:
        """Create or update a metric in the analytics database."""
        try:
            # Find existing metric
            existing_metric = await self._find_metric(metric_name, category)
            
            # Prepare properties
            properties = {
                "Metric": {"title": [{"text": {"content": metric_name}}]},
                "Category": {"select": {"name": category}},
                "Value": {"number": float(value) if value is not None else 0},
                "Unit": {"select": {"name": unit}},
                "Trend": {"select": {"name": trend}},
                "Date": {"date": {"start": datetime.now(timezone.utc).isoformat()}},
                "Chart Type": {"select": {"name": chart_type}},
                "Description": {"rich_text": [{"text": {"content": description}}]}
            }
            
            if existing_metric:
                # Update existing metric
                response = await self.notion_service._make_api_request(
                    method="PATCH",
                    endpoint=f"/pages/{existing_metric}",
                    data={"properties": properties}
                )
                
                return response and "id" in response
            else:
                # Create new metric
                response = await self.notion_service._make_api_request(
                    method="POST",
                    endpoint="/pages",
                    data={
                        "parent": {"database_id": self.analytics_db_id},
                        "properties": properties
                    }
                )
                
                return response and "id" in response
                
        except Exception as e:
            self.logger.error(f"Error creating/updating metric {metric_name}: {e}")
            return False
    
    async def _find_metric(self, metric_name: str, category: str) -> Optional[str]:
        """Find a metric in the analytics database."""
        try:
            response = await self.notion_service._make_api_request(
                method="POST",
                endpoint=f"/databases/{self.analytics_db_id}/query",
                data={
                    "filter": {
                        "and": [
                            {
                                "property": "Metric",
                                "title": {
                                    "equals": metric_name
                                }
                            },
                            {
                                "property": "Category",
                                "select": {
                                    "equals": category
                                }
                            }
                        ]
                    }
                }
            )
            
            if response and response.get("results"):
                return response["results"][0]["id"]
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding metric: {e}")
            return None
    
    async def _get_movie_statistics(self) -> Dict[str, Any]:
        """Get movie statistics from Notion integration."""
        try:
            # Get Notion pipeline integration
            notion_integration = getattr(self.notion_service, "notion_integration", None)
            if notion_integration:
                return await notion_integration.get_movie_statistics()
            
            # Fallback to empty stats
            return {
                "total_movies": 0,
                "by_status": {},
                "by_quality": {},
                "average_processing_time": 0,
                "total_file_size": 0,
                "error_rate": 0
            }
            
        except Exception as e:
            self.logger.error(f"Error getting movie statistics: {e}")
            return {}
    
    async def _get_pipeline_statistics(self) -> Dict[str, Any]:
        """Get pipeline statistics from memory service."""
        try:
            # Get memory service
            memory_service = self.notion_service.mcp_manager.services.get("memory_manager")
            if not memory_service:
                return {}
            
            # Get stage statistics
            stage_stats = {}
            for stage in ["stage01_intake", "stage02_download", "stage03_mkv", "stage04_subtitle", "stage04b_mux", "stage05_qc"]:
                stats = await memory_service.retrieve_memory("processing_stats", stage)
                if stats:
                    stage_stats[stage] = stats
            
            # Calculate derived statistics
            result = {
                "stage_processing_times": {},
                "stage_success_rates": {},
                "bottlenecks": [],
                "error_distribution": {}
            }
            
            # Process stage statistics
            for stage, stats in stage_stats.items():
                # Success rates
                total = stats.get("total_processed", 0)
                successful = stats.get("successful", 0)
                success_rate = (successful / total * 100) if total > 0 else 0
                result["stage_success_rates"][stage] = success_rate
                
                # Processing times
                if "average_processing_time" in stats:
                    result["stage_processing_times"][stage] = stats["average_processing_time"]
                
                # Error distribution
                failed = stats.get("failed", 0)
                if failed > 0:
                    result["error_distribution"][stage] = failed
            
            # Identify bottlenecks (stages with longest processing times)
            if result["stage_processing_times"]:
                sorted_stages = sorted(
                    result["stage_processing_times"].items(),
                    key=lambda x: x[1],
                    reverse=True
                )
                result["bottlenecks"] = [stage for stage, _ in sorted_stages[:2]]
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error getting pipeline statistics: {e}")
            return {}
    
    def _calculate_trend(self, metric_name: str, current_value: float, inverse: bool = False) -> str:
        """Calculate trend for a metric based on historical data."""
        # In a real implementation, this would compare with historical values
        # For now, return a placeholder
        return "Stable"
    
    async def generate_daily_report(self) -> bool:
        """Generate a daily report in Notion."""
        if not self.initialized:
            return False
        
        try:
            self.logger.info("Generating daily report...")
            
            # Get current date
            today = datetime.now(timezone.utc).date()
            yesterday = today - timedelta(days=1)
            
            # Create all dashboards
            await self.create_all_dashboards()
            
            # Create daily report page
            properties = {
                "Title": {"title": [{"text": {"content": f"Daily Report - {yesterday.isoformat()}"}}]},
                "Date": {"date": {"start": yesterday.isoformat()}},
                "Type": {"select": {"name": "Daily"}},
                "Status": {"select": {"name": "Generated"}}
            }
            
            response = await self.notion_service._make_api_request(
                method="POST",
                endpoint="/pages",
                data={
                    "parent": {"database_id": self.analytics_db_id},
                    "properties": properties,
                    "children": [
                        {
                            "object": "block",
                            "type": "heading_1",
                            "heading_1": {
                                "rich_text": [{"type": "text", "text": {"content": f"Pipeline Performance Report - {yesterday.isoformat()}"}}]
                            }
                        },
                        {
                            "object": "block",
                            "type": "paragraph",
                            "paragraph": {
                                "rich_text": [{"type": "text", "text": {"content": "Automatically generated daily report for the Plex Movie Automator pipeline."}}]
                            }
                        }
                        # Additional blocks would be added here
                    ]
                }
            )
            
            if response and "id" in response:
                self.logger.info(f"Daily report generated: {response['id']}")
                return True
            else:
                self.logger.error("Failed to generate daily report")
                return False
                
        except Exception as e:
            self.logger.error(f"Error generating daily report: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Perform health check on the dashboard."""
        return self.initialized and self.notion_service and self.notion_service.initialized
    
    async def cleanup(self) -> None:
        """Cleanup the dashboard."""
        self.logger.info("Cleaning up Notion Analytics Dashboard...")
        self.initialized = False

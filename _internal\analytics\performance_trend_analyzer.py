#!/usr/bin/env python3
"""
PlexMovieAutomator/src/analytics/performance_trend_analyzer.py

Advanced Performance Trend Analysis Engine
Creates advanced trend analysis with time-series data, seasonal patterns, performance forecasting, and bottleneck prediction.
"""

import asyncio
import json
import logging
import math
import statistics
import time
from collections import defaultdict, deque
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, NamedTuple
from dataclasses import dataclass, field
import numpy as np
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score

@dataclass
class TimeSeriesPoint:
    """Represents a single point in time series data."""
    timestamp: datetime
    value: float
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "timestamp": self.timestamp.isoformat(),
            "value": self.value,
            "metadata": self.metadata
        }

@dataclass
class TrendAnalysis:
    """Represents the result of trend analysis."""
    metric_name: str
    trend_direction: str  # "increasing", "decreasing", "stable"
    trend_strength: float  # 0.0 to 1.0
    slope: float
    r_squared: float
    confidence_interval: Tuple[float, float]
    seasonal_pattern: bool
    seasonal_period: Optional[int] = None
    forecast_points: List[TimeSeriesPoint] = field(default_factory=list)
    anomalies: List[TimeSeriesPoint] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "metric_name": self.metric_name,
            "trend_direction": self.trend_direction,
            "trend_strength": self.trend_strength,
            "slope": self.slope,
            "r_squared": self.r_squared,
            "confidence_interval": list(self.confidence_interval),
            "seasonal_pattern": self.seasonal_pattern,
            "seasonal_period": self.seasonal_period,
            "forecast_points": [p.to_dict() for p in self.forecast_points],
            "anomalies": [a.to_dict() for a in self.anomalies]
        }

@dataclass
class PerformanceBottleneck:
    """Represents a detected performance bottleneck."""
    stage: str
    severity: str  # "low", "medium", "high", "critical"
    impact_score: float  # 0.0 to 100.0
    description: str
    root_cause: str
    recommendations: List[str]
    affected_metrics: List[str]
    detection_time: datetime

    def to_dict(self) -> Dict[str, Any]:
        return {
            "stage": self.stage,
            "severity": self.severity,
            "impact_score": self.impact_score,
            "description": self.description,
            "root_cause": self.root_cause,
            "recommendations": self.recommendations,
            "affected_metrics": self.affected_metrics,
            "detection_time": self.detection_time.isoformat()
        }

class PerformanceTrendAnalyzer:
    """
    Advanced Performance Trend Analysis Engine for Plex Movie Automator.
    Provides time-series analysis, seasonal pattern detection, forecasting, and bottleneck prediction.
    """

    def __init__(self, memory_service, notion_service, logger: logging.Logger):
        self.memory_service = memory_service
        self.notion_service = notion_service
        self.logger = logger
        self.time_series_data: Dict[str, List[TimeSeriesPoint]] = defaultdict(list)
        self.trend_analyses: Dict[str, TrendAnalysis] = {}
        self.bottlenecks: List[PerformanceBottleneck] = []
        self.analysis_window_days = 30
        self.forecast_horizon_days = 7
        self.anomaly_threshold = 2.0  # Standard deviations
        self.min_data_points = 10
        self.initialized = False

        # Performance metrics to track
        self.tracked_metrics = {
            "processing_time": {
                "unit": "minutes",
                "higher_is_better": False,
                "critical_threshold": 60.0,
                "warning_threshold": 30.0
            },
            "success_rate": {
                "unit": "percent",
                "higher_is_better": True,
                "critical_threshold": 80.0,
                "warning_threshold": 90.0
            },
            "error_rate": {
                "unit": "percent",
                "higher_is_better": False,
                "critical_threshold": 20.0,
                "warning_threshold": 10.0
            },
            "throughput": {
                "unit": "movies/hour",
                "higher_is_better": True,
                "critical_threshold": 1.0,
                "warning_threshold": 2.0
            },
            "queue_length": {
                "unit": "movies",
                "higher_is_better": False,
                "critical_threshold": 50,
                "warning_threshold": 20
            },
            "resource_usage": {
                "unit": "percent",
                "higher_is_better": False,
                "critical_threshold": 90.0,
                "warning_threshold": 75.0
            }
        }

        # Stage-specific metrics
        self.stage_metrics = {
            "stage01_intake": ["processing_time", "success_rate", "error_rate"],
            "stage02_download": ["processing_time", "success_rate", "throughput", "queue_length"],
            "stage03_mkv": ["processing_time", "success_rate", "resource_usage"],
            "stage04_subtitle": ["processing_time", "success_rate", "ocr_quality"],
            "stage04b_mux": ["processing_time", "success_rate", "file_size"],
            "stage05_qc": ["processing_time", "success_rate", "quality_score"]
        }

    async def initialize(self) -> bool:
        """Initialize the performance trend analyzer."""
        try:
            self.logger.info("Initializing Performance Trend Analyzer...")

            if not self.memory_service:
                self.logger.error("Memory service not available")
                return False

            # Load historical data
            await self._load_historical_data()

            # Initialize trend analysis for all metrics
            await self._initialize_trend_analyses()

            self.initialized = True
            self.logger.info("Performance Trend Analyzer initialized successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize Performance Trend Analyzer: {e}")
            return False

    async def _load_historical_data(self) -> None:
        """Load historical performance data from memory service."""
        try:
            # Load processing statistics for each stage
            for stage in self.stage_metrics.keys():
                stage_memories = await self.memory_service.search_memories(
                    category="processing_stats",
                    tags=[stage]
                )

                for memory in stage_memories:
                    timestamp = datetime.fromisoformat(memory["created_time"])
                    stats = memory["value"]

                    # Extract metrics for this stage
                    for metric in self.stage_metrics[stage]:
                        if metric in stats:
                            point = TimeSeriesPoint(
                                timestamp=timestamp,
                                value=float(stats[metric]),
                                metadata={"stage": stage, "source": "memory"}
                            )
                            self.time_series_data[f"{stage}_{metric}"].append(point)

            # Load global metrics
            global_memories = await self.memory_service.search_memories(
                category="global_stats"
            )

            for memory in global_memories:
                timestamp = datetime.fromisoformat(memory["created_time"])
                stats = memory["value"]

                for metric_name, metric_value in stats.items():
                    if metric_name in self.tracked_metrics:
                        point = TimeSeriesPoint(
                            timestamp=timestamp,
                            value=float(metric_value),
                            metadata={"source": "global"}
                        )
                        self.time_series_data[f"global_{metric_name}"].append(point)

            # Sort all time series by timestamp
            for metric_name in self.time_series_data:
                self.time_series_data[metric_name].sort(key=lambda p: p.timestamp)

            total_points = sum(len(series) for series in self.time_series_data.values())
            self.logger.info(f"Loaded {total_points} historical data points across {len(self.time_series_data)} metrics")

        except Exception as e:
            self.logger.error(f"Error loading historical data: {e}")

    async def _initialize_trend_analyses(self) -> None:
        """Initialize trend analyses for all metrics with sufficient data."""
        for metric_name, data_points in self.time_series_data.items():
            if len(data_points) >= self.min_data_points:
                try:
                    analysis = await self._analyze_trend(metric_name, data_points)
                    if analysis:
                        self.trend_analyses[metric_name] = analysis
                except Exception as e:
                    self.logger.error(f"Error analyzing trend for {metric_name}: {e}")

    async def add_data_point(self, metric_name: str, value: float,
                           timestamp: datetime = None, metadata: Dict[str, Any] = None) -> None:
        """Add a new data point for trend analysis."""
        if not timestamp:
            timestamp = datetime.now(timezone.utc)

        point = TimeSeriesPoint(
            timestamp=timestamp,
            value=value,
            metadata=metadata or {}
        )

        self.time_series_data[metric_name].append(point)

        # Keep only data within the analysis window
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=self.analysis_window_days)
        self.time_series_data[metric_name] = [
            p for p in self.time_series_data[metric_name]
            if p.timestamp >= cutoff_time
        ]

        # Update trend analysis if we have enough data
        if len(self.time_series_data[metric_name]) >= self.min_data_points:
            try:
                analysis = await self._analyze_trend(metric_name, self.time_series_data[metric_name])
                if analysis:
                    self.trend_analyses[metric_name] = analysis

                    # Check for bottlenecks
                    await self._check_for_bottlenecks(metric_name, analysis)

            except Exception as e:
                self.logger.error(f"Error updating trend analysis for {metric_name}: {e}")

    async def _analyze_trend(self, metric_name: str, data_points: List[TimeSeriesPoint]) -> Optional[TrendAnalysis]:
        """Perform comprehensive trend analysis on a time series."""
        try:
            if len(data_points) < self.min_data_points:
                return None

            # Convert to numpy arrays for analysis
            timestamps = np.array([p.timestamp.timestamp() for p in data_points])
            values = np.array([p.value for p in data_points])

            # Normalize timestamps to start from 0
            timestamps = timestamps - timestamps[0]

            # Linear regression for trend
            X = timestamps.reshape(-1, 1)
            y = values

            model = LinearRegression()
            model.fit(X, y)

            slope = model.coef_[0]
            r_squared = model.score(X, y)

            # Determine trend direction and strength
            trend_direction = "stable"
            trend_strength = 0.0

            if abs(slope) > 0.001:  # Threshold for considering a trend
                trend_direction = "increasing" if slope > 0 else "decreasing"
                trend_strength = min(abs(slope) * 100, 1.0)  # Normalize to 0-1

            # Calculate confidence interval
            predictions = model.predict(X)
            mse = mean_squared_error(y, predictions)
            std_error = math.sqrt(mse)
            confidence_interval = (
                predictions[-1] - 1.96 * std_error,
                predictions[-1] + 1.96 * std_error
            )

            # Detect seasonal patterns
            seasonal_pattern, seasonal_period = self._detect_seasonal_pattern(values)

            # Generate forecast
            forecast_points = self._generate_forecast(
                model, timestamps[-1], values[-1], std_error
            )

            # Detect anomalies
            anomalies = self._detect_anomalies(data_points, values)

            return TrendAnalysis(
                metric_name=metric_name,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                slope=slope,
                r_squared=r_squared,
                confidence_interval=confidence_interval,
                seasonal_pattern=seasonal_pattern,
                seasonal_period=seasonal_period,
                forecast_points=forecast_points,
                anomalies=anomalies
            )

        except Exception as e:
            self.logger.error(f"Error in trend analysis for {metric_name}: {e}")
            return None

    def _detect_seasonal_pattern(self, values: np.ndarray) -> Tuple[bool, Optional[int]]:
        """Detect seasonal patterns in the data using autocorrelation."""
        try:
            if len(values) < 20:  # Need sufficient data for seasonal detection
                return False, None

            # Calculate autocorrelation for different lags
            max_lag = min(len(values) // 4, 24)  # Up to 24 hours or quarter of data
            autocorrelations = []

            for lag in range(1, max_lag + 1):
                if lag < len(values):
                    correlation = np.corrcoef(values[:-lag], values[lag:])[0, 1]
                    if not np.isnan(correlation):
                        autocorrelations.append((lag, abs(correlation)))

            if not autocorrelations:
                return False, None

            # Find the lag with highest autocorrelation
            best_lag, best_correlation = max(autocorrelations, key=lambda x: x[1])

            # Consider it seasonal if correlation is strong enough
            if best_correlation > 0.3:  # Threshold for seasonal pattern
                return True, best_lag

            return False, None

        except Exception as e:
            self.logger.error(f"Error detecting seasonal pattern: {e}")
            return False, None

    def _generate_forecast(self, model: LinearRegression, last_timestamp: float,
                          last_value: float, std_error: float) -> List[TimeSeriesPoint]:
        """Generate forecast points for the next period."""
        try:
            forecast_points = []
            forecast_seconds = self.forecast_horizon_days * 24 * 3600

            # Generate hourly forecasts
            for hours in range(1, self.forecast_horizon_days * 24 + 1):
                future_timestamp = last_timestamp + (hours * 3600)
                predicted_value = model.predict([[future_timestamp]])[0]

                # Add some uncertainty
                uncertainty = std_error * math.sqrt(hours / 24)  # Uncertainty increases with time

                forecast_point = TimeSeriesPoint(
                    timestamp=datetime.fromtimestamp(future_timestamp + last_timestamp, tz=timezone.utc),
                    value=predicted_value,
                    metadata={
                        "type": "forecast",
                        "uncertainty": uncertainty,
                        "confidence": max(0.1, 1.0 - (hours / (self.forecast_horizon_days * 24)))
                    }
                )
                forecast_points.append(forecast_point)

            return forecast_points

        except Exception as e:
            self.logger.error(f"Error generating forecast: {e}")
            return []

    def _detect_anomalies(self, data_points: List[TimeSeriesPoint],
                         values: np.ndarray) -> List[TimeSeriesPoint]:
        """Detect anomalies using statistical methods."""
        try:
            if len(values) < self.min_data_points:
                return []

            # Calculate z-scores
            mean_value = np.mean(values)
            std_value = np.std(values)

            if std_value == 0:
                return []

            z_scores = np.abs((values - mean_value) / std_value)

            # Find anomalies (points beyond threshold standard deviations)
            anomaly_indices = np.where(z_scores > self.anomaly_threshold)[0]

            anomalies = []
            for idx in anomaly_indices:
                anomaly_point = data_points[idx]
                anomaly_point.metadata.update({
                    "anomaly": True,
                    "z_score": float(z_scores[idx]),
                    "severity": "high" if z_scores[idx] > 3.0 else "medium"
                })
                anomalies.append(anomaly_point)

            return anomalies

        except Exception as e:
            self.logger.error(f"Error detecting anomalies: {e}")
            return []

    async def _check_for_bottlenecks(self, metric_name: str, analysis: TrendAnalysis) -> None:
        """Check for performance bottlenecks based on trend analysis."""
        try:
            # Extract stage and metric type from metric name
            parts = metric_name.split("_", 1)
            if len(parts) < 2:
                return

            stage = parts[0]
            metric_type = parts[1]

            # Get metric configuration
            metric_config = self.tracked_metrics.get(metric_type)
            if not metric_config:
                return

            # Get recent values for threshold checking
            recent_data = self.time_series_data[metric_name][-10:]  # Last 10 points
            if not recent_data:
                return

            recent_values = [p.value for p in recent_data]
            avg_recent_value = statistics.mean(recent_values)

            # Check for threshold violations
            bottleneck = None
            higher_is_better = metric_config["higher_is_better"]
            critical_threshold = metric_config["critical_threshold"]
            warning_threshold = metric_config["warning_threshold"]

            if higher_is_better:
                if avg_recent_value < critical_threshold:
                    severity = "critical"
                    impact_score = 90.0 + (10.0 * (critical_threshold - avg_recent_value) / critical_threshold)
                elif avg_recent_value < warning_threshold:
                    severity = "high"
                    impact_score = 70.0 + (20.0 * (warning_threshold - avg_recent_value) / warning_threshold)
                else:
                    severity = None
            else:
                if avg_recent_value > critical_threshold:
                    severity = "critical"
                    impact_score = 90.0 + (10.0 * (avg_recent_value - critical_threshold) / critical_threshold)
                elif avg_recent_value > warning_threshold:
                    severity = "high"
                    impact_score = 70.0 + (20.0 * (avg_recent_value - warning_threshold) / warning_threshold)
                else:
                    severity = None

            # Check for negative trends
            if analysis.trend_direction == "decreasing" and higher_is_better:
                if not severity or severity == "high":
                    severity = "medium" if not severity else severity
                    impact_score = max(impact_score if severity else 0, 50.0 + (analysis.trend_strength * 30.0))
            elif analysis.trend_direction == "increasing" and not higher_is_better:
                if not severity or severity == "high":
                    severity = "medium" if not severity else severity
                    impact_score = max(impact_score if severity else 0, 50.0 + (analysis.trend_strength * 30.0))

            # Create bottleneck if detected
            if severity:
                recommendations = self._generate_bottleneck_recommendations(
                    stage, metric_type, severity, analysis
                )

                bottleneck = PerformanceBottleneck(
                    stage=stage,
                    severity=severity,
                    impact_score=min(impact_score, 100.0),
                    description=self._generate_bottleneck_description(stage, metric_type, severity, avg_recent_value),
                    root_cause=self._analyze_root_cause(stage, metric_type, analysis),
                    recommendations=recommendations,
                    affected_metrics=[metric_name],
                    detection_time=datetime.now(timezone.utc)
                )

                # Add to bottlenecks list (remove duplicates)
                self.bottlenecks = [b for b in self.bottlenecks if not (b.stage == stage and metric_type in b.affected_metrics)]
                self.bottlenecks.append(bottleneck)

                self.logger.warning(f"Performance bottleneck detected: {bottleneck.description}")

                # Store bottleneck in memory for persistence
                await self._store_bottleneck(bottleneck)

        except Exception as e:
            self.logger.error(f"Error checking for bottlenecks: {e}")

    def _generate_bottleneck_description(self, stage: str, metric_type: str,
                                       severity: str, current_value: float) -> str:
        """Generate a human-readable description of the bottleneck."""
        stage_names = {
            "stage01": "Intake & NZB Search",
            "stage02": "Download & Organize",
            "stage03": "MKV Processing",
            "stage04": "Subtitle Processing",
            "stage04b": "Final Mux",
            "stage05": "Poster & QC"
        }

        metric_descriptions = {
            "processing_time": "processing time",
            "success_rate": "success rate",
            "error_rate": "error rate",
            "throughput": "throughput",
            "queue_length": "queue length",
            "resource_usage": "resource usage"
        }

        stage_name = stage_names.get(stage, stage)
        metric_desc = metric_descriptions.get(metric_type, metric_type)

        return f"{severity.title()} {metric_desc} issue in {stage_name} (current: {current_value:.2f})"

    def _analyze_root_cause(self, stage: str, metric_type: str, analysis: TrendAnalysis) -> str:
        """Analyze the root cause of a performance bottleneck."""
        root_causes = {
            ("stage01", "processing_time"): "Slow metadata lookup or NZB search response times",
            ("stage01", "success_rate"): "API failures or network connectivity issues",
            ("stage02", "processing_time"): "Slow download speeds or network congestion",
            ("stage02", "throughput"): "Download client limitations or bandwidth constraints",
            ("stage03", "processing_time"): "Complex video processing or insufficient CPU resources",
            ("stage03", "resource_usage"): "High CPU/memory usage during MKV processing",
            ("stage04", "processing_time"): "OCR processing bottlenecks or complex subtitle extraction",
            ("stage04", "success_rate"): "OCR service failures or poor image quality",
            ("stage04b", "processing_time"): "Muxing complexity or file size limitations",
            ("stage05", "processing_time"): "Poster download delays or file consolidation issues"
        }

        # Check for seasonal patterns
        if analysis.seasonal_pattern:
            return f"{root_causes.get((stage, metric_type), 'Unknown cause')} with seasonal variation (period: {analysis.seasonal_period})"

        # Check for trend-based causes
        if analysis.trend_direction != "stable":
            trend_causes = {
                "increasing": "Progressive degradation or increasing load",
                "decreasing": "System optimization or reduced load"
            }
            base_cause = root_causes.get((stage, metric_type), "Unknown cause")
            return f"{base_cause} showing {trend_causes[analysis.trend_direction]}"

        return root_causes.get((stage, metric_type), "Performance degradation detected")

    def _generate_bottleneck_recommendations(self, stage: str, metric_type: str,
                                           severity: str, analysis: TrendAnalysis) -> List[str]:
        """Generate optimization recommendations for a bottleneck."""
        recommendations = []

        # Stage-specific recommendations
        stage_recommendations = {
            "stage01": {
                "processing_time": [
                    "Optimize metadata API calls with caching",
                    "Implement parallel NZB searches",
                    "Add request timeout and retry logic"
                ],
                "success_rate": [
                    "Implement API failover mechanisms",
                    "Add network connectivity monitoring",
                    "Increase retry attempts with exponential backoff"
                ]
            },
            "stage02": {
                "processing_time": [
                    "Optimize download client settings",
                    "Implement bandwidth management",
                    "Add parallel download support"
                ],
                "throughput": [
                    "Increase concurrent download slots",
                    "Optimize download queue management",
                    "Implement priority-based processing"
                ]
            },
            "stage03": {
                "processing_time": [
                    "Optimize MKV processing parameters",
                    "Implement GPU acceleration if available",
                    "Add processing queue management"
                ],
                "resource_usage": [
                    "Limit concurrent MKV processing",
                    "Optimize memory usage patterns",
                    "Implement resource monitoring and throttling"
                ]
            },
            "stage04": {
                "processing_time": [
                    "Optimize OCR service selection",
                    "Implement parallel subtitle processing",
                    "Add OCR result caching"
                ],
                "success_rate": [
                    "Improve OCR preprocessing",
                    "Add fallback OCR services",
                    "Implement quality-based retry logic"
                ]
            },
            "stage04b": {
                "processing_time": [
                    "Optimize muxing parameters",
                    "Implement smart compression settings",
                    "Add parallel track processing"
                ]
            },
            "stage05": {
                "processing_time": [
                    "Optimize poster download sources",
                    "Implement file consolidation optimization",
                    "Add parallel QC operations"
                ]
            }
        }

        # Get stage-specific recommendations
        stage_recs = stage_recommendations.get(stage, {})
        metric_recs = stage_recs.get(metric_type, [])
        recommendations.extend(metric_recs)

        # Severity-specific recommendations
        if severity in ["critical", "high"]:
            recommendations.extend([
                "Consider scaling resources immediately",
                "Implement emergency throttling if needed",
                "Monitor closely for further degradation"
            ])

        # Trend-specific recommendations
        if analysis.trend_direction == "increasing" and metric_type in ["processing_time", "error_rate"]:
            recommendations.extend([
                "Investigate recent system changes",
                "Check for resource constraints",
                "Consider proactive scaling"
            ])
        elif analysis.trend_direction == "decreasing" and metric_type in ["success_rate", "throughput"]:
            recommendations.extend([
                "Identify and address root cause immediately",
                "Implement monitoring alerts",
                "Consider rollback if recent changes caused degradation"
            ])

        # Seasonal pattern recommendations
        if analysis.seasonal_pattern:
            recommendations.extend([
                f"Plan for seasonal variations (period: {analysis.seasonal_period})",
                "Implement predictive scaling based on patterns",
                "Adjust resource allocation for peak periods"
            ])

        return recommendations[:10]  # Limit to top 10 recommendations

    async def _store_bottleneck(self, bottleneck: PerformanceBottleneck) -> None:
        """Store bottleneck information in memory for persistence."""
        try:
            if self.memory_service:
                await self.memory_service.store_memory(
                    category="performance_bottlenecks",
                    key=f"{bottleneck.stage}_{bottleneck.detection_time.strftime('%Y%m%d_%H%M%S')}",
                    value=bottleneck.to_dict(),
                    tags=["bottleneck", bottleneck.stage, bottleneck.severity]
                )
        except Exception as e:
            self.logger.error(f"Error storing bottleneck: {e}")

    async def get_trend_analysis(self, metric_name: str) -> Optional[TrendAnalysis]:
        """Get trend analysis for a specific metric."""
        return self.trend_analyses.get(metric_name)

    async def get_all_trend_analyses(self) -> Dict[str, TrendAnalysis]:
        """Get all current trend analyses."""
        return self.trend_analyses.copy()

    async def get_active_bottlenecks(self, severity_filter: str = None) -> List[PerformanceBottleneck]:
        """Get currently active bottlenecks, optionally filtered by severity."""
        if severity_filter:
            return [b for b in self.bottlenecks if b.severity == severity_filter]
        return self.bottlenecks.copy()

    async def get_performance_forecast(self, metric_name: str, days: int = None) -> List[TimeSeriesPoint]:
        """Get performance forecast for a specific metric."""
        if days:
            self.forecast_horizon_days = days

        analysis = self.trend_analyses.get(metric_name)
        if analysis:
            return analysis.forecast_points
        return []

    async def get_anomalies(self, metric_name: str = None,
                           time_range_hours: int = 24) -> List[TimeSeriesPoint]:
        """Get detected anomalies, optionally filtered by metric and time range."""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=time_range_hours)
        anomalies = []

        if metric_name:
            analysis = self.trend_analyses.get(metric_name)
            if analysis:
                anomalies.extend([
                    a for a in analysis.anomalies
                    if a.timestamp >= cutoff_time
                ])
        else:
            for analysis in self.trend_analyses.values():
                anomalies.extend([
                    a for a in analysis.anomalies
                    if a.timestamp >= cutoff_time
                ])

        return sorted(anomalies, key=lambda a: a.timestamp, reverse=True)

    async def generate_performance_summary(self) -> Dict[str, Any]:
        """Generate a comprehensive performance summary."""
        try:
            summary = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metrics_tracked": len(self.time_series_data),
                "trend_analyses": len(self.trend_analyses),
                "active_bottlenecks": len(self.bottlenecks),
                "critical_bottlenecks": len([b for b in self.bottlenecks if b.severity == "critical"]),
                "trends": {},
                "bottlenecks": [b.to_dict() for b in self.bottlenecks],
                "forecasts": {},
                "anomalies": len(await self.get_anomalies()),
                "health_score": 0.0
            }

            # Add trend summaries
            for metric_name, analysis in self.trend_analyses.items():
                summary["trends"][metric_name] = {
                    "direction": analysis.trend_direction,
                    "strength": analysis.trend_strength,
                    "r_squared": analysis.r_squared,
                    "seasonal": analysis.seasonal_pattern
                }

            # Add forecast summaries
            for metric_name, analysis in self.trend_analyses.items():
                if analysis.forecast_points:
                    next_24h = [
                        p for p in analysis.forecast_points
                        if p.timestamp <= datetime.now(timezone.utc) + timedelta(hours=24)
                    ]
                    if next_24h:
                        summary["forecasts"][metric_name] = {
                            "next_24h_trend": next_24h[-1].value - next_24h[0].value,
                            "confidence": statistics.mean([
                                p.metadata.get("confidence", 0.5) for p in next_24h
                            ])
                        }

            # Calculate health score
            health_score = 100.0
            for bottleneck in self.bottlenecks:
                if bottleneck.severity == "critical":
                    health_score -= 25.0
                elif bottleneck.severity == "high":
                    health_score -= 15.0
                elif bottleneck.severity == "medium":
                    health_score -= 10.0
                else:
                    health_score -= 5.0

            summary["health_score"] = max(0.0, health_score)

            return summary

        except Exception as e:
            self.logger.error(f"Error generating performance summary: {e}")
            return {}

    async def health_check(self) -> bool:
        """Perform health check on the trend analyzer."""
        return (self.initialized and
                self.memory_service is not None and
                len(self.time_series_data) > 0)

    async def cleanup(self) -> None:
        """Cleanup the trend analyzer."""
        self.logger.info("Cleaning up Performance Trend Analyzer...")
        self.time_series_data.clear()
        self.trend_analyses.clear()
        self.bottlenecks.clear()
        self.initialized = False
#!/usr/bin/env python3
"""
PlexMovieAutomator/01_intake_and_nzb_search.py

Auto-activates virtual environment if not already active.
"""

import sys
import os
from pathlib import Path

# Auto-activate virtual environment
def ensure_venv():
    """Ensure we're running in the virtual environment"""
    # Get the current script directory
    root_dir = Path(__file__).parent
    venv_python = root_dir / "_internal" / "venv" / "Scripts" / "python.exe"

    # Check if we're already in venv or if venv doesn't exist
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # Already in virtual environment
        return

    if venv_python.exists():
        # Re-run this script with venv python
        import subprocess
        print(f"🔄 Activating virtual environment: {venv_python}")
        result = subprocess.run([str(venv_python)] + sys.argv, cwd=str(root_dir))
        sys.exit(result.returncode)
    else:
        print("⚠️ Virtual environment not found, running with system Python")

# Activate venv before any other imports
ensure_venv()

"""
PlexMovieAutomator/01_intake_and_nzb_search.py

FILESYSTEM-FIRST ARCHITECTURE:
Handles new movie requests, fetches metadata, and adds to Radarr.
Uses filesystem scanning for state discovery and metadata-only database.
No status tracking - Radarr manages downloads, Script 2 discovers completions.
"""
import sys
import time
import json
import logging
import asyncio
from pathlib import Path
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any

# Utility imports with fallback
sys.path.insert(0, str(Path(__file__).parent))

# Setup paths for clean imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent / "_internal"))

try:
    from utils.common_helpers import (
        get_path_setting,
        get_setting
    )
    from utils.metadata_apis import fetch_movie_metadata_for_intake
    from _internal.utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
except ImportError:
    # Fallback for testing
    from datetime import datetime, timezone, timedelta

    def get_path_setting(section, key, settings_dict=None, default=None):
        return settings_dict.get(section, {}).get(key, default) if settings_dict else default

    def get_setting(section, key, settings_dict=None, default=None, expected_type=str):
        return settings_dict.get(section, {}).get(key, default) if settings_dict else default

    # Import filesystem-first state manager for fallback
    import sys
    from pathlib import Path
    sys.path.insert(0, str(Path(__file__).parent / "_internal"))
    from utils.filesystem_first_state_manager import FilesystemFirstStateManager, MetadataOnlyDatabase
    
    async def fetch_movie_metadata_for_intake(raw_title, settings_dict=None):
        return {
            "tmdb_id": 27205,
            "title": "Inception",
            "cleaned_title": "Inception",
            "year": 2010,
            "imdb_id": None,
            "images": []
        }

# Modern API imports (replaced Selenium)
import aiohttp

# --- Modern Radarr API Functions (replaces Chrome driver workflow) ---

def _determine_quality_profile_by_year(year: int, logger_instance: logging.Logger, settings_dict: dict = None) -> dict:
    """
    Determine quality profile strategy based on movie year.

    Rules:
    - ≤2009: Largest 1080p only (NOT 4K, even if 4K is larger)
    - 2010-2015: BOTH largest 1080p AND largest 4K (download both)
    - 2016+: Largest 4K only

    Returns: {"profiles": [list of profile IDs], "strategy": str, "description": str}
    """
    # Get quality profile IDs from settings (with fallback defaults)
    radarr_preferences = settings_dict.get("Radarr", {}) if settings_dict else {}
    hd_profile_id = radarr_preferences.get("hd_quality_profile_id", 6)  # Default HD profile
    uhd_profile_id = radarr_preferences.get("uhd_quality_profile_id", 7)  # Default 4K profile

    if year <= 2009:
        # ≤2009: 1080p only - use HD profile
        strategy = {
            "profiles": [hd_profile_id],
            "strategy": "1080p_only",
            "description": f"≤2009 movie: Using 1080p only (Profile {hd_profile_id}) - largest file preferred"
        }
        logger_instance.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy

    elif 2010 <= year <= 2015:
        # 2010-2015: Both 1080p AND 4K - we'll add twice with different profiles
        strategy = {
            "profiles": [hd_profile_id, uhd_profile_id],
            "strategy": "both_1080p_and_4k",
            "description": f"2010-2015 movie: Will download BOTH 1080p (Profile {hd_profile_id}) and 4K (Profile {uhd_profile_id}) versions"
        }
        logger_instance.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy

    else:  # 2016+
        # 2016+: 4K only - use Ultra-HD profile
        strategy = {
            "profiles": [uhd_profile_id],
            "strategy": "4k_only",
            "description": f"2016+ movie: Using 4K only (Profile {uhd_profile_id}) - largest file preferred"
        }
        logger_instance.info(f"🎯 Quality Strategy for {year}: {strategy['description']}")
        return strategy


# [GEMINI-REFACTOR] Modified to accept the download directory directly for clarity and reliability.
async def _add_movie_to_radarr_modern(movie_data: dict, settings_dict: dict, logger_instance: logging.Logger) -> dict:
    """
    Modern replacement for Chrome driver NZB search: Add movie directly to Radarr via API.

    This replaces the entire Chrome driver + NZB scraping workflow with a single API call
    that leverages Radarr's built-in search capabilities and Prowlarr integration.

    Implements year-based quality selection:
    - ≤2009: Largest 1080p only
    - 2010-2015: BOTH 1080p AND 4K
    - 2016+: Largest 4K only

    Returns: {"success": bool, "reason": str, "radarr_id": int|None}
    """
    import aiohttp
    
    # Get Radarr configuration
    radarr_url = get_setting("Radarr", "url", settings_dict=settings_dict, default="http://localhost:7878")
    radarr_api_key = get_setting("Radarr", "api_key", settings_dict=settings_dict)
    
    if not radarr_api_key:
        logger_instance.error("Radarr API key not configured in settings")
        return {"success": False, "reason": "missing_api_key"}
    
    try:
        cleaned_title = movie_data.get("cleaned_title", "")
        year = movie_data.get("year", "")
        tmdb_id = movie_data.get("tmdb_id")
        
        if not cleaned_title or not year:
            return {"success": False, "reason": "insufficient_metadata"}
        
        search_term = f"{cleaned_title} {year}"
        headers = {'X-Api-Key': radarr_api_key}

        # Determine quality strategy based on year
        quality_strategy = _determine_quality_profile_by_year(year, logger_instance, settings_dict)

        async with aiohttp.ClientSession() as session:
            # Search for the movie first
            search_url = f"{radarr_url}/api/v3/movie/lookup"
            search_params = {"term": search_term}
            
            logger_instance.info(f"Searching Radarr for: {search_term}")
            
            async with session.get(search_url, params=search_params, headers=headers) as response:
                if response.status != 200:
                    return {"success": False, "reason": f"search_failed_status_{response.status}"}
                
                search_results = await response.json()
                
                if not search_results:
                    return {"success": False, "reason": "no_search_results"}
                
                # Find best match (prefer TMDB ID match, then title/year match)
                best_match = None
                if tmdb_id:
                    best_match = next((r for r in search_results if r.get('tmdbId') == tmdb_id), None)
                
                if not best_match:
                    # Fall back to first result (usually best match)
                    best_match = search_results[0]
                
                logger_instance.info(f"Found match: {best_match.get('title')} ({best_match.get('year')})")
            
            # Check if movie already exists in Radarr
            existing_url = f"{radarr_url}/api/v3/movie"
            async with session.get(existing_url, headers=headers) as response:
                if response.status == 200:
                    existing_movies = await response.json()
                    movie_tmdb_id = best_match.get('tmdbId')
                    
                    existing_movie = next((m for m in existing_movies if m.get('tmdbId') == movie_tmdb_id), None)
                    
                    if existing_movie:
                        # SMART CLEANUP: Check if movie actually exists in filesystem
                        logger_instance.info(f"🔍 Movie exists in Radarr, verifying filesystem reality: {search_term}")
                        
                        # Check complete_raw directory
                        complete_raw_dir = None
                        if "Paths" in settings_dict and "download_complete_raw_dir" in settings_dict["Paths"]:
                            raw_path = settings_dict["Paths"]["download_complete_raw_dir"]
                            if "%(download_client_active_dir)s" in raw_path:
                                base_dir = settings_dict["Paths"].get("download_client_active_dir", "workspace/1_downloading")
                                complete_raw_dir = Path(raw_path.replace("%(download_client_active_dir)s", base_dir))
                            else:
                                complete_raw_dir = Path(raw_path)
                        else:
                            complete_raw_dir = Path("workspace/1_downloading/complete_raw")
                        
                        # Check organized directories  
                        organized_dirs = []
                        if "Paths" in settings_dict and "mkv_processing_output_dir" in settings_dict["Paths"]:
                            mkv_base = Path(settings_dict["Paths"]["mkv_processing_output_dir"])
                            organized_dirs = [mkv_base / "1080p", mkv_base / "4K", mkv_base / "SD_or_unknown"]
                        else:
                            base_dir = Path("workspace/2_downloaded_and_organized")
                            organized_dirs = [base_dir / "1080p", base_dir / "4K", base_dir / "SD_or_unknown"]
                        
                        # Look for the movie in filesystem
                        movie_title = best_match.get('title', '').lower()
                        movie_year = str(best_match.get('year', ''))
                        found_in_filesystem = False
                        
                        # Check complete_raw
                        if complete_raw_dir.exists():
                            for item in complete_raw_dir.iterdir():
                                if item.is_dir():
                                    item_name = item.name.lower()
                                    if movie_title in item_name and movie_year in item_name:
                                        found_in_filesystem = True
                                        logger_instance.info(f"✅ Found in complete_raw: {item.name}")
                                        break
                        
                        # Check organized directories
                        if not found_in_filesystem:
                            for organized_dir in organized_dirs:
                                if organized_dir.exists():
                                    for movie_folder in organized_dir.iterdir():
                                        if movie_folder.is_dir():
                                            folder_name = movie_folder.name.lower()
                                            if movie_title in folder_name and movie_year in folder_name:
                                                found_in_filesystem = True
                                                logger_instance.info(f"✅ Found in organized: {movie_folder}")
                                                break
                                if found_in_filesystem:
                                    break
                        
                        if found_in_filesystem:
                            logger_instance.info(f"✅ Movie exists in Radarr and filesystem: {search_term}")
                            return {"success": True, "reason": "already_exists", "radarr_movie": existing_movie}
                        else:
                            # ORPHANED ENTRY: Exists in Radarr but not in filesystem
                            logger_instance.warning(f"🧹 ORPHANED: Movie exists in Radarr but NOT in filesystem: {search_term}")
                            logger_instance.info(f"🧹 Cleaning up orphaned Radarr entry to enable fresh download...")
                            
                            try:
                                # Simple cleanup: Remove the movie from Radarr
                                movie_id = existing_movie.get('id')
                                delete_url = f"{radarr_url}/api/v3/movie/{movie_id}?deleteFiles=false&addImportExclusion=false"
                                
                                async with session.delete(delete_url, headers=headers) as delete_response:
                                    if delete_response.status == 200:
                                        logger_instance.info(f"✅ Removed orphaned movie from Radarr: {search_term}")
                                        
                                        # 🔄 VERIFICATION: Wait and verify cleanup worked
                                        logger_instance.info(f"⏳ Waiting 2 seconds for Radarr to process cleanup...")
                                        await asyncio.sleep(2)
                                        
                                        # 🔍 COMPREHENSIVE VERIFICATION: Check both Radarr and SABnzbd
                                        logger_instance.info(f"🔍 Comprehensive verification: Checking Radarr + SABnzbd...")
                                        
                                        verification_success = True
                                        
                                        # 1. Verify movie is gone from Radarr
                                        async with session.get(existing_url, headers=headers) as verify_response:
                                            if verify_response.status == 200:
                                                updated_movies = await verify_response.json()
                                                still_exists = any(m.get('tmdbId') == movie_tmdb_id for m in updated_movies)
                                                
                                                if not still_exists:
                                                    logger_instance.info(f"✅ Radarr verification: Movie removed from library")
                                                else:
                                                    logger_instance.warning(f"⚠️ Radarr verification: Movie still exists after cleanup")
                                                    verification_success = False
                                            else:
                                                logger_instance.warning(f"Could not verify Radarr cleanup: HTTP {verify_response.status}")
                                                verification_success = False
                                        
                                        # 2. Verify movie is gone from Radarr queue
                                        queue_url = f"{radarr_url}/api/v3/queue"
                                        async with session.get(queue_url, headers=headers) as queue_response:
                                            if queue_response.status == 200:
                                                queue_data = await queue_response.json()
                                                queue_records = queue_data.get('records', [])
                                                
                                                # Check if any queue items match our movie title
                                                movie_title_lower = best_match.get('title', '').lower()
                                                queue_matches = [
                                                    item for item in queue_records 
                                                    if movie_title_lower in item.get('title', '').lower()
                                                ]
                                                
                                                if not queue_matches:
                                                    logger_instance.info(f"✅ Radarr queue verification: No queue items found")
                                                else:
                                                    logger_instance.warning(f"⚠️ Radarr queue verification: {len(queue_matches)} queue items still exist")
                                                    verification_success = False
                                            else:
                                                logger_instance.warning(f"Could not verify Radarr queue: HTTP {queue_response.status}")
                                        
                                        # 3. Verify movie is gone from SABnzbd history
                                        try:
                                            sabnzbd_api_key = settings_dict.get("DownloadAndOrganize", {}).get("sabnzbd_api_key")
                                            sabnzbd_url = settings_dict.get("DownloadAndOrganize", {}).get("sabnzbd_url", "http://127.0.0.1:8080")
                                            
                                            if sabnzbd_api_key:
                                                history_url = f"{sabnzbd_url}/api"
                                                params = {
                                                    "mode": "history",
                                                    "apikey": sabnzbd_api_key,
                                                    "output": "json",
                                                    "limit": 20
                                                }
                                                
                                                async with session.get(history_url, params=params) as sab_response:
                                                    if sab_response.status == 200:
                                                        history_data = await sab_response.json()
                                                        history_slots = history_data.get("history", {}).get("slots", [])
                                                        
                                                        # Check if any history entries match our movie
                                                        title_words = movie_title_lower.split()
                                                        year_str = str(best_match.get('year', ''))
                                                        
                                                        sab_matches = []
                                                        for entry in history_slots:
                                                            entry_name = entry.get("name", "").lower()
                                                            if all(word in entry_name for word in title_words) and year_str in entry_name:
                                                                sab_matches.append(entry)
                                                        
                                                        if not sab_matches:
                                                            logger_instance.info(f"✅ SABnzbd verification: No history entries found")
                                                        else:
                                                            logger_instance.warning(f"⚠️ SABnzbd verification: {len(sab_matches)} history entries still exist")
                                                            # Note: We might still proceed even if SABnzbd has history
                                                    else:
                                                        logger_instance.warning(f"Could not verify SABnzbd history: HTTP {sab_response.status}")
                                            else:
                                                logger_instance.info("No SABnzbd API key - skipping SABnzbd verification")
                                                
                                        except Exception as sab_error:
                                            logger_instance.warning(f"SABnzbd verification error: {sab_error}")
                                        
                                        # Final verification result
                                        if verification_success:
                                            logger_instance.info(f"✅ VERIFICATION COMPLETE: Movie fully removed, safe to proceed")
                                            logger_instance.info(f"🔄 Starting fresh download...")
                                        else:
                                            logger_instance.warning(f"⚠️ VERIFICATION ISSUES: Some cleanup may have failed")
                                            logger_instance.info(f"🔄 Proceeding anyway - Radarr may handle conflicts...")
                                        
                                    else:
                                        logger_instance.warning(f"⚠️ Failed to remove from Radarr: HTTP {delete_response.status}")
                                        logger_instance.info(f"🔄 Proceeding with download anyway...")
                                        
                            except Exception as cleanup_error:
                                logger_instance.error(f"Error during cleanup: {cleanup_error}")
                                logger_instance.info(f"🔄 Proceeding with download anyway...")
                        
                        # After this point, continue with normal add-to-Radarr logic
            
            # Add movie to Radarr with year-based quality strategy
            plex_movies_path = get_path_setting("Paths", "plex_movies_directory", settings_dict=settings_dict)
            if not plex_movies_path:
                plex_movies_path = Path("C:\\Users\\<USER>\\Videos\\plex")

            # Ensure Windows path format for Radarr
            root_folder_path = str(plex_movies_path).replace('/', '\\')

            # Use year-based quality strategy instead of single profile
            profiles_to_add = quality_strategy["profiles"]
            strategy_description = quality_strategy["description"]

            logger_instance.info(f"📋 {strategy_description}")
            logger_instance.info(f"🎬 Adding movie to Radarr with {len(profiles_to_add)} quality profile(s): {search_term}")

            added_movies = []

            # Add movie for each quality profile in the strategy
            for i, quality_profile_id in enumerate(profiles_to_add):
                profile_suffix = f" (Profile {quality_profile_id})" if len(profiles_to_add) > 1 else ""

                add_movie_data = {
                    "title": best_match["title"],
                    "year": best_match["year"],
                    "tmdbId": best_match["tmdbId"],
                    "images": best_match.get("images", []),
                    "qualityProfileId": quality_profile_id,
                    "monitored": True,
                    "minimumAvailability": "announced",
                    "rootFolderPath": root_folder_path,
                    "addOptions": {
                        "monitor": "movieOnly",
                        "searchForMovie": True  # This automatically searches and downloads!
                    }
                }

                logger_instance.info(f"   📥 Adding with quality profile {quality_profile_id}{profile_suffix}...")

                async with session.post(f"{radarr_url}/api/v3/movie",
                                      headers=headers,
                                      json=add_movie_data) as response:
                    if response.status in [200, 201]:
                        added_movie = await response.json()
                        added_movies.append(added_movie)
                        logger_instance.info(f"   ✅ Successfully added: {search_term} (ID: {added_movie.get('id')}, Profile: {quality_profile_id})")
                    else:
                        response_text = await response.text()
                        if "already exists" in response_text.lower():
                            logger_instance.warning(f"   ⚠️ Movie already exists in Radarr with profile {quality_profile_id}")
                        else:
                            logger_instance.error(f"   ❌ Failed to add with profile {quality_profile_id}: {response.status} - {response_text}")

            # Return success if at least one profile was added successfully
            if added_movies:
                return {
                    "success": True,
                    "reason": "successfully_added",
                    "radarr_movies": added_movies,
                    "radarr_id": added_movies[0].get("id"),  # Return first ID for compatibility
                    "quality_strategy": quality_strategy["strategy"],
                    "profiles_added": len(added_movies)
                }
            else:
                return {"success": False, "reason": "all_profiles_failed"}
                    
    except Exception as e:
        logger_instance.error(f"Exception in Radarr API call for {search_term}: {e}", exc_info=True)
        return {"success": False, "reason": f"exception_{str(e)}"}

# --- Legacy Chrome driver functions removed in modernization ---
# The following functions were replaced with direct Radarr API integration:
# - _perform_nzb_search_on_site() 
# - _attempt_nzb_download_from_results_page()
# 
# Modern approach: Movies are added directly to Radarr which automatically
# searches Prowlarr indexers and downloads via SABnzbd - no manual NZB scraping needed.

# --- MCP-Enhanced Main Stage Function (called by Orchestrator) ---
async def run_intake_and_search_stage(movies_data_list: list, settings_dict: dict, logger_instance: logging.Logger, mcp_manager=None) -> bool:
    """
    MCP-Enhanced Stage 01: Intelligent intake with sequential thinking, memory learning, and error handling.

    This stage operates purely with SQLite database - no legacy format conversion.
    Returns boolean success/failure instead of movies list.

    MCP Enhancements:
    - Sequential task breakdown for complex operations
    - Memory-based learning from previous intake patterns
    - Intelligent error handling with GitHub issue creation
    - Performance tracking and optimization suggestions
    - SQLite-based state management for reliability

    Args:
        movies_data_list: Legacy parameter (ignored - stage works with SQLite directly)
        settings_dict: Pipeline settings
        logger_instance: Logger instance
        mcp_manager: MCP manager instance

    Returns:
        bool: True if stage completed successfully, False otherwise
    """
    logger_instance.info("===== Starting MCP-Enhanced Stage 01: Intake and NZB Search =====")

    # Initialize filesystem-first state manager
    workspace_root = Path.cwd()
    filesystem_manager = FilesystemFirstStateManager(workspace_root)
    metadata_db = MetadataOnlyDatabase(workspace_root)

    # Discover current movies by scanning filesystem
    logger_instance.info("Discovering movies by scanning filesystem...")
    movies_by_stage = filesystem_manager.discover_movies_by_stage()
    logger_instance.info(f"Found movies in {len(movies_by_stage)} stages")

    # Initialize MCP services for this stage
    sequential_service = mcp_manager.services.get('sequential_thinking') if mcp_manager else None
    memory_service = mcp_manager.services.get('memory_manager') if mcp_manager else None
    github_service = mcp_manager.services.get('github_integration') if mcp_manager else None

    # Create sequential thinking task for the entire intake process
    intake_task_id = None
    if sequential_service:
        intake_task_id = await sequential_service.create_task(
            task_type="intake_and_nzb_search",
            movie_id="batch_intake",
            pipeline_stage="01_intake_and_nzb_search",
            custom_steps=[
                "Load intake preferences from memory",
                "Process new movie requests from file",
                "Fetch metadata with intelligent caching",
                "Learn from metadata patterns",
                "Search NZBs with optimization",
                "Download NZBs with retry logic",
                "Update memory with processing results"
            ]
        )
        if intake_task_id:
            await sequential_service.start_task(intake_task_id)
            logger_instance.info(f"Created MCP sequential task: {intake_task_id}")

    try:
        start_time = time.time()  # Track processing time for MCP analytics
        
        # --- 1. Load Intake Preferences from Memory ---
        intake_preferences = {}
        if memory_service:
            intake_preferences = await memory_service.retrieve_memory(
                category="intake_preferences",
                key="user_defaults"
            ) or {}
            logger_instance.info(f"Loaded intake preferences: {len(intake_preferences)} settings")

        # --- 2. Intake New Movie Requests from File ---
        raw_path = get_path_setting("General", "new_movie_requests_file", settings_dict=settings_dict)
        new_requests_file_path = Path(raw_path) if raw_path else None

        if new_requests_file_path and new_requests_file_path.exists():
            logger_instance.info(f"Processing new movie requests from: {new_requests_file_path}")
        elif not new_requests_file_path:
            logger_instance.warning("No new movie requests file configured in settings")
        elif not new_requests_file_path.exists():
            logger_instance.warning(f"New movie requests file not found: {new_requests_file_path}")

        if new_requests_file_path and new_requests_file_path.exists():
            remaining_lines_in_intake_file = []
            try:
                with open(new_requests_file_path, 'r', encoding='utf-8') as f:
                    raw_request_lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]

                for raw_title_input in raw_request_lines:
                    # Check for duplicates in metadata database (filesystem-first)
                    existing_movies = metadata_db.get_all_movies()
                    is_duplicate = any(movie.get("title", "").lower() == raw_title_input.lower() for movie in existing_movies)
                    if is_duplicate:
                        logger_instance.info(f"Skipping duplicate request from intake file: '{raw_title_input}'")
                        continue

                    logger_instance.info(f"Fetching metadata for new request: '{raw_title_input}'...")

                    # MCP Enhancement: Check memory for cached metadata
                    cached_metadata = None
                    if memory_service:
                        cached_metadata = await memory_service.retrieve_memory(
                            category="metadata_cache",
                            key=f"title_{raw_title_input.lower().replace(' ', '_')}"
                        )
                        if cached_metadata:
                            logger_instance.info(f"Found cached metadata for '{raw_title_input}'")

                    # Fetch metadata with intelligent caching
                    if cached_metadata and cached_metadata.get('timestamp'):
                        # Use cached metadata if it's recent (within 7 days)
                        from datetime import datetime, timedelta
                        cache_time = datetime.fromisoformat(cached_metadata['timestamp'])
                        if datetime.now(timezone.utc) - cache_time < timedelta(days=7):
                            metadata_result = cached_metadata['data']
                            logger_instance.info("Using cached metadata (fresh)")
                        else:
                            metadata_result = await fetch_movie_metadata_for_intake(raw_title_input, settings_dict=settings_dict)
                            # Update cache with fresh data
                            if memory_service and metadata_result:
                                await memory_service.store_memory(
                                    category="metadata_cache",
                                    key=f"title_{raw_title_input.lower().replace(' ', '_')}",
                                    value={
                                        'data': metadata_result,
                                        'timestamp': datetime.now(timezone.utc).isoformat()
                                    },
                                    tags=["metadata", "cache", "intake"]
                                )
                    else:
                        metadata_result = await fetch_movie_metadata_for_intake(raw_title_input, settings_dict=settings_dict)
                        # Cache new metadata
                        if memory_service and metadata_result:
                            await memory_service.store_memory(
                                category="metadata_cache",
                                key=f"title_{raw_title_input.lower().replace(' ', '_')}",
                                value={
                                    'data': metadata_result,
                                    'timestamp': datetime.now(timezone.utc).isoformat()
                                },
                                tags=["metadata", "cache", "intake"]
                            )

                    # FILESYSTEM-FIRST: Create movie entry in metadata-only database
                    # Use title-year identifier to match filesystem folder names
                    if metadata_result and not metadata_result.get("error"):
                        cleaned_title = metadata_result.get("title", raw_title_input)
                        year = metadata_result.get("year")
                        unique_id = f"{cleaned_title} ({year})" if year else cleaned_title
                        
                        # Check if movie already exists in metadata database
                        existing_movie = metadata_db.get_movie_metadata(unique_id)
                        if existing_movie:
                            logger_instance.info(f"Movie already exists in metadata database: {unique_id}")
                            continue

                        # Store metadata in database (NO STATUS - filesystem manages state)
                        success = metadata_db.save_movie_metadata(
                            unique_id=unique_id,
                            title=cleaned_title,
                            year=year,
                            tmdb_id=metadata_result.get("tmdb_id"),
                            imdb_id=metadata_result.get("imdb_id"),
                            metadata={
                                'raw_title_input': raw_title_input,
                                'request_timestamp': datetime.now(timezone.utc).isoformat(),
                                'mcp_task_id': intake_task_id,
                                'metadata_details': metadata_result
                            }
                        )
                        
                        if success:
                            logger_instance.info(f"Stored metadata for '{raw_title_input}': {unique_id}")
                        else:
                            logger_instance.warning(f"Failed to store metadata for '{raw_title_input}'")
                    else:
                        # No metadata found - log error but don't store in database
                        error_msg = metadata_result.get("error", "Unknown metadata lookup error.") if metadata_result else "No metadata result"
                        logger_instance.error(f"Metadata lookup failed for '{raw_title_input}': {error_msg}")
                        remaining_lines_in_intake_file.append(raw_title_input)

                # Update or clear the intake file
                with open(new_requests_file_path, 'w', encoding='utf-8') as f:
                    if remaining_lines_in_intake_file:
                        f.write('\n'.join(remaining_lines_in_intake_file))
                        logger_instance.info(f"Updated '{new_requests_file_path}' with titles that failed metadata lookup.")
                    else:
                        logger_instance.info(f"All requests from '{new_requests_file_path}' processed; file cleared.")
            except Exception as e:
                logger_instance.error(f"Error processing intake file '{new_requests_file_path}': {e}", exc_info=True)

        # --- 3. Learn from Metadata Patterns ---
        if memory_service:
            # Store successful metadata patterns for learning
            await memory_service.store_memory(
                category="metadata_patterns",
                key=f"success_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                value={
                    "stage": "intake_and_search",
                    "success": True,
                    "processing_time": time.time() - start_time if 'start_time' in locals() else 0
                },
                tags=["metadata", "pattern", "success"]
            )

        # --- 4. Modern Radarr API Integration (replaces NZB scraping) ---
        # FILESYSTEM-FIRST: Script 1 focuses only on metadata and Radarr integration
        # NO status management - Radarr handles downloads, Script 2 discovers completions
        
        # NOTE: In filesystem-first architecture, Script 1 doesn't track "pending_nzb_search"
        # Instead, it just processes the requests and adds to Radarr
        # Radarr handles the download state, and Script 2 discovers completed downloads
        
        logger_instance.info("Radarr integration: Movies added to Radarr will be tracked by Radarr's internal state")
        logger_instance.info("Download completion will be discovered by Script 2's filesystem scanning")

        # FILESYSTEM-FIRST: Script 1 has completed its core function
        # - Processed new movie requests from intake file
        # - Fetched metadata for each movie
        # - Stored metadata in database
        # - Added movies to Radarr for automatic downloading
        #
        # Next steps happen automatically:
        # - Radarr manages download state internally
        # - Script 2 will discover completed downloads via filesystem scanning
        # - No status tracking needed in Script 1 database
        
        logger_instance.info("✅ Script 1 completed: All new movie requests processed and added to Radarr")
        logger_instance.info("📁 Download monitoring will be handled by Script 2's filesystem scanning")
        
        return True

    except Exception as e:
        logger_instance.error(f"Critical error in intake and NZB search: {e}")
        if sequential_service:
            await sequential_service.fail_task(intake_task_id, f"Critical error: {str(e)}")
        return False
    finally:
        # FILESYSTEM-FIRST: Clean up resources
        if metadata_db and hasattr(metadata_db, 'close'):
            metadata_db.close()
        logger_instance.info("===== Finished Filesystem-First Stage 01: Intake and Metadata Storage =====")

        return True


# --- Standalone Execution Support ---
if __name__ == "__main__":
    """
    Allow the script to be run standalone for testing and manual execution.
    """
    import asyncio
    from pathlib import Path
    
    # Import the missing functions for standalone execution
    try:
        from utils.common_helpers import load_settings
        from utils.metadata_apis import fetch_movie_metadata_for_intake as metadata_function
    except ImportError:
        # Fallback import - add _internal to path
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent / "_internal"))
        from utils.common_helpers import load_settings
        from utils.metadata_apis import fetch_movie_metadata_for_intake as metadata_function
    
    # Setup logging for standalone execution
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    standalone_logger = logging.getLogger("standalone_pipeline_01")
    
    async def main():
        """FILESYSTEM-FIRST: Main standalone execution function"""
        filesystem_manager = None
        metadata_db = None
        try:
            standalone_logger.info("===== Starting Standalone Pipeline 01 Execution =====")

            # Initialize filesystem-first state manager
            workspace_root = Path.cwd()
            filesystem_manager = FilesystemFirstStateManager(workspace_root)
            metadata_db = MetadataOnlyDatabase(workspace_root)
            standalone_logger.info("Filesystem-first state manager initialized")

            # Discover movies by scanning filesystem
            movies_by_stage = filesystem_manager.discover_movies_by_stage()
            standalone_logger.info(f"Found movies in {len(movies_by_stage)} stages")

            # Load settings
            settings_dict = load_settings("_internal/config/settings.ini")
            standalone_logger.info("Settings loaded successfully")

            # Read movie requests file
            requests_file_path = get_path_setting("General", "new_movie_requests_file", settings_dict=settings_dict)
            if not requests_file_path:
                # Fallback to default path
                requests_file_path = Path("new_movie_requests.txt")
            elif isinstance(requests_file_path, str):
                requests_file_path = Path(requests_file_path)

            if not requests_file_path.exists():
                standalone_logger.error(f"Movie requests file not found: {requests_file_path}")
                return
                
            # Read and process movie requests
            with open(requests_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                raw_request_lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                
            standalone_logger.info(f"Found {len(raw_request_lines)} movie requests: {raw_request_lines}")
            
            if not raw_request_lines:
                standalone_logger.warning("No movie requests found in file")
                return
                
            # Process each movie request
            movies_data_list = []
            for raw_title in raw_request_lines:
                standalone_logger.info(f"Processing: {raw_title}")
                
                # Fetch metadata
                metadata = metadata_function(raw_title, settings_dict)
                standalone_logger.info(f"Metadata result: {metadata}")
                
                if metadata.get('success'):
                    standalone_logger.info(f"✅ Successfully found metadata for: {metadata.get('cleaned_title')} ({metadata.get('year')})")
                    
                    # Add to Radarr using the same function that worked in our test
                    movie_data = {
                        "cleaned_title": metadata.get('cleaned_title'),
                        "year": metadata.get('year'),
                        "tmdb_id": metadata.get('tmdb_id')
                    }
                    
                    result = await _add_movie_to_radarr_modern(movie_data, settings_dict, standalone_logger)
                    
                    if result.get('success'):
                        standalone_logger.info("✅ Successfully added movie to Radarr!")
                        if result.get('reason') == 'already_exists':
                            standalone_logger.info("🎬 Movie already exists in Radarr")
                        elif result.get('reason') == 'successfully_added':
                            standalone_logger.info("🎬 Movie was added to Radarr with automatic search enabled")
                            standalone_logger.info("📥 Radarr should now be searching for NZBs automatically")
                            
                        # Store metadata in database (filesystem-first: metadata only)
                        cleaned_title = metadata.get('cleaned_title')
                        year = metadata.get('year') 
                        tmdb_id = str(metadata.get('tmdb_id'))
                        title_year_id = f"{cleaned_title} ({year})"
                        
                        # Store using add_movie method (metadata only)
                        metadata_db.add_movie(
                            title_year_id=title_year_id,
                            tmdb_id=tmdb_id,
                            metadata={
                                **metadata,
                                'raw_title_input': raw_title,
                                'radarr_id': result.get('radarr_id')
                            }
                        )
                        standalone_logger.info(f"Stored metadata for: {title_year_id}")
                        
                    else:
                        standalone_logger.error(f"❌ Failed to add movie to Radarr: {result.get('reason', 'unknown error')}")
                        
                else:
                    standalone_logger.error(f"❌ Failed to get metadata for: {raw_title}")
            
            standalone_logger.info("===== Pipeline 01 Execution Complete =====")
            
        except Exception as e:
            standalone_logger.error(f"Critical error in standalone execution: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # Close connections if they exist
            if metadata_db:
                metadata_db.close()
    
    # Run the async main function
    asyncio.run(main())
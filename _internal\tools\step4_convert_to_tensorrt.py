#!/usr/bin/env python3
"""
Step 4: TensorRT Model Conversion Script
Convert NGC ONNX models to optimized TensorRT engines for RTX 5090
"""

import os
import sys
from pathlib import Path

def setup_tensorrt_environment():
    """Set up TensorRT environment"""
    tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
    current_path = os.environ.get('PATH', '')
    if tensorrt_lib_path not in current_path:
        os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"

def convert_onnx_to_tensorrt():
    """Convert ONNX models to TensorRT engines"""
    print("🔄 STEP 4: CONVERTING ONNX TO TENSORRT")
    print("=" * 60)
    print()
    
    # Set up environment
    setup_tensorrt_environment()
    
    # Model paths
    models_base = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    tensorrt_dir = models_base / "tensorrt"
    tensorrt_dir.mkdir(exist_ok=True)
    
    # ONNX model paths
    ocdnet_onnx = models_base / "ocdnet_v2.4" / "ocdnet_vdeployable_onnx_v2.4" / "ocdnet_fan_tiny_2x_icdar_pruned.onnx"
    ocrnet_onnx = models_base / "ocrnet_v2.1.1" / "ocrnet_vdeployable_v2.1.1" / "ocrnet-vit-pcb.onnx"
    
    # TensorRT engine paths
    ocdnet_trt = tensorrt_dir / "ocdnet_optimized.trt"
    ocrnet_trt = tensorrt_dir / "ocrnet_optimized.trt"
    
    print("📁 MODEL PATHS")
    print("-" * 40)
    print(f"ONNX Models Directory: {models_base}")
    print(f"TensorRT Output Directory: {tensorrt_dir}")
    print()
    
    # Check ONNX models exist
    print("📋 ONNX MODEL VERIFICATION")
    print("-" * 40)
    
    if ocdnet_onnx.exists():
        print(f"✅ OCDNet ONNX: Found")
        print(f"   📍 {ocdnet_onnx}")
    else:
        print(f"❌ OCDNet ONNX: Not found")
        print(f"   📍 Expected: {ocdnet_onnx}")
        return False
    
    if ocrnet_onnx.exists():
        print(f"✅ OCRNet ONNX: Found") 
        print(f"   📍 {ocrnet_onnx}")
    else:
        print(f"❌ OCRNet ONNX: Not found")
        print(f"   📍 Expected: {ocrnet_onnx}")
        return False
    
    print()
    
    # Test TensorRT import
    print("🔧 TENSORRT VERIFICATION")
    print("-" * 40)
    
    try:
        import tensorrt as trt
        print(f"✅ TensorRT imported successfully")
        print(f"   Version: {trt.__version__}")
        
        # Test basic TensorRT functionality
        logger = trt.Logger(trt.Logger.WARNING)
        builder = trt.Builder(logger)
        print(f"✅ TensorRT Builder created")
        print(f"   Platform has FP16: {builder.platform_has_fast_fp16}")
        print(f"   Platform has INT8: {builder.platform_has_fast_int8}")
        
    except Exception as e:
        print(f"❌ TensorRT verification failed: {e}")
        return False
    
    print()
    
    # Check existing TensorRT engines
    print("⚡ TENSORRT ENGINE STATUS")
    print("-" * 40)
    
    if ocdnet_trt.exists():
        print(f"✅ OCDNet TensorRT engine already exists")
        print(f"   📍 {ocdnet_trt}")
        print(f"   Size: {ocdnet_trt.stat().st_size / (1024*1024):.1f} MB")
    else:
        print(f"🔄 OCDNet TensorRT engine needs conversion")
    
    if ocrnet_trt.exists():
        print(f"✅ OCRNet TensorRT engine already exists")
        print(f"   📍 {ocrnet_trt}")
        print(f"   Size: {ocrnet_trt.stat().st_size / (1024*1024):.1f} MB")
    else:
        print(f"🔄 OCRNet TensorRT engine needs conversion")
    
    print()
    
    # Conversion using trtexec (NVIDIA's command-line tool)
    print("🚀 TENSORRT CONVERSION OPTIONS")
    print("-" * 40)
    print("We can convert using two methods:")
    print("1. Python TensorRT API (complex but customizable)")
    print("2. trtexec command-line tool (simple and reliable)")
    print()
    print("For Step 4, let's use trtexec commands:")
    print()
    
    # Generate trtexec commands
    print("📝 TRTEXEC CONVERSION COMMANDS")
    print("-" * 40)
    
    # OCDNet conversion command
    ocdnet_cmd = f'''trtexec \\
    --onnx="{ocdnet_onnx}" \\
    --saveEngine="{ocdnet_trt}" \\
    --fp16 \\
    --workspace=8192 \\
    --minShapes=input:1x3x720x1280 \\
    --optShapes=input:4x3x720x1280 \\
    --maxShapes=input:8x3x1080x1920 \\
    --verbose'''
    
    print("🔍 OCDNet (Text Detection):")
    print(ocdnet_cmd)
    print()
    
    # OCRNet conversion command  
    ocrnet_cmd = f'''trtexec \\
    --onnx="{ocrnet_onnx}" \\
    --saveEngine="{ocrnet_trt}" \\
    --fp16 \\
    --workspace=4096 \\
    --minShapes=input:1x1x64x200 \\
    --optShapes=input:16x1x64x200 \\
    --maxShapes=input:32x1x64x200 \\
    --verbose'''
    
    print("📝 OCRNet (Text Recognition):")
    print(ocrnet_cmd)
    print()
    
    # Alternative: Simple conversion without dynamic shapes
    print("🔄 SIMPLE CONVERSION COMMANDS (if above fails):")
    print("-" * 40)
    
    simple_ocdnet = f'trtexec --onnx="{ocdnet_onnx}" --saveEngine="{ocdnet_trt}" --fp16'
    simple_ocrnet = f'trtexec --onnx="{ocrnet_onnx}" --saveEngine="{ocrnet_trt}" --fp16'
    
    print(f"OCDNet: {simple_ocdnet}")
    print(f"OCRNet: {simple_ocrnet}")
    print()
    
    # Instructions
    print("📋 CONVERSION INSTRUCTIONS")
    print("-" * 40)
    print("1. Open PowerShell as Administrator")
    print("2. Navigate to your project directory")
    print("3. Run the trtexec commands above")
    print("4. This will create optimized TensorRT engines for your RTX 5090")
    print("5. Conversion may take 5-15 minutes per model")
    print()
    
    print("💡 PERFORMANCE OPTIMIZATIONS")
    print("-" * 40)
    print("• FP16 precision enabled for RTX 5090")
    print("• Dynamic batch sizes for efficiency")
    print("• Large workspace memory (4-8GB)")
    print("• Optimized for subtitle processing resolutions")
    print()
    
    return True

def test_tensorrt_engines():
    """Test if TensorRT engines are working"""
    print("🧪 TESTING TENSORRT ENGINES")
    print("-" * 40)
    
    models_base = Path(r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\models\ngc_ocr")
    tensorrt_dir = models_base / "tensorrt"
    
    ocdnet_trt = tensorrt_dir / "ocdnet_optimized.trt"
    ocrnet_trt = tensorrt_dir / "ocrnet_optimized.trt"
    
    engines_ready = ocdnet_trt.exists() and ocrnet_trt.exists()
    
    if engines_ready:
        print("✅ Both TensorRT engines found")
        print(f"   OCDNet: {ocdnet_trt.stat().st_size / (1024*1024):.1f} MB")
        print(f"   OCRNet: {ocrnet_trt.stat().st_size / (1024*1024):.1f} MB")
        print()
        print("🚀 READY FOR STEP 4 TEXT RECOGNITION!")
    else:
        print("⚠️  TensorRT engines not found")
        print("   Please run the trtexec conversion commands first")
    
    return engines_ready

if __name__ == "__main__":
    print("Step 4: TensorRT Model Conversion")
    print()
    
    # Run conversion setup
    success = convert_onnx_to_tensorrt()
    
    if success:
        print()
        # Test engines
        test_tensorrt_engines()
        
        print()
        print("🎯 NEXT STEPS:")
        print("1. Run the trtexec commands to create TensorRT engines")
        print("2. Test the engines with sample subtitle images")
        print("3. Integrate with the full pipeline")
        print("4. Benchmark performance on RTX 5090")

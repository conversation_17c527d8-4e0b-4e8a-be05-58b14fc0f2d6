# Optimized Pipeline Flow - No More Stage 4 Folder

## 🎯 **PROBLEM SOLVED**

The previous pipeline had a confusing `4_subtitles_converting` folder that created unnecessary complexity in the workflow. This has been **eliminated** for a cleaner, more logical flow.

## 🚀 **NEW STREAMLINED FLOW**

### **Stage-by-Stage Breakdown:**

1. **Stage 1 (Download)** - `01_intake_and_nzb_search.py`
   - Downloads movies to `1_downloading/`
   - Status: `downloading` → `download_completed`

2. **Stage 2 (Organize)** - `02_download_and_organize.py` 
   - Organizes files from `1_downloading/complete_raw/` 
   - Moves to `2_downloaded_and_organized/[movie]/`
   - Status: `download_completed` → `mkv_processing_pending`

3. **Stage 3 (Extract)** - `03_mkv_processor.py`
   - Processes MKV files from `2_downloaded_and_organized/[movie]/`
   - Extracts subtitles to `3_mkv_cleaned_subtitles_extracted/[movie]/subtitles/`
   - Moves video to `3_mkv_cleaned_subtitles_extracted/[movie]/`
   - Status: `mkv_processing_pending` → `subtitle_ocr_pending`

4. **Stage 4 (Encode)** - `04_video_encoder.py`
   - Encodes video from `3_mkv_cleaned_subtitles_extracted/[movie]/`
   - Saves encoded video to `4_ready_for_final_mux/[movie]/`
   - Status: `subtitle_ocr_pending` → `subtitle_ocr_pending` (unchanged - waits for subtitles)

5. **Stage 5 (Subtitles)** - `05_subtitle_handler.py` ⭐️ **KEY CHANGE**
   - **INPUT**: Reads subtitles from `3_mkv_cleaned_subtitles_extracted/[movie]/subtitles/`
   - **PROCESSING**: Converts PGS/SUP → SRT using MCP ImageSorcery 
   - **OUTPUT**: Stores final SRT paths in SQLite database
   - **NO FILE MOVEMENT**: Processes subtitles in place
   - Status: `subtitle_ocr_pending` → `final_mux_pending`

6. **Stage 6 (Final Mux)** - `06_final_mux.py`
   - **VIDEO**: Gets encoded video from `4_ready_for_final_mux/[movie]/`
   - **SUBTITLES**: Gets SRT path from SQLite database (still in stage 3 location)
   - **OUTPUT**: Creates final MKV in `4_ready_for_final_mux/[movie]/`
   - Status: `final_mux_pending` → `poster_download_pending`

7. **Stage 7 (Poster & QC)** - `07_poster_and_qc_prep.py`
   - Consolidates files to `6_awaiting_quality_check/[movie]/`
   - Downloads poster, prepares for final QC
   - Status: `poster_download_pending` → `quality_check_pending`

## 🔥 **KEY BENEFITS OF THIS FLOW**

### ✅ **No Unnecessary File Movement**
- Stage 5 processes subtitles **in place** 
- No copying to intermediate folders
- Faster processing, less disk usage

### ✅ **Clear Stage Responsibility** 
- **Stage 3**: Extract everything (video + subtitles)
- **Stage 4**: Encode video → Stage 5 destination
- **Stage 5**: Convert subtitles → SQLite paths
- **Stage 6**: Mux video + subtitles → Final file

### ✅ **Logical File Locations**
- Video processing: `3_mkv_cleaned_subtitles_extracted/` → `4_ready_for_final_mux/`
- Subtitle processing: `3_mkv_cleaned_subtitles_extracted/subtitles/` → SRT paths in database
- Final mux: Combines both using SQLite paths

### ✅ **Database-Driven Coordination**
- SQLite database tracks file paths
- Stages coordinate through status updates
- No confusion about "where files should be"

## 📁 **FINAL FOLDER STRUCTURE**

```
workspace/
├── 0_new_movie_requests/          # Stage 1 input
├── 1_downloading/                 # Stage 1 output
├── 2_downloaded_and_organized/    # Stage 2 output  
├── 3_mkv_cleaned_subtitles_extracted/  # Stage 3 output (video + subtitles)
│   └── [Movie]/
│       ├── movie.mkv
│       └── subtitles/
│           ├── movie.sup         # Stage 5 processes these in place
│           └── movie.ocr.srt     # Stage 5 creates these
├── 4_ready_for_final_mux/         # Stage 4 & 6 output
│   └── [Movie]/
│       ├── encoded_video.mkv     # From Stage 4
│       └── final_movie.mkv       # From Stage 6
├── 5_awaiting_poster/
├── 6_awaiting_quality_check/
├── 7_final_plex_ready/
└── issues_hold/
```

## 🎬 **PRACTICAL EXAMPLE**

**Movie**: "The Matrix (1999)"

1. **After Stage 3**: 
   - `3_mkv_cleaned_subtitles_extracted/The Matrix (1999)/movie.mkv`
   - `3_mkv_cleaned_subtitles_extracted/The Matrix (1999)/subtitles/movie.sup`

2. **After Stage 4**:
   - `4_ready_for_final_mux/The Matrix (1999)/encoded_video.mkv`
   - Subtitles still in stage 3 location

3. **After Stage 5**:
   - `3_mkv_cleaned_subtitles_extracted/The Matrix (1999)/subtitles/movie.ocr.srt`
   - SQLite database stores: `final_srt_path = "3_mkv_cleaned_subtitles_extracted/The Matrix (1999)/subtitles/movie.ocr.srt"`

4. **After Stage 6**:
   - `4_ready_for_final_mux/The Matrix (1999)/The Matrix (1999).mkv` (final file with video + audio + subtitles)

## 🚀 **RESULT**

**Perfect logical flow** with no unnecessary folders or file movements. Each stage has a clear purpose and knows exactly where to find its inputs and place its outputs.

**Your intuition was 100% correct** - Stage 4 folder was unnecessary complexity!

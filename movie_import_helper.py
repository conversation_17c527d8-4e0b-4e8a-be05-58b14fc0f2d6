#!/usr/bin/env python3
"""
Movie Import Helper - Add External Movies to Pipeline
====================================================

This script helps you add movies from external sources (like your manual Plex work)
into the automated pipeline by:

1. Scanning directories you specify for movie content
2. Analyzing what processing stage each movie is at
3. Moving/copying them to the correct pipeline stage folder
4. Creating the appropriate marker files so the scripts can pick them up

This is perfect for migrating existing movie collections into the new pipeline!

Usage Examples:
  python movie_import_helper.py --scan "C:\Movies\MyCollection" --dry-run
  python movie_import_helper.py --import "C:\Movies\Processed" --target-stage mkv_complete
  python movie_import_helper.py --analyze-folder "C:\Movies\Mixed"

Author: GitHub Copilot  
Date: July 2025
"""

import os
import sys
import json
import shutil
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('movie_import.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class MovieCandidate:
    """A movie found in external directories that could be imported"""
    source_directory: Path
    title: str
    year: str
    detected_resolution: str
    video_files: List[Path]
    subtitle_files: List[Path]
    audio_dirs: List[Path]
    subtitle_dirs: List[Path]
    estimated_stage: str
    recommended_target_stage: str
    recommended_markers: List[str]
    total_size_gb: float
    issues: List[str]


class MovieImportHelper:
    """Helper for importing external movies into the pipeline"""
    
    def __init__(self, workspace_path: str = "."):
        """Initialize the import helper"""
        self.workspace_path = Path(workspace_path)
        
        # Define pipeline stage directories
        self.stage_directories = {
            'raw_download': self.workspace_path / 'workspace' / '1_downloading' / 'complete_raw',
            'organized': self.workspace_path / 'workspace' / '2_downloaded_and_organized', 
            'mkv_complete': self.workspace_path / 'workspace' / '3_mkv_cleaned_subtitles_extracted',
            # Note: 4_subtitles_converting removed - Stage 5 works directly from stage 3 to stage 5
            'ready_for_mux': self.workspace_path / 'workspace' / '4_ready_for_final_mux',
            'awaiting_poster': self.workspace_path / 'workspace' / '5_awaiting_poster',
            'awaiting_qc': self.workspace_path / 'workspace' / '6_awaiting_quality_check',
            'final_ready': self.workspace_path / 'workspace' / '7_final_plex_ready'
        }
        
        # Create directories if they don't exist
        for stage_dir in self.stage_directories.values():
            for res_dir in ['1080p', '4k', '720p']:
                (stage_dir / res_dir).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Movie import helper initialized for workspace: {self.workspace_path}")

    def scan_external_directory(self, external_path: str) -> List[MovieCandidate]:
        """
        Scan an external directory for movies that could be imported
        
        Args:
            external_path: Path to scan for movies
            
        Returns:
            List of MovieCandidate objects
        """
        external_dir = Path(external_path)
        if not external_dir.exists():
            raise FileNotFoundError(f"Directory not found: {external_path}")
        
        print(f"Scanning for movies in: {external_dir}")
        print(f"{'='*60}")
        
        candidates = []
        
        # Look for movie directories
        for item in external_dir.iterdir():
            if item.is_dir():
                candidate = self.analyze_potential_movie(item)
                if candidate:
                    candidates.append(candidate)
        
        print(f"Found {len(candidates)} movie candidates")
        return candidates

    def analyze_potential_movie(self, directory: Path) -> Optional[MovieCandidate]:
        """
        Analyze a directory to see if it's a movie and what stage it's at
        
        Args:
            directory: Directory to analyze
            
        Returns:
            MovieCandidate if it's a movie, None otherwise
        """
        try:
            # Extract title and year
            title, year = self.extract_title_year(directory.name)
            if not title:
                return None  # Not a movie directory
            
            # Find video files
            video_extensions = {'.mkv', '.mp4', '.avi', '.m4v'}
            video_files = [f for f in directory.rglob('*') if f.suffix.lower() in video_extensions and f.is_file()]
            
            if not video_files:
                return None  # No video files found
            
            # Find subtitle files
            subtitle_files = list(directory.glob('*.srt')) + list(directory.glob('*.sup'))
            
            # Find processed directories
            audio_dirs = [d for d in directory.iterdir() if d.is_dir() and 'audio' in d.name.lower()]
            subtitle_dirs = [d for d in directory.iterdir() if d.is_dir() and 'subtitle' in d.name.lower()]
            
            # Detect resolution
            resolution = self.detect_resolution(directory, video_files)
            
            # Calculate total size
            total_size = sum(f.stat().st_size for f in video_files) / (1024**3)  # GB
            
            # Analyze processing stage
            stage_info = self.analyze_processing_stage(video_files, subtitle_files, audio_dirs, subtitle_dirs)
            
            return MovieCandidate(
                source_directory=directory,
                title=title,
                year=year,
                detected_resolution=resolution,
                video_files=video_files,
                subtitle_files=subtitle_files,
                audio_dirs=audio_dirs,
                subtitle_dirs=subtitle_dirs,
                estimated_stage=stage_info['current_stage'],
                recommended_target_stage=stage_info['target_stage'],
                recommended_markers=stage_info['markers'],
                total_size_gb=total_size,
                issues=stage_info['issues']
            )
            
        except Exception as e:
            logger.warning(f"Error analyzing {directory.name}: {e}")
            return None

    def extract_title_year(self, dir_name: str) -> Tuple[str, str]:
        """Extract movie title and year from directory name"""
        import re
        
        # Common patterns
        patterns = [
            r'^(.+?)\s*\((\d{4})\).*$',  # Title (Year)
            r'^(.+?)\s+(\d{4}).*$',      # Title Year
            r'^(.+?)\.(\d{4}).*$',       # Title.Year
        ]
        
        for pattern in patterns:
            match = re.match(pattern, dir_name)
            if match:
                title = match.group(1).strip().replace('.', ' ')
                year = match.group(2)
                return title, year
        
        # Check if it looks like a movie (contains common movie indicators)
        movie_indicators = ['bluray', 'bdrip', 'webrip', 'dvdrip', 'hdtv', '1080p', '720p', '4k']
        if any(indicator in dir_name.lower() for indicator in movie_indicators):
            # Remove common tags and try to extract title
            clean_name = dir_name
            for tag in movie_indicators + ['x264', 'x265', 'h264', 'h265']:
                clean_name = re.sub(rf'\b{tag}\b', '', clean_name, flags=re.IGNORECASE)
            
            # Try to find year in cleaned name
            year_match = re.search(r'\b(\d{4})\b', clean_name)
            if year_match:
                year = year_match.group(1)
                title = clean_name[:year_match.start()].strip(' .-_')
                return title, year
            else:
                return clean_name.strip(' .-_'), "unknown"
        
        return "", ""  # Not a movie directory

    def detect_resolution(self, directory: Path, video_files: List[Path]) -> str:
        """Detect video resolution from directory name or file analysis"""
        dir_name = directory.name.lower()
        
        # Check directory name first
        if '4k' in dir_name or '2160p' in dir_name:
            return '4k'
        elif '1080p' in dir_name:
            return '1080p'
        elif '720p' in dir_name:
            return '720p'
        
        # TODO: Could use ffprobe here to check actual video resolution
        # For now, default to 1080p
        return '1080p'

    def analyze_processing_stage(self, video_files: List[Path], subtitle_files: List[Path], 
                               audio_dirs: List[Path], subtitle_dirs: List[Path]) -> Dict:
        """
        Analyze what processing stage a movie is at based on its content
        
        Returns:
            Dictionary with stage analysis
        """
        issues = []
        
        # Analyze video processing
        has_processed_video = any('processed' in vf.name.lower() for vf in video_files)
        has_raw_video = any('processed' not in vf.name.lower() for vf in video_files)
        
        # Analyze subtitle processing
        has_srt = any(sf.suffix.lower() == '.srt' for sf in subtitle_files)
        has_sup = any(sf.suffix.lower() == '.sup' for sf in subtitle_files)
        has_subtitle_dirs = len(subtitle_dirs) > 0
        
        # Analyze audio processing
        has_audio_dirs = len(audio_dirs) > 0
        
        # Determine current stage and recommendations
        if has_processed_video and has_subtitle_dirs and has_audio_dirs:
            # Fully processed - ready for video encoding
            return {
                'current_stage': 'fully_processed',
                'target_stage': 'mkv_complete',
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        
        elif has_processed_video and (has_srt or has_subtitle_dirs):
            # Video and subtitles processed
            return {
                'current_stage': 'video_and_subtitles_done',
                'target_stage': 'mkv_complete', 
                'markers': ['.mkv_complete'],
                'issues': issues
            }
        
        elif has_processed_video:
            # Video processed, may need subtitle work
            if has_sup and not has_srt:
                issues.append("Has .sup files that may need conversion to .srt")
                return {
                    'current_stage': 'video_done_subtitles_pending',
                    'target_stage': 'ready_for_mux',  # Stage 5 now works directly to final mux
                    'markers': ['.subtitle_processing'],
                    'issues': issues
                }
            else:
                return {
                    'current_stage': 'video_done',
                    'target_stage': 'mkv_complete',
                    'markers': ['.mkv_complete'],
                    'issues': issues
                }
        
        elif has_raw_video and (has_audio_dirs or has_subtitle_dirs):
            # Raw video with some processing done
            return {
                'current_stage': 'partially_processed',
                'target_stage': 'organized',
                'markers': [],
                'issues': issues
            }
        
        elif has_raw_video:
            # Just raw video files
            return {
                'current_stage': 'raw_video_only',
                'target_stage': 'organized',
                'markers': [],
                'issues': issues
            }
        
        else:
            issues.append("No clear video files found")
            return {
                'current_stage': 'unknown',
                'target_stage': 'manual_review',
                'markers': [],
                'issues': issues
            }

    def show_import_preview(self, candidates: List[MovieCandidate]):
        """Show a preview of what would be imported"""
        print(f"\nIMPORT PREVIEW")
        print(f"{'='*80}")
        
        if not candidates:
            print("No movie candidates found for import.")
            return
        
        # Group by target stage
        by_stage = {}
        for candidate in candidates:
            stage = candidate.recommended_target_stage
            if stage not in by_stage:
                by_stage[stage] = []
            by_stage[stage].append(candidate)
        
        total_size = sum(c.total_size_gb for c in candidates)
        
        print(f"Found {len(candidates)} movies to import (Total: {total_size:.1f} GB)")
        print()
        
        for stage, movies in by_stage.items():
            print(f"TARGET STAGE: {stage.upper()}")
            print(f"{'-'*40}")
            
            for movie in movies:
                print(f"  🎬 {movie.title} ({movie.year}) [{movie.detected_resolution}] - {movie.total_size_gb:.1f} GB")
                print(f"      Source: {movie.source_directory.name}")
                print(f"      Current stage: {movie.estimated_stage}")
                
                if movie.video_files:
                    print(f"      Video files: {len(movie.video_files)}")
                if movie.subtitle_files:
                    print(f"      Subtitle files: {len(movie.subtitle_files)} (.srt: {sum(1 for f in movie.subtitle_files if f.suffix == '.srt')}, .sup: {sum(1 for f in movie.subtitle_files if f.suffix == '.sup')})")
                if movie.audio_dirs:
                    print(f"      Audio directories: {len(movie.audio_dirs)}")
                if movie.subtitle_dirs:
                    print(f"      Subtitle directories: {len(movie.subtitle_dirs)}")
                
                if movie.recommended_markers:
                    print(f"      Will create markers: {', '.join(movie.recommended_markers)}")
                
                if movie.issues:
                    print(f"      Issues:")
                    for issue in movie.issues:
                        print(f"        ⚠️  {issue}")
                
                print()

    def import_movies(self, candidates: List[MovieCandidate], dry_run: bool = True, 
                     copy_mode: bool = True) -> Dict[str, int]:
        """
        Import movies into the pipeline
        
        Args:
            candidates: List of movies to import
            dry_run: If True, only show what would be done
            copy_mode: If True, copy files; if False, move files
            
        Returns:
            Dictionary with import statistics
        """
        if dry_run:
            print(f"\n🔍 DRY RUN MODE - Showing what would be imported:")
        else:
            print(f"\n🚀 IMPORTING MOVIES...")
            
        print(f"{'='*60}")
        
        stats = {'successful': 0, 'failed': 0, 'skipped': 0}
        action_word = "copy" if copy_mode else "move"
        
        for i, candidate in enumerate(candidates, 1):
            print(f"\n{i}/{len(candidates)}: {candidate.title} ({candidate.year})")
            
            try:
                # Determine target directory
                target_stage_dir = self.stage_directories[candidate.recommended_target_stage]
                target_res_dir = target_stage_dir / candidate.detected_resolution.lower()
                target_movie_dir = target_res_dir / f"{candidate.title} ({candidate.year})"
                
                if dry_run:
                    print(f"  Would {action_word} to: {target_movie_dir}")
                    print(f"  Would create markers: {candidate.recommended_markers}")
                    stats['successful'] += 1
                else:
                    # Check if target already exists
                    if target_movie_dir.exists():
                        print(f"  ⚠️  Target already exists, skipping: {target_movie_dir.name}")
                        stats['skipped'] += 1
                        continue
                    
                    # Create target directory
                    target_movie_dir.mkdir(parents=True, exist_ok=True)
                    
                    # Copy or move files
                    if copy_mode:
                        shutil.copytree(candidate.source_directory, target_movie_dir, dirs_exist_ok=True)
                        print(f"  ✅ Copied to: {target_movie_dir}")
                    else:
                        shutil.move(str(candidate.source_directory), str(target_movie_dir))
                        print(f"  ✅ Moved to: {target_movie_dir}")
                    
                    # Create marker files
                    for marker in candidate.recommended_markers:
                        marker_file = target_movie_dir / marker
                        marker_data = {
                            'created_by': 'movie_import_helper',
                            'timestamp': datetime.now().isoformat(),
                            'source_directory': str(candidate.source_directory),
                            'detected_stage': candidate.estimated_stage,
                            'auto_imported': True
                        }
                        
                        with open(marker_file, 'w') as f:
                            json.dump(marker_data, f, indent=2)
                        
                        print(f"  📌 Created marker: {marker}")
                    
                    stats['successful'] += 1
                    
            except Exception as e:
                logger.error(f"Failed to import {candidate.title}: {e}")
                print(f"  ❌ Error: {e}")
                stats['failed'] += 1
        
        # Show summary
        print(f"\n{'='*60}")
        print(f"IMPORT SUMMARY:")
        if dry_run:
            print(f"  Would import: {stats['successful']}")
        else:
            print(f"  Successfully imported: {stats['successful']}")
            print(f"  Failed: {stats['failed']}")
            print(f"  Skipped (already exists): {stats['skipped']}")
        
        return stats

    def prompt_yes_no(self, question: str, default: bool = True) -> bool:
        """Prompt user for yes/no response"""
        default_text = "Y/n" if default else "y/N"
        response = input(f"{question} [{default_text}]: ").upper().strip()
        
        if response == "":
            return default
        elif response in ["Y", "YES"]:
            return True
        elif response in ["N", "NO"]:
            return False
        else:
            print("Please answer yes or no.")
            return self.prompt_yes_no(question, default)


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Movie Import Helper - Add external movies to pipeline")
    parser.add_argument("--scan", help="Directory to scan for movies to import")
    parser.add_argument("--workspace", help="Pipeline workspace directory", default=".")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without making changes")
    parser.add_argument("--copy", action="store_true", help="Copy files instead of moving them")
    parser.add_argument("--auto-import", action="store_true", help="Automatically import all found movies")
    
    args = parser.parse_args()
    
    if not args.scan:
        print("Please specify a directory to scan with --scan")
        print("Example: python movie_import_helper.py --scan \"C:\\Movies\\MyCollection\" --dry-run")
        return
    
    try:
        # Initialize importer
        importer = MovieImportHelper(workspace_path=args.workspace)
        
        # Scan for movies
        candidates = importer.scan_external_directory(args.scan)
        
        if not candidates:
            print("No movies found in the specified directory.")
            return
        
        # Show preview
        importer.show_import_preview(candidates)
        
        # Import movies
        if args.auto_import or args.dry_run:
            importer.import_movies(candidates, dry_run=args.dry_run, copy_mode=args.copy)
        else:
            if importer.prompt_yes_no(f"Import {len(candidates)} movies into pipeline?", default=True):
                importer.import_movies(candidates, dry_run=False, copy_mode=args.copy)
            else:
                print("Import cancelled.")
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"Error: {e}")


if __name__ == "__main__":
    main()

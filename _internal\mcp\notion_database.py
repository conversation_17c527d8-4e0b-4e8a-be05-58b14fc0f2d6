#!/usr/bin/env python3
"""
PlexMovieAutomator/src/mcp/notion_database.py

Comprehensive Notion Database Integration for Plex Movie Automator
Provides advanced Notion database operations, batch processing, and intelligent data management.
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
import re
import aiohttp
import aiofiles
from dataclasses import dataclass, field

# Notion API constants
NOTION_API_BASE_URL = "https://api.notion.com/v1"
NOTION_VERSION = "2022-06-28"

@dataclass
class NotionDatabase:
    """Represents a Notion database with its schema and operations."""
    id: str
    name: str
    description: str = ""
    properties: Dict[str, Dict] = field(default_factory=dict)
    last_synced: Optional[str] = None
    
    def get_property_schema(self, property_name: str) -> Dict:
        """Get the schema for a specific property."""
        return self.properties.get(property_name, {})
    
    def get_property_type(self, property_name: str) -> str:
        """Get the type of a specific property."""
        schema = self.get_property_schema(property_name)
        return schema.get("type", "")

@dataclass
class NotionPage:
    """Represents a Notion page with its properties and content."""
    id: str
    parent_database_id: str
    properties: Dict[str, Any] = field(default_factory=dict)
    content: List[Dict] = field(default_factory=list)
    url: str = ""
    created_time: str = ""
    last_edited_time: str = ""
    
    def get_property(self, property_name: str) -> Any:
        """Get a property value."""
        return self.properties.get(property_name)
    
    def set_property(self, property_name: str, value: Any) -> None:
        """Set a property value."""
        self.properties[property_name] = value

class NotionDatabaseMCP:
    """
    MCP service for comprehensive Notion database integration.
    Provides advanced database operations, batch processing, and intelligent data management.
    """
    
    def __init__(self, config, logger: logging.Logger, mcp_manager=None):
        self.config = config
        self.logger = logger
        self.mcp_manager = mcp_manager

        # Handle both dict and MCPServerConfig objects
        if hasattr(config, 'api_key'):
            self.api_key = config.api_key
            self.db_config = config.additional_config
        else:
            self.api_key = config.get("api_key", "")
            self.db_config = config.get("additional_config", {})

        self.databases: Dict[str, NotionDatabase] = {}
        self.cache_dir = Path("cache/notion")
        self.session = None
        self.rate_limit_remaining = 100
        self.rate_limit_reset_at = 0
        self.batch_queue = []
        self.batch_size = 10
        self.batch_interval = 1.0  # seconds
        self.last_batch_time = 0
        self.initialized = False

        # Database IDs from config
        self.movies_db_id = self.db_config.get("movies_database_id", "")
        self.history_db_id = self.db_config.get("history_database_id", "")
        self.stages_db_id = self.db_config.get("stages_database_id", "")
        self.preferences_db_id = self.db_config.get("preferences_database_id", "")
        self.analytics_db_id = self.db_config.get("analytics_database_id", "")
        self.errors_db_id = self.db_config.get("errors_database_id", "")

        # Auto-sync settings
        self.auto_sync = self.db_config.get("auto_sync", True)
        self.sync_interval = self.db_config.get("sync_interval", 300)  # seconds
        self.last_sync_time = 0
        
    async def initialize(self) -> bool:
        """Initialize the Notion Database MCP service."""
        try:
            self.logger.info("Initializing Notion Database MCP service...")
            
            if not self.api_key:
                self.logger.error("Notion API key not provided")
                return False
            
            # Create cache directory
            self.cache_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize HTTP session
            self.session = aiohttp.ClientSession(headers={
                "Authorization": f"Bearer {self.api_key}",
                "Notion-Version": NOTION_VERSION,
                "Content-Type": "application/json"
            })
            
            # Initialize databases
            if self.movies_db_id:
                await self._initialize_database("movies", self.movies_db_id)
            if self.history_db_id:
                await self._initialize_database("history", self.history_db_id)
            if self.stages_db_id:
                await self._initialize_database("stages", self.stages_db_id)
            if self.preferences_db_id:
                await self._initialize_database("preferences", self.preferences_db_id)
            if self.analytics_db_id:
                await self._initialize_database("analytics", self.analytics_db_id)
            if self.errors_db_id:
                await self._initialize_database("errors", self.errors_db_id)
            
            # Start batch processing task
            asyncio.create_task(self._process_batch_queue())
            
            # Start auto-sync task if enabled
            if self.auto_sync:
                asyncio.create_task(self._auto_sync_task())
            
            self.initialized = True
            self.logger.info("Notion Database MCP service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Notion Database MCP: {e}")
            return False
    
    async def _initialize_database(self, db_name: str, db_id: str) -> bool:
        """Initialize a specific database."""
        try:
            # Retrieve database schema
            database = await self._retrieve_database(db_id)
            if database:
                self.databases[db_name] = database
                self.logger.info(f"Initialized Notion database: {db_name} ({db_id})")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to initialize database {db_name}: {e}")
            return False
    
    async def _retrieve_database(self, database_id: str) -> Optional[NotionDatabase]:
        """Retrieve database schema from Notion API."""
        try:
            response = await self._make_api_request(
                method="GET",
                endpoint=f"/databases/{database_id}"
            )
            
            if response:
                properties = response.get("properties", {})
                return NotionDatabase(
                    id=database_id,
                    name=response.get("title", [{}])[0].get("plain_text", "Unnamed Database"),
                    description=response.get("description", ""),
                    properties=properties,
                    last_synced=datetime.now(timezone.utc).isoformat()
                )
            return None
        except Exception as e:
            self.logger.error(f"Failed to retrieve database {database_id}: {e}")
            return None
    
    async def _make_api_request(self, method: str, endpoint: str, data: Dict = None) -> Optional[Dict]:
        """Make a request to the Notion API with rate limiting and error handling."""
        if not self.session:
            self.logger.error("HTTP session not initialized")
            return None
        
        # Check rate limits
        if self.rate_limit_remaining <= 5:
            now = time.time()
            if now < self.rate_limit_reset_at:
                wait_time = self.rate_limit_reset_at - now + 0.5  # Add buffer
                self.logger.warning(f"Rate limit almost reached. Waiting {wait_time:.2f}s")
                await asyncio.sleep(wait_time)
        
        url = f"{NOTION_API_BASE_URL}{endpoint}"
        
        try:
            async with getattr(self.session, method.lower())(url, json=data) as response:
                # Update rate limit info
                self.rate_limit_remaining = int(response.headers.get("X-RateLimit-Remaining", "100"))
                reset_at = response.headers.get("X-RateLimit-Reset-At")
                if reset_at:
                    self.rate_limit_reset_at = time.mktime(datetime.fromisoformat(reset_at).timetuple())
                
                if response.status == 429:
                    retry_after = int(response.headers.get("Retry-After", "5"))
                    self.logger.warning(f"Rate limit exceeded. Retrying after {retry_after}s")
                    await asyncio.sleep(retry_after)
                    return await self._make_api_request(method, endpoint, data)
                
                if response.status >= 400:
                    error_data = await response.json()
                    self.logger.error(f"Notion API error: {response.status} - {error_data}")
                    return None
                
                return await response.json()
                
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP error during Notion API request: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during Notion API request: {e}")
            return None
    
    async def create_movie_entry(self, movie_data: Dict[str, Any]) -> Optional[str]:
        """Create a movie entry in the Movies database."""
        if not self.initialized or "movies" not in self.databases:
            self.logger.error("Movies database not initialized")
            return None
        
        try:
            # Prepare properties according to database schema
            properties = self._prepare_movie_properties(movie_data)
            
            # Create page in Notion
            response = await self._make_api_request(
                method="POST",
                endpoint="/pages",
                data={
                    "parent": {"database_id": self.movies_db_id},
                    "properties": properties
                }
            )
            
            if response and "id" in response:
                page_id = response["id"]
                self.logger.info(f"Created Notion entry for movie: {movie_data.get('title', 'Unknown')} (ID: {page_id})")
                
                # Create initial processing history entry
                await self.add_processing_history(
                    movie_id=movie_data.get("unique_id", ""),
                    notion_page_id=page_id,
                    stage="01_intake",
                    operation="movie_creation",
                    status="success",
                    message=f"Movie added to database: {movie_data.get('title', 'Unknown')}"
                )
                
                return page_id
            else:
                self.logger.error(f"Failed to create Notion entry for movie: {movie_data.get('title', 'Unknown')}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating movie entry: {e}")
            return None
    
    def _prepare_movie_properties(self, movie_data: Dict[str, Any]) -> Dict:
        """Prepare movie properties according to Notion database schema."""
        properties = {}
        db = self.databases.get("movies")
        
        if not db:
            return properties
        
        # Map movie data to Notion properties based on property types
        for prop_name, prop_schema in db.properties.items():
            prop_type = prop_schema.get("type", "")
            
            # Skip if property not in movie data
            if prop_name.lower() not in {k.lower() for k in movie_data.keys()}:
                continue
            
            # Find the actual key in movie_data (case-insensitive)
            data_key = next((k for k in movie_data.keys() if k.lower() == prop_name.lower()), None)
            if not data_key:
                continue
            
            value = movie_data[data_key]
            
            # Format property based on type
            if prop_type == "title" and prop_name.lower() == "title":
                properties[prop_name] = {"title": [{"text": {"content": str(value)}}]}
            
            elif prop_type == "rich_text":
                properties[prop_name] = {"rich_text": [{"text": {"content": str(value)}}]}
            
            elif prop_type == "number" and value is not None:
                try:
                    properties[prop_name] = {"number": float(value)}
                except (ValueError, TypeError):
                    pass
            
            elif prop_type == "select" and value:
                properties[prop_name] = {"select": {"name": str(value)}}
            
            elif prop_type == "multi_select" and value:
                if isinstance(value, list):
                    properties[prop_name] = {"multi_select": [{"name": str(item)} for item in value]}
                else:
                    properties[prop_name] = {"multi_select": [{"name": str(value)}]}
            
            elif prop_type == "date" and value:
                # Handle both date strings and datetime objects
                if isinstance(value, str):
                    date_str = value
                else:
                    date_str = value.isoformat() if hasattr(value, 'isoformat') else str(value)
                properties[prop_name] = {"date": {"start": date_str}}
            
            elif prop_type == "checkbox" and value is not None:
                properties[prop_name] = {"checkbox": bool(value)}
            
            elif prop_type == "url" and value:
                properties[prop_name] = {"url": str(value)}
        
        return properties
    
    async def update_movie_status(self, movie_id: str, status: str, additional_data: Dict = None) -> bool:
        """Update a movie's status and additional properties in Notion."""
        # Add to batch queue for efficient processing
        self.batch_queue.append({
            "type": "update_movie",
            "movie_id": movie_id,
            "status": status,
            "additional_data": additional_data or {},
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
        
        # Process immediately if batch is full
        if len(self.batch_queue) >= self.batch_size:
            await self._process_batch_queue(force=True)
        
        return True
    
    async def add_processing_history(self, movie_id: str, notion_page_id: str, 
                                    stage: str, operation: str, status: str, 
                                    message: str, error_details: str = None,
                                    duration: float = None, task_id: str = None) -> Optional[str]:
        """Add a processing history entry for a movie."""
        if not self.initialized or "history" not in self.databases:
            self.logger.error("History database not initialized")
            return None
        
        try:
            # Prepare properties
            properties = {
                "Movie": {"relation": [{"id": notion_page_id}]},
                "Stage": {"select": {"name": stage}},
                "Operation": {"rich_text": [{"text": {"content": operation}}]},
                "Status": {"select": {"name": status}},
                "Timestamp": {"date": {"start": datetime.now(timezone.utc).isoformat()}},
                "Message": {"rich_text": [{"text": {"content": message}}]}
            }
            
            if error_details:
                properties["Error Details"] = {"rich_text": [{"text": {"content": error_details}}]}
            
            if duration is not None:
                properties["Duration"] = {"number": duration}
            
            if task_id:
                properties["Task ID"] = {"rich_text": [{"text": {"content": task_id}}]}
            
            # Create page in Notion
            response = await self._make_api_request(
                method="POST",
                endpoint="/pages",
                data={
                    "parent": {"database_id": self.history_db_id},
                    "properties": properties
                }
            )
            
            if response and "id" in response:
                history_id = response["id"]
                self.logger.debug(f"Added processing history entry: {operation} for movie {movie_id}")
                return history_id
            else:
                self.logger.error(f"Failed to add processing history entry for movie {movie_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error adding processing history: {e}")
            return None
    
    async def _process_batch_queue(self, force: bool = False) -> None:
        """Process the batch queue of operations."""
        while True:
            now = time.time()
            if (force or len(self.batch_queue) >= self.batch_size or 
                (self.batch_queue and now - self.last_batch_time >= self.batch_interval)):
                
                # Process current batch
                batch = self.batch_queue[:self.batch_size]
                self.batch_queue = self.batch_queue[self.batch_size:]
                self.last_batch_time = now
                
                if not batch:
                    if force:
                        break
                    await asyncio.sleep(self.batch_interval)
                    continue
                
                try:
                    # Group operations by type for efficient processing
                    movie_updates = [op for op in batch if op["type"] == "update_movie"]
                    
                    # Process movie updates
                    if movie_updates:
                        await self._process_movie_updates(movie_updates)
                    
                    self.logger.debug(f"Processed batch of {len(batch)} operations")
                    
                except Exception as e:
                    self.logger.error(f"Error processing batch: {e}")
            
            if force:
                break
                
            await asyncio.sleep(self.batch_interval)
    
    async def _process_movie_updates(self, updates: List[Dict]) -> None:
        """Process a batch of movie updates."""
        # Group updates by movie_id to avoid multiple updates to the same movie
        grouped_updates = {}
        for update in updates:
            movie_id = update["movie_id"]
            if movie_id not in grouped_updates:
                grouped_updates[movie_id] = update
            else:
                # Merge additional data and keep the latest status
                grouped_updates[movie_id]["additional_data"].update(update["additional_data"])
                grouped_updates[movie_id]["status"] = update["status"]
                grouped_updates[movie_id]["timestamp"] = update["timestamp"]
        
        # Process each movie update
        for movie_id, update in grouped_updates.items():
            await self._update_movie_in_notion(
                movie_id=movie_id,
                status=update["status"],
                additional_data=update["additional_data"]
            )
    
    async def _update_movie_in_notion(self, movie_id: str, status: str, additional_data: Dict) -> bool:
        """Update a movie in Notion."""
        try:
            # Find the movie in Notion by unique_id
            notion_page_id = await self._find_movie_by_unique_id(movie_id)
            if not notion_page_id:
                self.logger.error(f"Movie not found in Notion: {movie_id}")
                return False
            
            # Prepare properties
            properties = {
                "Status": {"select": {"name": status}}
            }
            
            # Add additional properties
            for key, value in additional_data.items():
                prop_type = self._get_property_type("movies", key)
                if prop_type:
                    properties[key] = self._format_property_value(prop_type, value)
            
            # Update page in Notion
            response = await self._make_api_request(
                method="PATCH",
                endpoint=f"/pages/{notion_page_id}",
                data={"properties": properties}
            )
            
            if response and "id" in response:
                self.logger.debug(f"Updated movie in Notion: {movie_id} (Status: {status})")
                return True
            else:
                self.logger.error(f"Failed to update movie in Notion: {movie_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error updating movie in Notion: {e}")
            return False
    
    async def _find_movie_by_unique_id(self, movie_id: str) -> Optional[str]:
        """Find a movie in Notion by its unique_id."""
        try:
            response = await self._make_api_request(
                method="POST",
                endpoint="/databases/" + self.movies_db_id + "/query",
                data={
                    "filter": {
                        "property": "Unique ID",
                        "rich_text": {
                            "equals": movie_id
                        }
                    }
                }
            )
            
            if response and response.get("results"):
                return response["results"][0]["id"]
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding movie by unique_id: {e}")
            return None
    
    def _get_property_type(self, db_name: str, property_name: str) -> Optional[str]:
        """Get the type of a property in a database."""
        db = self.databases.get(db_name)
        if not db:
            return None
        
        # Case-insensitive property name lookup
        for prop_name, prop_schema in db.properties.items():
            if prop_name.lower() == property_name.lower():
                return prop_schema.get("type")
        
        return None
    
    def _format_property_value(self, prop_type: str, value: Any) -> Dict:
        """Format a property value according to its type."""
        if prop_type == "title":
            return {"title": [{"text": {"content": str(value)}}]}
        
        elif prop_type == "rich_text":
            return {"rich_text": [{"text": {"content": str(value)}}]}
        
        elif prop_type == "number" and value is not None:
            try:
                return {"number": float(value)}
            except (ValueError, TypeError):
                return {}
        
        elif prop_type == "select" and value:
            return {"select": {"name": str(value)}}
        
        elif prop_type == "multi_select" and value:
            if isinstance(value, list):
                return {"multi_select": [{"name": str(item)} for item in value]}
            else:
                return {"multi_select": [{"name": str(value)}]}
        
        elif prop_type == "date" and value:
            if isinstance(value, str):
                date_str = value
            else:
                date_str = value.isoformat() if hasattr(value, 'isoformat') else str(value)
            return {"date": {"start": date_str}}
        
        elif prop_type == "checkbox" and value is not None:
            return {"checkbox": bool(value)}
        
        elif prop_type == "url" and value:
            return {"url": str(value)}
        
        elif prop_type == "relation" and value:
            if isinstance(value, list):
                return {"relation": [{"id": str(item)} for item in value]}
            else:
                return {"relation": [{"id": str(value)}]}
        
        return {}
    
    async def _auto_sync_task(self) -> None:
        """Periodically sync data with Notion."""
        while True:
            try:
                now = time.time()
                if now - self.last_sync_time >= self.sync_interval:
                    self.logger.info("Starting auto-sync with Notion...")
                    await self._sync_databases()
                    self.last_sync_time = now
                    self.logger.info("Auto-sync completed")
            except Exception as e:
                self.logger.error(f"Error during auto-sync: {e}")
            
            await asyncio.sleep(60)  # Check every minute
    
    async def _sync_databases(self) -> None:
        """Sync all databases with Notion."""
        for db_name, db_id in [
            ("movies", self.movies_db_id),
            ("history", self.history_db_id),
            ("stages", self.stages_db_id),
            ("preferences", self.preferences_db_id),
            ("analytics", self.analytics_db_id),
            ("errors", self.errors_db_id)
        ]:
            if db_id:
                await self._initialize_database(db_name, db_id)
    
    async def health_check(self) -> bool:
        """Perform health check on the service."""
        if not self.initialized:
            return False
        
        try:
            # Test API connection
            response = await self._make_api_request(
                method="GET",
                endpoint="/users/me"
            )
            
            return response is not None
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup the service."""
        self.logger.info("Cleaning up Notion Database MCP service...")
        
        # Process any remaining batch operations
        await self._process_batch_queue(force=True)
        
        # Close HTTP session
        if self.session:
            await self.session.close()
            self.session = None
        
        self.initialized = False

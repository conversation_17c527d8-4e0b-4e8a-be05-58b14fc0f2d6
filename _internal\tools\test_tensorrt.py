#!/usr/bin/env python3
"""
Quick TensorRT Test for Step 2 Environment Setup
"""

import os
import sys

def test_tensorrt():
    """Test TensorRT import with proper PATH setup"""
    print("🔧 TensorRT Import Test")
    print("=" * 40)
    
    # Add TensorRT DLL path to environment
    tensorrt_lib_path = r"C:\Users\<USER>\Videos\PlexMovieAutomator\_internal\tools\TensorRT\lib"
    
    if os.path.exists(tensorrt_lib_path):
        print(f"✅ TensorRT lib directory found: {tensorrt_lib_path}")
        
        # Add to PATH
        current_path = os.environ.get('PATH', '')
        if tensorrt_lib_path not in current_path:
            os.environ['PATH'] = f"{tensorrt_lib_path};{current_path}"
            print(f"✅ Added TensorRT lib to PATH")
        
        try:
            import tensorrt as trt
            print(f"✅ TensorRT imported successfully!")
            print(f"   Version: {trt.__version__}")
            
            # Test basic TensorRT functionality
            logger = trt.Logger(trt.Logger.WARNING)
            builder = trt.Builder(logger)
            print(f"✅ TensorRT Builder created successfully")
            
            # Test network creation
            network = builder.create_network()
            print(f"✅ TensorRT Network created successfully")
            
            # Check available optimizations
            config = builder.create_builder_config()
            print(f"✅ TensorRT Builder Config created")
            print(f"   Platform has fast FP16: {builder.platform_has_fast_fp16}")
            print(f"   Platform has fast INT8: {builder.platform_has_fast_int8}")
            
            return True
            
        except FileNotFoundError as e:
            print(f"❌ TensorRT DLL not found: {e}")
            return False
        except ImportError as e:
            print(f"❌ TensorRT import failed: {e}")
            return False
        except Exception as e:
            print(f"❌ TensorRT test failed: {e}")
            return False
    else:
        print(f"❌ TensorRT lib directory not found: {tensorrt_lib_path}")
        return False

if __name__ == "__main__":
    success = test_tensorrt()
    if success:
        print("\n🎯 TensorRT is ready for NGC OCR models!")
    else:
        print("\n⚠️  TensorRT needs attention, but ONNX Runtime can be used instead")

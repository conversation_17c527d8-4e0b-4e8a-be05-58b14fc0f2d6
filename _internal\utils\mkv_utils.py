#!/usr/bin/env python3
"""
MKV utilities for pipeline processing
"""

import subprocess
import json
import re
from pathlib import Path

def get_mkv_info(file_path, settings=None):
    """Get MKV file information using mkvmerge"""
    try:
        # Try to get mkvmerge path from settings
        mkvmerge_path = 'mkvmerge'
        if settings and 'Executables' in settings:
            mkvmerge_path = settings['Executables'].get('mkvmerge_path', 'mkvmerge')
        
        cmd = [mkvmerge_path, '-J', str(file_path)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            return json.loads(result.stdout)
        else:
            return None
    except Exception as e:
        print(f"Error getting MKV info: {e}")
        return None

def clean_mkv_title_and_tags(file_path, settings=None):
    """Clean MKV title and tags"""
    try:
        # Get mkvpropedit path from settings
        mkvpropedit_path = 'mkvpropedit'
        if settings and 'Executables' in settings:
            mkvpropedit_path = settings['Executables'].get('mkvpropedit_path', 'mkvpropedit')

        cmd = [
            mkvpropedit_path, str(file_path),
            '--edit', 'info',
            '--delete', 'title',
            '--delete', 'segment-uid'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Error cleaning MKV: {e}")
        return False

def clean_attachments_iterative(file_path, settings):
    """Clean attachments from MKV file"""
    try:
        # Get mkvpropedit path from settings
        mkvpropedit_path = 'mkvpropedit'
        if settings and 'Executables' in settings:
            mkvpropedit_path = settings['Executables'].get('mkvpropedit_path', 'mkvpropedit')

        # First, check if there are any attachments
        mkv_info = get_mkv_info(file_path, settings)
        if not mkv_info or 'attachments' not in mkv_info:
            return True  # No attachments to clean

        # Delete all attachments iteratively
        for attachment in mkv_info.get('attachments', []):
            cmd = [mkvpropedit_path, str(file_path), '--delete-attachment', str(attachment['id'])]
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                print(f"Warning: Failed to delete attachment {attachment['id']}")

        return True
    except Exception as e:
        print(f"Error cleaning attachments: {e}")
        return False

def set_track_properties_by_uid(file_path, uid, language=None, name=None, settings=None):
    """Set track properties by UID"""
    try:
        cmd = ['mkvpropedit', str(file_path)]
        if uid:
            cmd.extend(['--edit', f'track:={uid}'])
            if language:
                cmd.extend(['--set', f'language={language}'])
            if name:
                cmd.extend(['--set', f'name={name}'])
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0
    except Exception as e:
        print(f"Error setting track properties: {e}")
        return False

def select_audio_track(mkv_info, preferred_lang="eng", original_lang_from_master=None):
    """Select best audio track with quality ranking and commentary detection"""
    if not mkv_info or 'tracks' not in mkv_info:
        return None

    audio_tracks = [track for track in mkv_info['tracks'] if track['type'] == 'audio']
    if not audio_tracks:
        return None

    # Audio quality ranking (higher = better)
    AUDIO_QUALITY_RANKING = {
        'A_TRUEHD': 100,      # TrueHD Atmos (highest quality)
        'A_DTS': 90,          # DTS-HD MA
        'A_FLAC': 80,         # FLAC
        'A_EAC3': 70,         # E-AC3 (Dolby Digital Plus)
        'A_AC3': 60,          # AC3 (Dolby Digital)
        'A_AAC': 50,          # AAC
        'A_MPEG': 40,         # MPEG Audio
    }

    # Commentary detection keywords
    COMMENTARY_KEYWORDS = ['commentary', 'director', 'cast', 'crew', 'making of', 'behind the scenes']

    def is_commentary_track(track):
        """Check if track is a commentary track"""
        track_name = track.get('properties', {}).get('track_name', '').lower()
        return any(keyword in track_name for keyword in COMMENTARY_KEYWORDS)

    def get_audio_quality_score(track):
        """Get quality score for audio track"""
        codec = track.get('properties', {}).get('codec_id', '')
        return AUDIO_QUALITY_RANKING.get(codec, 30)  # Default score for unknown codecs

    # Filter out commentary tracks
    non_commentary_tracks = [track for track in audio_tracks if not is_commentary_track(track)]
    if not non_commentary_tracks:
        print("Warning: All audio tracks appear to be commentary, using all tracks")
        non_commentary_tracks = audio_tracks

    # Filter by preferred language first
    preferred_lang_tracks = [track for track in non_commentary_tracks
                           if track.get('properties', {}).get('language') == preferred_lang]

    if preferred_lang_tracks:
        # Sort by quality score (highest first)
        preferred_lang_tracks.sort(key=get_audio_quality_score, reverse=True)
        best_track = preferred_lang_tracks[0]
        print(f"Selected {preferred_lang.upper()} audio track: {best_track.get('properties', {}).get('codec_id', 'unknown')} (quality score: {get_audio_quality_score(best_track)})")
        return best_track

    # Fallback: select highest quality track regardless of language
    non_commentary_tracks.sort(key=get_audio_quality_score, reverse=True)
    best_track = non_commentary_tracks[0]
    track_lang = best_track.get('properties', {}).get('language', 'unknown')
    print(f"No {preferred_lang.upper()} audio found, selected {track_lang.upper()} track: {best_track.get('properties', {}).get('codec_id', 'unknown')}")
    return best_track

def get_audio_track_name(audio_track):
    """Get descriptive name for audio track using proper naming conventions"""
    if not audio_track:
        return "Audio"

    props = audio_track.get('properties', {})
    codec = props.get('codec_id', '')
    channels = props.get('audio_channels', 2)

    # Proper audio naming conventions
    if codec == 'A_TRUEHD':
        if channels >= 8:
            return "TrueHD Atmos"
        else:
            return f"TrueHD {channels}ch"
    elif codec == 'A_DTS':
        if channels >= 6:
            return "DTS-HD MA"
        else:
            return f"DTS {channels}ch"
    elif codec == 'A_EAC3':
        return f"E-AC3 {channels}ch"
    elif codec == 'A_AC3':
        return f"AC3 {channels}ch"
    elif codec == 'A_AAC':
        if channels == 2:
            return "Stereo"
        else:
            return f"AAC {channels}ch"
    elif codec == 'A_FLAC':
        return f"FLAC {channels}ch"
    else:
        # Fallback for unknown codecs
        codec_clean = codec.replace('A_', '')
        return f"{codec_clean} {channels}ch"

def select_subtitle_tracks(mkv_info, preferred_lang="eng", processing_file_path=None, settings=None):
    """
    ENHANCED: Smart English subtitle selection with quality ranking.
    
    Preference Order (User Requirements):
    1. English, NON-SDH (regular subtitles preferred over SDH)
    2. English, SDH (only if no regular English available)
    3. Forced/Foreign parts only
    4. Avoids commentary tracks completely
    
    Detects ANY subtitle format, not just SRT/PGS.
    """
    if not mkv_info or 'tracks' not in mkv_info:
        return []

    subtitle_tracks = [track for track in mkv_info['tracks'] if track['type'] == 'subtitles']
    if not subtitle_tracks:
        return []

    print(f"Analyzing {len(subtitle_tracks)} total subtitle tracks for smart selection...")

    # Analyze each English subtitle track
    english_candidates = []
    for track in subtitle_tracks:
        track_props = track.get('properties', {})
        track_lang = track_props.get('language', 'und')
        track_name = track_props.get('track_name', '').lower()
        codec = track_props.get('codec_id', '').upper()

        # Check if track is English
        is_english = (track_lang == preferred_lang or 
                     'english' in track_name or 
                     track_name.startswith('eng'))

        if is_english:
            # Analyze track characteristics
            is_commentary = any(word in track_name for word in ['commentary', 'comment', 'director', 'cast'])
            is_sdh = any(word in track_name for word in ['sdh', 'deaf', 'hard of hearing', 'cc', 'closed caption'])
            is_forced = any(word in track_name for word in ['forced', 'foreign', 'parts only', 'signs'])
            is_full = not (is_forced or is_commentary)

            # Skip commentary tracks entirely
            if is_commentary:
                print(f"  Skipping commentary track: {track_name}")
                continue

            # Quality scoring (higher = better, per user preferences)
            quality_score = 0
            
            # HIGHEST PRIORITY: Regular English (non-SDH)
            if is_full and not is_sdh:
                quality_score = 100
                subtitle_type = "Regular English"
            # SECOND PRIORITY: SDH (only if no regular available)
            elif is_full and is_sdh:
                quality_score = 80
                subtitle_type = "English SDH"
            # THIRD PRIORITY: Forced/Foreign only
            elif is_forced:
                quality_score = 60
                subtitle_type = "Forced/Foreign"
            else:
                quality_score = 40
                subtitle_type = "Other English"

            english_candidates.append({
                'track': track,
                'quality_score': quality_score,
                'subtitle_type': subtitle_type,
                'track_name': track_name,
                'codec': codec,
                'is_sdh': is_sdh,
                'is_forced': is_forced
            })

    if not english_candidates:
        print(f"No English subtitles found, taking first 2 tracks out of {len(subtitle_tracks)} total")
        return subtitle_tracks[:2]

    # Sort by quality score (highest first) - User prefers NON-SDH
    english_candidates.sort(key=lambda x: x['quality_score'], reverse=True)

    # Select the best subtitles based on user preferences
    selected_tracks = []
    
    # Always include the highest quality regular English
    best_regular = next((c for c in english_candidates if c['quality_score'] >= 80), None)
    if best_regular:
        selected_tracks.append(best_regular['track'])
        print(f"  ✅ Selected BEST: {best_regular['subtitle_type']} - '{best_regular['track_name']}' (Score: {best_regular['quality_score']})")

    # Include forced/foreign if available (many users want both)
    best_forced = next((c for c in english_candidates if c['is_forced']), None)
    if best_forced and best_forced not in [s for s in selected_tracks]:
        selected_tracks.append(best_forced['track'])
        print(f"  ✅ Selected FORCED: {best_forced['subtitle_type']} - '{best_forced['track_name']}' (Score: {best_forced['quality_score']})")

    # If we only found SDH, include it (better than nothing)
    if not selected_tracks:
        sdh_track = next((c for c in english_candidates if c['is_sdh']), None)
        if sdh_track:
            selected_tracks.append(sdh_track['track'])
            print(f"  ✅ Selected SDH (only option): {sdh_track['subtitle_type']} - '{sdh_track['track_name']}'")

    # Fallback to any English if nothing else worked
    if not selected_tracks and english_candidates:
        fallback = english_candidates[0]
        selected_tracks.append(fallback['track'])
        print(f"  ✅ Selected FALLBACK: {fallback['subtitle_type']} - '{fallback['track_name']}'")

    print(f"Smart selection complete: {len(selected_tracks)} English subtitle tracks selected (avoiding {len(subtitle_tracks) - len(english_candidates)} non-English tracks)")
    return selected_tracks

def create_clean_video_audio_mkv(input_path, output_path, video_track=None, audio_track=None, settings=None):
    """Create clean video/audio only MKV"""
    try:
        # Get mkvmerge path from settings
        mkvmerge_path = 'mkvmerge'
        if settings and 'Executables' in settings:
            mkvmerge_path = settings['Executables'].get('mkvmerge_path', 'mkvmerge')

        cmd = [mkvmerge_path, '-o', str(output_path)]

        if video_track is not None:
            cmd.extend(['--video-tracks', str(video_track)])
        if audio_track is not None:
            cmd.extend(['--audio-tracks', str(audio_track)])

        cmd.extend(['--no-subtitles', '--no-attachments', str(input_path)])

        print(f"Running MKV merge command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"Successfully created clean video/audio MKV: {output_path}")
            return True
        else:
            print(f"MKV merge failed with return code {result.returncode}")
            print(f"Error output: {result.stderr}")
            return False

    except Exception as e:
        print(f"Error creating clean MKV: {e}")
        return False

def extract_subtitle_tracks_individually(input_path, movie_stem, output_dir, subtitle_tracks, settings=None):
    """
    ENHANCED: Extract subtitle tracks with comprehensive format detection.
    
    Supports ALL subtitle formats, not just SRT/PGS:
    - Text-based: SRT, ASS, SSA, WebVTT, etc.
    - Image-based: PGS/SUP, VobSub, DVD subtitles, etc.
    - Any other format that MKV can contain
    """
    try:
        # Get mkvextract path from settings
        mkvextract_path = 'mkvextract'
        if settings and 'Executables' in settings:
            mkvextract_path = settings['Executables'].get('mkvextract_path', 'mkvextract')

        extracted_files_map = {}

        for track_info in subtitle_tracks:
            # Handle both track ID and track info dict
            if isinstance(track_info, dict):
                track_id = track_info.get('id')
                track_props = track_info.get('properties', {})
                language = track_props.get('language', 'und')
                codec = track_props.get('codec_id', 'unknown')
                track_name = track_props.get('track_name', '')
            else:
                track_id = track_info
                language = 'und'
                codec = 'unknown'
                track_name = ''

            # COMPREHENSIVE FORMAT DETECTION - Handles ANY subtitle format
            codec_upper = codec.upper()
            
            # Image-based subtitle formats
            if any(fmt in codec_upper for fmt in ['PGS', 'SUP', 'HDMV']):
                ext = 'sup'
            elif any(fmt in codec_upper for fmt in ['VOBSUB', 'VOB']):
                ext = 'sub'
            elif 'DVD' in codec_upper:
                ext = 'sub'
            # Text-based subtitle formats  
            elif any(fmt in codec_upper for fmt in ['SRT', 'SUBRIP']):
                ext = 'srt'
            elif any(fmt in codec_upper for fmt in ['ASS', 'SSA']):
                ext = 'ass'
            elif any(fmt in codec_upper for fmt in ['WEBVTT', 'VTT']):
                ext = 'vtt'
            elif any(fmt in codec_upper for fmt in ['TTML', 'DFXP']):
                ext = 'ttml'
            elif 'USF' in codec_upper:
                ext = 'usf'
            # Fallback for unknown formats - let MKVExtract determine
            else:
                # Use generic extension and let mkvextract handle it
                ext = 'sub'
                print(f"  Unknown subtitle codec '{codec}', using .sub extension")

            # SMART NAMING based on user preferences (NON-SDH preferred)
            if language == 'eng' or 'english' in track_name.lower():
                # Check for specific subtitle types
                track_name_lower = track_name.lower()
                
                if any(word in track_name_lower for word in ['forced', 'foreign', 'parts only', 'signs']):
                    subtitle_name = "English.Forced"
                elif any(word in track_name_lower for word in ['sdh', 'deaf', 'hard of hearing', 'cc', 'closed caption']):
                    subtitle_name = "English.SDH"
                elif any(word in track_name_lower for word in ['commentary', 'comment']):
                    subtitle_name = "English.Commentary"
                else:
                    # Regular English subtitle (user's preferred type)
                    subtitle_name = "English"
            else:
                # Non-English subtitle
                subtitle_name = language if language != 'und' else 'Unknown'

            output_file = Path(output_dir) / f"{movie_stem}.{subtitle_name}.{ext}"

            cmd = [
                mkvextract_path, str(input_path), 'tracks',
                f"{track_id}:{output_file}"
            ]

            print(f"Extracting {codec} subtitle track {track_id} ({subtitle_name}): {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)

            if result.returncode == 0:
                extracted_files_map[track_id] = str(output_file)
                print(f"  ✅ Successfully extracted: {output_file}")
                
                # Log the format for user awareness
                format_type = "Image-based" if ext in ['sup', 'sub'] else "Text-based"
                print(f"     Format: {codec} ({format_type})")
            else:
                print(f"  ❌ Failed to extract subtitle track {track_id}: {result.stderr}")

        print(f"Subtitle extraction complete: {len(extracted_files_map)}/{len(subtitle_tracks)} tracks extracted successfully")
        return True, extracted_files_map
    except Exception as e:
        print(f"Error extracting subtitles: {e}")
        return False, {}

def parse_master_list(master_list_path):
    """Parse master list for original language hints"""
    try:
        if not Path(master_list_path).exists():
            return {}
        
        with open(master_list_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        language_map = {}
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                parts = line.split('|')
                if len(parts) >= 2:
                    title = parts[0].strip()
                    language = parts[1].strip()
                    language_map[title] = language
        
        return language_map
    except Exception as e:
        print(f"Error parsing master list: {e}")
        return {}
